"""
脚本生成系统数据库管理器
"""
import logging
from typing import List, Optional, Dict, Any
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime

from backend.config import SystemConfig
from backend.models.script_models import (
    Base, BusinessModule, PackageComponent, TestCase, 
    GeneratedScript, ScriptVersion, AgentSession, AgentMessage
)
from backend.excel_reader import TestCase as ExcelTestCase, PackageComponent as ExcelPackageComponent

logger = logging.getLogger(__name__)


class ScriptDatabaseManager:
    """脚本生成系统数据库管理器"""
    
    def __init__(self, config: Optional[SystemConfig] = None):
        """
        初始化数据库管理器
        
        Args:
            config: 系统配置，如果为None则使用默认配置
        """
        self.config = config or SystemConfig()
        self.engine = None
        self.SessionLocal = None
        self._initialize_database()
    
    def _initialize_database(self):
        """初始化数据库连接"""
        try:
            # 创建数据库引擎
            self.engine = create_engine(
                self.config.database.connection_url,
                echo=False,  # 设置为True可以看到SQL语句
                pool_pre_ping=True,
                pool_recycle=3600
            )
            
            # 创建会话工厂
            self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
            
            # 创建表
            Base.metadata.create_all(bind=self.engine)
            
            logger.info("脚本生成系统数据库初始化成功")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        return self.SessionLocal()
    
    def create_business_module(self, module_name: str, description: str = "") -> BusinessModule:
        """
        创建业务模块
        
        Args:
            module_name: 模块名称
            description: 模块描述
            
        Returns:
            BusinessModule: 创建的业务模块
        """
        with self.get_session() as session:
            try:
                # 检查是否已存在
                existing = session.query(BusinessModule).filter_by(module_name=module_name).first()
                if existing:
                    return existing
                
                # 创建新模块
                module = BusinessModule(
                    module_name=module_name,
                    description=description
                )
                session.add(module)
                session.commit()
                session.refresh(module)
                
                logger.info(f"创建业务模块: {module_name}")
                return module
                
            except SQLAlchemyError as e:
                session.rollback()
                logger.error(f"创建业务模块失败: {e}")
                raise
    
    def save_package_components(self, components: List[ExcelPackageComponent]) -> int:
        """
        保存包组件映射数据
        
        Args:
            components: 包组件映射列表
            
        Returns:
            int: 保存的数量
        """
        with self.get_session() as session:
            try:
                saved_count = 0
                
                for comp in components:
                    # 获取或创建业务模块
                    module = session.query(BusinessModule).filter_by(
                        module_name=comp.business_module
                    ).first()
                    
                    if not module:
                        module = BusinessModule(
                            module_name=comp.business_module,
                            description=f"从Excel导入的模块: {comp.business_module}"
                        )
                        session.add(module)
                        session.flush()  # 获取ID
                    
                    # 检查是否已存在相同的组件映射
                    existing = session.query(PackageComponent).filter_by(
                        business_module_id=module.id,
                        package_name=comp.package_name,
                        component_name=comp.component_name
                    ).first()
                    
                    if not existing:
                        # 创建新的组件映射
                        package_comp = PackageComponent(
                            business_module_id=module.id,
                            package_name=comp.package_name,
                            component_name=comp.component_name,
                            description=comp.description
                        )
                        session.add(package_comp)
                        saved_count += 1
                
                session.commit()
                logger.info(f"保存了 {saved_count} 个包组件映射")
                return saved_count
                
            except SQLAlchemyError as e:
                session.rollback()
                logger.error(f"保存包组件映射失败: {e}")
                raise
    
    def save_test_cases(self, test_cases: List[ExcelTestCase]) -> int:
        """
        保存测试用例数据
        
        Args:
            test_cases: 测试用例列表
            
        Returns:
            int: 保存的数量
        """
        with self.get_session() as session:
            try:
                saved_count = 0
                
                for case in test_cases:
                    # 获取或创建业务模块（使用Group作为业务模块）
                    module = session.query(BusinessModule).filter_by(
                        module_name=case.group_name
                    ).first()

                    if not module:
                        module = BusinessModule(
                            module_name=case.group_name,
                            description=f"从Excel导入的模块: {case.group_name}"
                        )
                        session.add(module)
                        session.flush()  # 获取ID

                    # 检查是否已存在相同的测试用例
                    existing = session.query(TestCase).filter_by(tcid=case.tcid).first()

                    if not existing:
                        # 创建新的测试用例
                        test_case = TestCase(
                            tcid=case.tcid,
                            case_name=case.case_name,
                            group_name=case.group_name,
                            sub_group=case.sub_group,
                            component=case.component,
                            case_type=case.case_type,
                            level=case.level,
                            steps=case.steps,
                            expect_result=case.expect_result,
                            automated=case.automated,
                            owner=case.owner,
                            execute_owner=case.execute_owner,
                            sort=case.sort,
                            sub_component=case.sub_component,
                            sub_function=case.sub_function,
                            second_function=case.second_function,
                            case_set_type=case.case_set_type,
                            purpose=case.purpose,
                            pre_condition=case.pre_condition,
                            keywords=case.keywords,
                            phase=case.phase,
                            test_area=case.test_area,
                            execute_time=case.execute_time,
                            standard=case.standard,
                            simples=case.simples,
                            environment=case.environment,
                            os=case.os,
                            android=case.android,
                            cpu=case.cpu,
                            interaction_component=case.interaction_component,
                            brand=case.brand,
                            sim=case.sim,
                            source=case.source,
                            match_project=case.match_project,
                            test_code_path=case.test_code_path,
                            app_version=case.app_version,
                            special_ability=case.special_ability,
                            special_type=case.special_type,
                            audit_result=case.audit_result,
                            audit_remark=case.audit_remark,
                            business_module_id=module.id
                        )
                        # 处理更新时间
                        if case.update_time and case.update_time != 'nan':
                            try:
                                test_case.update_time = datetime.strptime(case.update_time, '%Y-%m-%d %H:%M:%S')
                            except:
                                pass

                        session.add(test_case)
                        saved_count += 1
                    else:
                        # 更新现有用例
                        existing.case_name = case.case_name
                        existing.steps = case.steps
                        existing.expect_result = case.expect_result
                        existing.level = case.level
                        existing.automated = case.automated
                        existing.owner = case.owner
                        existing.execute_owner = case.execute_owner
                        existing.updated_at = datetime.now()
                
                session.commit()
                logger.info(f"保存了 {saved_count} 个测试用例")
                return saved_count
                
            except SQLAlchemyError as e:
                session.rollback()
                logger.error(f"保存测试用例失败: {e}")
                raise
    
    def get_test_case_by_id(self, tcid: str) -> Optional[TestCase]:
        """
        根据用例ID获取测试用例

        Args:
            tcid: 测试用例ID (TCID)

        Returns:
            Optional[TestCase]: 测试用例或None
        """
        with self.get_session() as session:
            return session.query(TestCase).filter_by(tcid=tcid).first()
    
    def get_package_components_by_module(self, module_name: str) -> List[PackageComponent]:
        """
        根据业务模块获取包组件映射
        
        Args:
            module_name: 业务模块名称
            
        Returns:
            List[PackageComponent]: 包组件映射列表
        """
        with self.get_session() as session:
            return session.query(PackageComponent).join(BusinessModule).filter(
                BusinessModule.module_name == module_name
            ).all()
    
    def create_agent_session(self, test_case_id: int) -> AgentSession:
        """
        创建智能体会话
        
        Args:
            test_case_id: 测试用例ID
            
        Returns:
            AgentSession: 创建的会话
        """
        with self.get_session() as session:
            try:
                # 生成会话ID
                session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{test_case_id}"
                
                agent_session = AgentSession(
                    session_id=session_id,
                    test_case_id=test_case_id,
                    status='进行中',
                    current_agent='用例信息获取智能体'
                )
                
                session.add(agent_session)
                session.commit()
                session.refresh(agent_session)
                
                logger.info(f"创建智能体会话: {session_id}")
                return agent_session
                
            except SQLAlchemyError as e:
                session.rollback()
                logger.error(f"创建智能体会话失败: {e}")
                raise
    
    def save_generated_script(self, test_case_id: int, script_name: str, 
                            script_content: str, metadata: Dict[str, Any] = None) -> GeneratedScript:
        """
        保存生成的脚本
        
        Args:
            test_case_id: 测试用例ID
            script_name: 脚本名称
            script_content: 脚本内容
            metadata: 元数据
            
        Returns:
            GeneratedScript: 保存的脚本记录
        """
        with self.get_session() as session:
            try:
                script = GeneratedScript(
                    test_case_id=test_case_id,
                    script_name=script_name,
                    script_content=script_content,
                    generation_status='已完成',
                    review_status='待审查',
                    generation_metadata=metadata or {},
                    generation_completed_at=datetime.now()
                )
                
                session.add(script)
                session.commit()
                session.refresh(script)
                
                logger.info(f"保存生成的脚本: {script_name}")
                return script
                
            except SQLAlchemyError as e:
                session.rollback()
                logger.error(f"保存生成的脚本失败: {e}")
                raise
    
    def update_script_file_path(self, script_id: int, file_path: str, file_size: int = 0):
        """
        更新脚本文件路径
        
        Args:
            script_id: 脚本ID
            file_path: 文件路径
            file_size: 文件大小
        """
        with self.get_session() as session:
            try:
                script = session.query(GeneratedScript).filter_by(id=script_id).first()
                if script:
                    script.file_path = file_path
                    script.file_size = file_size
                    script.updated_at = datetime.now()
                    session.commit()
                    logger.info(f"更新脚本文件路径: {file_path}")
                
            except SQLAlchemyError as e:
                session.rollback()
                logger.error(f"更新脚本文件路径失败: {e}")
                raise


def main():
    """测试函数"""
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建数据库管理器
    db_manager = ScriptDatabaseManager()
    
    # 测试创建业务模块
    module = db_manager.create_business_module("登录模块", "用户登录相关功能")
    print(f"创建业务模块: {module.module_name}")


if __name__ == "__main__":
    main()
