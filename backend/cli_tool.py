#!/usr/bin/env python3
"""
基于AutoGen的Android自动化脚本生成系统命令行工具
"""
import asyncio
import argparse
import sys
import json
from pathlib import Path
from typing import Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.script_generation_controller import ScriptGenerationController
from backend.excel_reader import ExcelReader
from backend.script_database_manager import ScriptDatabaseManager


class ScriptGenerationCLI:
    """脚本生成系统命令行接口"""
    
    def __init__(self):
        self.controller = ScriptGenerationController()
    
    async def init_system(self):
        """初始化系统"""
        print("🚀 正在初始化脚本生成系统...")
        
        try:
            success = await self.controller.initialize_system()
            if success:
                print("✅ 系统初始化成功")
                
                # 显示统计信息
                stats = self.controller.get_generation_statistics()
                print(f"📊 系统统计:")
                print(f"   测试用例: {stats.get('total_test_cases', 0)} 个")
                print(f"   生成脚本: {stats.get('total_generated_scripts', 0)} 个")
                print(f"   成功率: {stats.get('success_rate', 0):.1f}%")
            else:
                print("❌ 系统初始化失败")
                return False
        except Exception as e:
            print(f"❌ 初始化异常: {e}")
            return False
        
        return True
    
    async def generate_from_text(self, case_text: str, module: Optional[str] = None):
        """从文本描述生成脚本"""
        print("📝 正在从文本描述生成脚本...")
        print(f"用例描述: {case_text[:100]}...")
        
        try:
            result = await self.controller.generate_script_from_case_description(
                case_text, module
            )
            
            if result.get("success"):
                print("✅ 脚本生成成功!")
                print(f"   文件路径: {result['script_file']}")
                print(f"   脚本名称: {result['script_name']}")
                print(f"   业务模块: {result['business_module']}")
                print(f"   审查评分: {result['review_score']}")
                
                # 显示生成的文件内容预览
                self._show_file_preview(result['script_file'])
                
            else:
                print(f"❌ 脚本生成失败: {result.get('error')}")
                
        except Exception as e:
            print(f"❌ 生成异常: {e}")
    
    async def generate_from_case_id(self, case_id: str):
        """从用例ID生成脚本"""
        print(f"🔍 正在为用例 {case_id} 生成脚本...")
        
        try:
            result = await self.controller.generate_script_from_case_id(case_id)
            
            if result.get("success"):
                print("✅ 脚本生成成功!")
                print(f"   文件路径: {result['script_file']}")
                print(f"   审查评分: {result['review_score']}")
                
                self._show_file_preview(result['script_file'])
            else:
                print(f"❌ 脚本生成失败: {result.get('error')}")
                
        except Exception as e:
            print(f"❌ 生成异常: {e}")
    
    async def batch_generate(self, case_ids: Optional[list] = None, 
                           module: Optional[str] = None, limit: int = 5):
        """批量生成脚本"""
        print("📦 正在批量生成脚本...")
        
        try:
            # 如果没有指定用例ID，获取前几个用例
            if not case_ids:
                with self.controller.script_db.get_session() as session:
                    from backend.models.script_models import TestCase
                    query = session.query(TestCase)
                    if module:
                        from backend.models.script_models import BusinessModule
                        query = query.join(BusinessModule).filter(
                            BusinessModule.module_name == module
                        )
                    test_cases = query.limit(limit).all()
                    case_ids = [case.tcid for case in test_cases]
            
            if not case_ids:
                print("⚠️  没有找到可处理的测试用例")
                return
            
            print(f"准备处理 {len(case_ids)} 个用例...")
            
            result = await self.controller.batch_generate_scripts(case_ids, module)
            
            if result.get("success"):
                print("✅ 批量生成完成!")
                print(f"   总用例数: {result['total_cases']}")
                print(f"   成功数量: {result['success_count']}")
                print(f"   失败数量: {result['failure_count']}")
                print(f"   成功率: {result['success_count']/result['total_cases']*100:.1f}%")
                
                # 显示成功的脚本
                success_results = [r for r in result['results'] if r.get('success')]
                if success_results:
                    print("\n📁 生成的脚本文件:")
                    for i, res in enumerate(success_results[:5], 1):
                        print(f"   {i}. {res.get('script_name', 'unknown')} "
                              f"(评分: {res.get('review_score', 0)})")
            else:
                print(f"❌ 批量生成失败: {result.get('error')}")
                
        except Exception as e:
            print(f"❌ 批量生成异常: {e}")
    
    def list_test_cases(self, module: Optional[str] = None, limit: int = 10):
        """列出测试用例"""
        print("📋 测试用例列表:")
        
        try:
            with self.controller.script_db.get_session() as session:
                from backend.models.script_models import TestCase, BusinessModule
                
                query = session.query(TestCase).join(BusinessModule)
                if module:
                    query = query.filter(BusinessModule.module_name == module)
                
                test_cases = query.limit(limit).all()
                
                if not test_cases:
                    print("   没有找到测试用例")
                    return
                
                for i, case in enumerate(test_cases, 1):
                    module_name = case.business_module.module_name if case.business_module else case.group_name
                    print(f"   {i:2d}. {case.tcid} - {module_name}")
                    print(f"       {case.steps[:80]}...")
                    print()
                    
        except Exception as e:
            print(f"❌ 列出用例失败: {e}")
    
    def list_modules(self):
        """列出业务模块"""
        print("📂 业务模块列表:")
        
        try:
            with self.controller.script_db.get_session() as session:
                from backend.models.script_models import BusinessModule
                
                modules = session.query(BusinessModule).all()
                
                if not modules:
                    print("   没有找到业务模块")
                    return
                
                for i, module in enumerate(modules, 1):
                    case_count = len(module.test_cases) if module.test_cases else 0
                    comp_count = len(module.package_components) if module.package_components else 0
                    print(f"   {i:2d}. {module.module_name}")
                    print(f"       用例: {case_count} 个, 组件: {comp_count} 个")
                    print(f"       {module.description}")
                    print()
                    
        except Exception as e:
            print(f"❌ 列出模块失败: {e}")
    
    def show_stats(self):
        """显示系统统计信息"""
        print("📊 系统统计信息:")
        
        try:
            stats = self.controller.get_generation_statistics()
            
            print(f"   测试用例总数: {stats.get('total_test_cases', 0)}")
            print(f"   生成脚本总数: {stats.get('total_generated_scripts', 0)}")
            print(f"   会话总数: {stats.get('total_sessions', 0)}")
            print(f"   完成会话数: {stats.get('completed_sessions', 0)}")
            print(f"   成功率: {stats.get('success_rate', 0):.1f}%")
            
            # 显示testcase目录中的文件
            testcase_dir = Path("testcase")
            if testcase_dir.exists():
                ts_files = list(testcase_dir.rglob("*.ts"))
                print(f"   生成文件数: {len(ts_files)}")
                
        except Exception as e:
            print(f"❌ 获取统计信息失败: {e}")
    
    def _show_file_preview(self, file_path: str, lines: int = 15):
        """显示文件预览"""
        try:
            if Path(file_path).exists():
                print(f"\n📄 文件预览 ({file_path}):")
                print("-" * 50)
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    for i, line in enumerate(f, 1):
                        if i > lines:
                            print("   ...")
                            break
                        print(f"   {i:2d}: {line.rstrip()}")
                
                print("-" * 50)
            else:
                print(f"⚠️  文件不存在: {file_path}")
                
        except Exception as e:
            print(f"⚠️  无法预览文件: {e}")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="基于AutoGen的Android自动化脚本生成系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 初始化系统
  python backend/cli_tool.py init
  
  # 从文本生成脚本
  python backend/cli_tool.py generate-text "1.打开应用 2.点击登录 3.输入用户名密码 4.验证登录成功"
  
  # 从用例ID生成脚本
  python backend/cli_tool.py generate-case TC_001
  
  # 批量生成脚本
  python backend/cli_tool.py batch --module 登录模块 --limit 3
  
  # 列出测试用例
  python backend/cli_tool.py list-cases --module 登录模块
  
  # 显示统计信息
  python backend/cli_tool.py stats
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 初始化命令
    subparsers.add_parser('init', help='初始化系统')
    
    # 从文本生成脚本
    text_parser = subparsers.add_parser('generate-text', help='从文本描述生成脚本')
    text_parser.add_argument('text', help='测试用例文本描述')
    text_parser.add_argument('--module', help='业务模块名称')
    
    # 从用例ID生成脚本
    case_parser = subparsers.add_parser('generate-case', help='从用例ID生成脚本')
    case_parser.add_argument('case_id', help='测试用例ID')
    
    # 批量生成
    batch_parser = subparsers.add_parser('batch', help='批量生成脚本')
    batch_parser.add_argument('--module', help='指定业务模块')
    batch_parser.add_argument('--limit', type=int, default=5, help='处理数量限制')
    batch_parser.add_argument('--case-ids', nargs='+', help='指定用例ID列表')
    
    # 列出用例
    list_cases_parser = subparsers.add_parser('list-cases', help='列出测试用例')
    list_cases_parser.add_argument('--module', help='过滤业务模块')
    list_cases_parser.add_argument('--limit', type=int, default=10, help='显示数量')
    
    # 列出模块
    subparsers.add_parser('list-modules', help='列出业务模块')
    
    # 显示统计
    subparsers.add_parser('stats', help='显示系统统计信息')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 创建CLI实例
    cli = ScriptGenerationCLI()
    
    try:
        if args.command == 'init':
            await cli.init_system()
            
        elif args.command == 'generate-text':
            await cli.generate_from_text(args.text, args.module)
            
        elif args.command == 'generate-case':
            await cli.generate_from_case_id(args.case_id)
            
        elif args.command == 'batch':
            await cli.batch_generate(args.case_ids, args.module, args.limit)
            
        elif args.command == 'list-cases':
            cli.list_test_cases(args.module, args.limit)
            
        elif args.command == 'list-modules':
            cli.list_modules()
            
        elif args.command == 'stats':
            cli.show_stats()
            
    except KeyboardInterrupt:
        print("\n⚠️  操作被用户中断")
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
