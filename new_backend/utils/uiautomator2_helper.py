"""
UIautomator2辅助工具
提供Android设备连接、元素提取、截图等功能
"""
import os
import sys
import json
import hashlib
import xml.etree.ElementTree as ET
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path
from datetime import datetime
import uiautomator2 as u2
from loguru import logger

# 添加相对路径导入
sys.path.append(str(Path(__file__).parent.parent))

from config import config
from models.page_models import UIElement, UIPage, ElementPosition, ElementAttributes


class UIAutomator2Helper:
    """UIautomator2辅助工具类"""
    
    def __init__(self, device_id: Optional[str] = None):
        """
        初始化UIautomator2辅助工具
        
        Args:
            device_id: 设备ID，None表示使用默认设备
        """
        self.device_id = device_id
        self.device = None
        self.config = config.uiautomator2
        
        # 初始化设备连接
        self._connect_device()
    
    def _connect_device(self) -> bool:
        """连接设备"""
        try:
            if self.device_id:
                self.device = u2.connect(self.device_id)
                logger.info(f"连接到指定设备: {self.device_id}")
            else:
                self.device = u2.connect()
                logger.info("连接到默认设备")
            
            # 检查设备状态
            device_info = self.device.info
            logger.info(f"设备信息: {device_info.get('productName', 'Unknown')} - {device_info.get('version', 'Unknown')}")
            
            return True
            
        except Exception as e:
            logger.error(f"设备连接失败: {e}")
            self.device = None
            return False
    
    def is_connected(self) -> bool:
        """检查设备是否连接"""
        try:
            if self.device is None:
                return False
            
            # 尝试获取设备信息来验证连接
            _ = self.device.info
            return True
            
        except Exception as e:
            logger.warning(f"设备连接检查失败: {e}")
            return False
    
    def reconnect(self) -> bool:
        """重新连接设备"""
        logger.info("尝试重新连接设备...")
        return self._connect_device()
    
    def get_device_info(self) -> Dict[str, Any]:
        """获取设备信息"""
        try:
            if not self.is_connected():
                return {}
            
            info = self.device.info
            return {
                'device_id': self.device_id or 'default',
                'product_name': info.get('productName', 'Unknown'),
                'version': info.get('version', 'Unknown'),
                'sdk_version': info.get('sdkInt', 0),
                'display': info.get('display', {}),
                'battery': self._get_battery_info(),
                'network': self._get_network_info()
            }
            
        except Exception as e:
            logger.error(f"获取设备信息失败: {e}")
            return {}
    
    def _get_battery_info(self) -> Dict[str, Any]:
        """获取电池信息"""
        try:
            # 使用shell命令获取电池信息
            battery_output = self.device.shell("dumpsys battery")
            battery_info = {}
            
            for line in battery_output.split('\n'):
                if 'level:' in line:
                    battery_info['level'] = int(line.split(':')[1].strip())
                elif 'status:' in line:
                    battery_info['status'] = line.split(':')[1].strip()
                elif 'temperature:' in line:
                    battery_info['temperature'] = int(line.split(':')[1].strip())
            
            return battery_info
            
        except Exception as e:
            logger.warning(f"获取电池信息失败: {e}")
            return {}
    
    def _get_network_info(self) -> Dict[str, Any]:
        """获取网络信息"""
        try:
            # 获取网络连接状态
            wifi_output = self.device.shell("dumpsys wifi | grep 'mNetworkInfo'")
            network_info = {
                'wifi_connected': 'CONNECTED' in wifi_output,
                'network_type': 'wifi' if 'WIFI' in wifi_output else 'mobile'
            }
            
            return network_info
            
        except Exception as e:
            logger.warning(f"获取网络信息失败: {e}")
            return {}
    
    def launch_app(self, package_name: str) -> bool:
        """启动应用"""
        try:
            if not self.is_connected():
                logger.error("设备未连接")
                return False
            
            logger.info(f"启动应用: {package_name}")
            self.device.app_start(package_name)
            
            # 等待应用启动
            import time
            time.sleep(self.config.wait_after_action)
            
            # 验证应用是否启动成功
            current_app = self.get_current_app()
            if current_app and current_app.get('package') == package_name:
                logger.info(f"应用启动成功: {package_name}")
                return True
            else:
                logger.warning(f"应用启动可能失败: {package_name}")
                return False
                
        except Exception as e:
            logger.error(f"启动应用失败: {e}")
            return False
    
    def get_current_app(self) -> Optional[Dict[str, str]]:
        """获取当前应用信息"""
        try:
            if not self.is_connected():
                return None
            
            app_info = self.device.app_current()
            return {
                'package': app_info.get('package', ''),
                'activity': app_info.get('activity', ''),
                'pid': app_info.get('pid', 0)
            }
            
        except Exception as e:
            logger.error(f"获取当前应用信息失败: {e}")
            return None
    
    def take_screenshot(self, save_path: Optional[str] = None) -> Optional[str]:
        """截取屏幕截图"""
        try:
            if not self.is_connected():
                logger.error("设备未连接")
                return None
            
            if save_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                save_path = str(config.paths.screenshots_dir / f"screenshot_{timestamp}.png")
            
            # 确保目录存在
            Path(save_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 截图
            self.device.screenshot(save_path)
            
            if Path(save_path).exists():
                logger.debug(f"截图保存成功: {save_path}")
                return save_path
            else:
                logger.error("截图保存失败")
                return None
                
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return None
    
    def dump_hierarchy(self, save_path: Optional[str] = None) -> Optional[str]:
        """导出UI层级结构XML"""
        try:
            if not self.is_connected():
                logger.error("设备未连接")
                return None
            
            if save_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                save_path = str(config.paths.xml_files_dir / f"hierarchy_{timestamp}.xml")
            
            # 确保目录存在
            Path(save_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 导出XML
            xml_content = self.device.dump_hierarchy()
            
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write(xml_content)
            
            logger.debug(f"UI层级结构保存成功: {save_path}")
            return save_path

        except Exception as e:
            logger.error(f"导出UI层级结构失败: {e}")
            return None

    def extract_elements_from_xml(self, xml_path: str) -> List[UIElement]:
        """从XML文件提取UI元素"""
        try:
            elements = []

            # 解析XML文件
            tree = ET.parse(xml_path)
            root = tree.getroot()

            # 递归提取元素
            self._extract_elements_recursive(root, elements, 0)

            logger.info(f"从XML提取到 {len(elements)} 个元素")
            return elements

        except Exception as e:
            logger.error(f"从XML提取元素失败: {e}")
            return []

    def _extract_elements_recursive(self, node: ET.Element, elements: List[UIElement], depth: int):
        """递归提取XML节点中的元素"""
        try:
            # 提取元素属性
            bounds = node.get('bounds', '')
            position = self._parse_bounds(bounds)

            # 创建元素属性对象
            attributes = ElementAttributes(
                clickable=node.get('clickable', 'false').lower() == 'true',
                editable=node.get('editable', 'false').lower() == 'true',
                scrollable=node.get('scrollable', 'false').lower() == 'true',
                checkable=node.get('checkable', 'false').lower() == 'true',
                checked=node.get('checked', 'false').lower() == 'true',
                enabled=node.get('enabled', 'true').lower() == 'true',
                focusable=node.get('focusable', 'false').lower() == 'true',
                focused=node.get('focused', 'false').lower() == 'true',
                selected=node.get('selected', 'false').lower() == 'true',
                visible=True  # 默认可见
            )

            # 生成元素ID
            element_id = self._generate_element_id(node, depth)

            # 创建UI元素对象
            element = UIElement(
                element_id=element_id,
                element_type=node.get('class', ''),
                class_name=node.get('class', ''),
                resource_id=node.get('resource-id', ''),
                text=node.get('text', ''),
                content_desc=node.get('content-desc', ''),
                position=position.__dict__ if position else None,
                attributes=attributes.__dict__,
                hierarchy_index=depth,
                is_actionable=attributes.clickable or attributes.editable or attributes.scrollable
            )

            # 只添加有意义的元素（有文本、资源ID或可操作的元素）
            if (element.text or element.resource_id or element.is_actionable or
                element.content_desc):
                elements.append(element)

            # 递归处理子元素
            for child in node:
                self._extract_elements_recursive(child, elements, depth + 1)

        except Exception as e:
            logger.warning(f"提取元素失败: {e}")

    def _parse_bounds(self, bounds_str: str) -> Optional[ElementPosition]:
        """解析bounds字符串为位置对象"""
        try:
            if not bounds_str:
                return None

            # bounds格式: [left,top][right,bottom]
            import re
            pattern = r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]'
            match = re.match(pattern, bounds_str)

            if match:
                left, top, right, bottom = map(int, match.groups())
                return ElementPosition(left=left, top=top, right=right, bottom=bottom)

            return None

        except Exception as e:
            logger.warning(f"解析bounds失败: {e}")
            return None

    def _generate_element_id(self, node: ET.Element, depth: int) -> str:
        """生成元素唯一ID"""
        try:
            # 使用多个属性组合生成唯一ID
            id_components = [
                node.get('class', ''),
                node.get('resource-id', ''),
                node.get('text', ''),
                node.get('content-desc', ''),
                node.get('bounds', ''),
                str(depth)
            ]

            # 创建哈希值
            id_string = '|'.join(id_components)
            element_id = hashlib.md5(id_string.encode('utf-8')).hexdigest()[:16]

            return element_id

        except Exception as e:
            logger.warning(f"生成元素ID失败: {e}")
            return f"element_{depth}_{datetime.now().timestamp()}"

    def extract_page_elements(self, package_name: str) -> Tuple[Optional[UIPage], List[UIElement], Optional[str], Optional[str]]:
        """提取当前页面的所有元素"""
        try:
            if not self.is_connected():
                logger.error("设备未连接")
                return None, [], None, None

            # 获取当前应用信息
            current_app = self.get_current_app()
            if not current_app or current_app.get('package') != package_name:
                logger.warning(f"当前应用不匹配: 期望 {package_name}, 实际 {current_app}")

            # 截图
            screenshot_path = self.take_screenshot()

            # 导出XML
            xml_path = self.dump_hierarchy()

            if not xml_path:
                logger.error("无法导出UI层级结构")
                return None, [], screenshot_path, None

            # 提取元素
            elements = self.extract_elements_from_xml(xml_path)

            # 创建页面对象
            page = self._create_page_object(current_app, elements, screenshot_path, xml_path)

            logger.info(f"页面元素提取完成: {len(elements)} 个元素")
            return page, elements, screenshot_path, xml_path

        except Exception as e:
            logger.error(f"提取页面元素失败: {e}")
            return None, [], None, None

    def _create_page_object(self, app_info: Dict[str, Any], elements: List[UIElement],
                          screenshot_path: Optional[str], xml_path: Optional[str]) -> UIPage:
        """创建页面对象"""
        try:
            # 生成页面ID
            page_id = self._generate_page_id(app_info, elements)

            # 计算页面哈希
            page_hash = self._calculate_page_hash(elements)
            key_elements_hash = self._calculate_key_elements_hash(elements)

            # 统计元素数量
            actionable_count = sum(1 for elem in elements if elem.is_actionable)

            page = UIPage(
                page_id=page_id,
                activity_name=app_info.get('activity', ''),
                page_name=self._extract_page_name(app_info, elements),
                page_hash=page_hash,
                key_elements_hash=key_elements_hash,
                element_count=len(elements),
                actionable_element_count=actionable_count,
                screenshot_path=screenshot_path,
                xml_dump_path=xml_path,
                is_analyzed=True,
                page_metadata={
                    'app_info': app_info,
                    'extraction_time': datetime.now().isoformat(),
                    'device_info': self.get_device_info()
                }
            )

            return page

        except Exception as e:
            logger.error(f"创建页面对象失败: {e}")
            return None

    def _generate_page_id(self, app_info: Dict[str, Any], elements: List[UIElement]) -> str:
        """生成页面唯一ID"""
        try:
            # 使用应用信息和关键元素生成页面ID
            id_components = [
                app_info.get('package', ''),
                app_info.get('activity', ''),
                str(len(elements)),
                str(sum(1 for elem in elements if elem.is_actionable))
            ]

            # 添加关键元素的特征
            key_elements = [elem for elem in elements if elem.resource_id or elem.text][:5]
            for elem in key_elements:
                id_components.append(elem.resource_id or elem.text or '')

            id_string = '|'.join(id_components)
            page_id = hashlib.md5(id_string.encode('utf-8')).hexdigest()[:16]

            return f"page_{page_id}"

        except Exception as e:
            logger.warning(f"生成页面ID失败: {e}")
            return f"page_{datetime.now().timestamp()}"

    def _calculate_page_hash(self, elements: List[UIElement]) -> str:
        """计算页面结构哈希值"""
        try:
            # 使用所有元素的结构信息计算哈希
            hash_components = []

            for elem in elements:
                component = f"{elem.element_type}|{elem.resource_id}|{elem.hierarchy_index}"
                hash_components.append(component)

            hash_string = '||'.join(sorted(hash_components))
            return hashlib.md5(hash_string.encode('utf-8')).hexdigest()

        except Exception as e:
            logger.warning(f"计算页面哈希失败: {e}")
            return ""

    def _calculate_key_elements_hash(self, elements: List[UIElement]) -> str:
        """计算关键元素哈希值"""
        try:
            # 只使用可操作元素和有文本的元素计算哈希
            key_elements = [elem for elem in elements if elem.is_actionable or elem.text or elem.resource_id]

            hash_components = []
            for elem in key_elements:
                component = f"{elem.element_type}|{elem.resource_id}|{elem.text}"
                hash_components.append(component)

            hash_string = '||'.join(sorted(hash_components))
            return hashlib.md5(hash_string.encode('utf-8')).hexdigest()

        except Exception as e:
            logger.warning(f"计算关键元素哈希失败: {e}")
            return ""

    def _extract_page_name(self, app_info: Dict[str, Any], elements: List[UIElement]) -> str:
        """提取页面名称"""
        try:
            # 尝试从Activity名称提取
            activity = app_info.get('activity', '')
            if activity:
                # 提取Activity的简单名称
                activity_name = activity.split('.')[-1]
                if activity_name and activity_name != 'Activity':
                    return activity_name

            # 尝试从页面标题元素提取
            title_elements = [elem for elem in elements if
                            elem.text and len(elem.text) < 50 and
                            ('title' in elem.resource_id.lower() if elem.resource_id else False)]

            if title_elements:
                return title_elements[0].text

            # 使用默认名称
            return f"Page_{datetime.now().strftime('%H%M%S')}"

        except Exception as e:
            logger.warning(f"提取页面名称失败: {e}")
            return "Unknown_Page"
