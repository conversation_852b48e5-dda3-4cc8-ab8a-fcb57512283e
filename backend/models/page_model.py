"""
UI页面数据模型
基于Page Object模式设计的页面模型
"""
from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List, Set
from datetime import datetime
import hashlib
import json
from .element_model import UIElement


@dataclass
class PageNavigation:
    """页面导航关系"""
    source_page_id: str
    target_page_id: str
    via_element_id: str
    action_type: str  # click, long_click, scroll等
    success_rate: float = 1.0  # 导航成功率
    avg_load_time: float = 0.0  # 平均加载时间
    created_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'source_page_id': self.source_page_id,
            'target_page_id': self.target_page_id,
            'via_element_id': self.via_element_id,
            'action_type': self.action_type,
            'success_rate': self.success_rate,
            'avg_load_time': self.avg_load_time,
            'created_at': self.created_at.isoformat()
        }


@dataclass
class UIPage:
    """UI页面模型"""
    page_id: str
    page_name: str = ""
    activity_name: str = ""
    package_name: str = ""
    elements: List[UIElement] = field(default_factory=list)
    screenshot_path: str = ""
    xml_source_path: str = ""
    
    # 页面特征
    content_hash: str = ""  # 页面内容哈希
    element_count: int = 0
    actionable_element_count: int = 0
    
    # 导航信息
    navigations: List[PageNavigation] = field(default_factory=list)
    parent_pages: Set[str] = field(default_factory=set)  # 父页面ID集合
    child_pages: Set[str] = field(default_factory=set)   # 子页面ID集合
    
    # 分析状态
    analysis_status: str = "pending"  # pending, analyzing, completed, failed
    analysis_error: str = ""
    
    # 元数据
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    last_visited_at: Optional[datetime] = None
    visit_count: int = 0
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.page_id:
            self.page_id = self.generate_page_id()
        self.update_statistics()
    
    def generate_page_id(self) -> str:
        """生成页面唯一ID"""
        # 基于Activity名称和内容哈希生成ID
        key_data = f"{self.activity_name}_{self.package_name}"
        if self.content_hash:
            key_data += f"_{self.content_hash}"
        else:
            # 如果没有内容哈希，使用元素信息生成
            element_info = "_".join([elem.element_id for elem in self.elements[:5]])
            key_data += f"_{element_info}"
        
        return hashlib.md5(key_data.encode()).hexdigest()[:16]
    
    def calculate_content_hash(self) -> str:
        """计算页面内容哈希"""
        # 基于关键元素信息计算哈希
        content_data = []
        for element in self.elements:
            if element.is_actionable():
                content_data.append(f"{element.element_type}_{element.resource_id}_{element.text}")
        
        content_str = "|".join(sorted(content_data))
        self.content_hash = hashlib.md5(content_str.encode()).hexdigest()
        return self.content_hash
    
    def update_statistics(self):
        """更新页面统计信息"""
        self.element_count = len(self.elements)
        self.actionable_element_count = sum(1 for elem in self.elements if elem.is_actionable())
        self.updated_at = datetime.now()
    
    def add_element(self, element: UIElement):
        """添加元素"""
        self.elements.append(element)
        self.update_statistics()
    
    def get_actionable_elements(self) -> List[UIElement]:
        """获取可操作元素"""
        return [elem for elem in self.elements if elem.is_actionable()]
    
    def get_elements_by_type(self, element_type: str) -> List[UIElement]:
        """根据类型获取元素"""
        return [elem for elem in self.elements if elem.element_type == element_type]
    
    def get_element_by_id(self, element_id: str) -> Optional[UIElement]:
        """根据ID获取元素"""
        for element in self.elements:
            if element.element_id == element_id:
                return element
        return None
    
    def get_elements_by_text(self, text: str, exact_match: bool = True) -> List[UIElement]:
        """根据文本获取元素"""
        if exact_match:
            return [elem for elem in self.elements if elem.text == text]
        else:
            return [elem for elem in self.elements if text.lower() in elem.text.lower()]
    
    def get_elements_by_resource_id(self, resource_id: str) -> List[UIElement]:
        """根据资源ID获取元素"""
        return [elem for elem in self.elements if elem.resource_id == resource_id]
    
    def add_navigation(self, navigation: PageNavigation):
        """添加导航关系"""
        self.navigations.append(navigation)
        self.child_pages.add(navigation.target_page_id)
    
    def get_navigation_to_page(self, target_page_id: str) -> Optional[PageNavigation]:
        """获取到指定页面的导航"""
        for nav in self.navigations:
            if nav.target_page_id == target_page_id:
                return nav
        return None
    
    def mark_visited(self):
        """标记页面已访问"""
        self.last_visited_at = datetime.now()
        self.visit_count += 1
    
    def is_similar_to(self, other_page: 'UIPage', similarity_threshold: float = 0.8) -> bool:
        """判断与另一个页面是否相似"""
        if self.activity_name and other_page.activity_name:
            if self.activity_name == other_page.activity_name:
                return True
        
        if self.content_hash and other_page.content_hash:
            return self.content_hash == other_page.content_hash
        
        # 基于元素相似度判断
        if not self.elements or not other_page.elements:
            return False
        
        common_elements = 0
        for elem1 in self.elements:
            for elem2 in other_page.elements:
                if (elem1.resource_id and elem1.resource_id == elem2.resource_id) or \
                   (elem1.text and elem1.text == elem2.text):
                    common_elements += 1
                    break
        
        similarity = common_elements / max(len(self.elements), len(other_page.elements))
        return similarity >= similarity_threshold
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'page_id': self.page_id,
            'page_name': self.page_name,
            'activity_name': self.activity_name,
            'package_name': self.package_name,
            'screenshot_path': self.screenshot_path,
            'xml_source_path': self.xml_source_path,
            'content_hash': self.content_hash,
            'element_count': self.element_count,
            'actionable_element_count': self.actionable_element_count,
            'analysis_status': self.analysis_status,
            'analysis_error': self.analysis_error,
            'elements': [elem.to_dict() for elem in self.elements],
            'navigations': [nav.to_dict() for nav in self.navigations],
            'parent_pages': list(self.parent_pages),
            'child_pages': list(self.child_pages),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'last_visited_at': self.last_visited_at.isoformat() if self.last_visited_at else None,
            'visit_count': self.visit_count
        }
    
    def to_page_object_class(self) -> str:
        """生成Page Object类代码"""
        class_name = self.page_name.replace(" ", "").replace("-", "") + "Page"
        if not class_name.endswith("Page"):
            class_name += "Page"
        
        # 生成类代码
        code_lines = [
            f"class {class_name}:",
            f'    """',
            f'    {self.page_name} Page Object',
            f'    Activity: {self.activity_name}',
            f'    """',
            f'    ',
            f'    def __init__(self, driver):',
            f'        self.driver = driver',
            f'        self.activity_name = "{self.activity_name}"',
            f'    '
        ]
        
        # 添加元素定位器
        for element in self.get_actionable_elements():
            element_name = self._generate_element_name(element)
            locators = element.get_locator_strategies()
            
            if 'resource_id' in locators:
                code_lines.append(f'    @property')
                code_lines.append(f'    def {element_name}(self):')
                code_lines.append(f'        """')
                code_lines.append(f'        {element.semantic_description or element.text or element.content_desc}')
                code_lines.append(f'        """')
                code_lines.append(f'        return self.driver(resourceId="{locators["resource_id"]}")')
                code_lines.append(f'    ')
        
        return "\n".join(code_lines)
    
    def _generate_element_name(self, element: UIElement) -> str:
        """生成元素名称"""
        name = ""
        if element.text:
            name = element.text.lower().replace(" ", "_").replace("-", "_")
        elif element.content_desc:
            name = element.content_desc.lower().replace(" ", "_").replace("-", "_")
        elif element.resource_id:
            name = element.resource_id.split(":")[-1].split("/")[-1]
        else:
            name = f"{element.element_type.lower()}_{element.index}"
        
        # 清理名称
        name = "".join(c for c in name if c.isalnum() or c == "_")
        if not name or name[0].isdigit():
            name = f"element_{name}"
        
        return name


@dataclass
class PageAnalysisResult:
    """页面分析结果"""
    page: UIPage
    success: bool = True
    error_message: str = ""
    analysis_duration: float = 0.0  # 分析耗时（秒）
    ai_enhancement_applied: bool = False
    screenshot_captured: bool = False
    xml_saved: bool = False
    
    # 分析统计
    total_elements_found: int = 0
    actionable_elements_found: int = 0
    ai_enhanced_elements: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'page_id': self.page.page_id,
            'success': self.success,
            'error_message': self.error_message,
            'analysis_duration': self.analysis_duration,
            'ai_enhancement_applied': self.ai_enhancement_applied,
            'screenshot_captured': self.screenshot_captured,
            'xml_saved': self.xml_saved,
            'total_elements_found': self.total_elements_found,
            'actionable_elements_found': self.actionable_elements_found,
            'ai_enhanced_elements': self.ai_enhanced_elements,
            'page_data': self.page.to_dict()
        }
