# 移动端应用UI自动化测试分析系统

## 系统概述

本系统是一个智能化的Android应用UI分析系统，能够自动启动应用、遍历所有页面，提取结构化元素信息，并通过大模型增强元素语义描述，最终存储到数据库。该系统旨在为后续的UI自动化脚本生成提供高质量的数据基础，采用Page Object模式组织元素信息，确保可维护性和可扩展性。

## 系统架构

```
应用控制层 → 元素提取层 → 智能分析层 → 数据存储层
  │               │             │             │
  │               │             │             └── MySQL数据库（存储元素和页面关系）
  │               │             │
  │               │             └── AutoGen(0.5.7) + UI-TARS大模型智能体（语义增强）
  │               │
  │               └── UIAutomator2（元素提取）+ ADB（设备操作和应用启动）
  │
  └── 待测Android应用（通过指定包名和Activity启动）
```

## 核心功能

### 1. 应用自动启动
- 支持通过包名和Activity名称启动应用
- 自动检测设备连接状态
- 强制停止应用确保干净状态
- 支持模拟器和真机设备

### 2. 智能页面遍历
- 基于BFS算法的页面遍历
- 自动识别可操作元素
- 智能去重避免重复访问
- 构建页面关系图谱

### 3. 元素信息提取
- 提取UI元素的完整属性信息
- 支持多种定位策略（资源ID、文本、XPath等）
- 自动截图和XML源码保存
- 元素位置坐标解析

### 4. AI语义增强
- 集成UI-TARS大模型进行语义分析
- 生成自然语言元素描述
- 预测用户操作意图
- 元素功能分类

### 5. 数据持久化
- MySQL数据库存储
- Page Object模式数据组织
- 支持数据清洗和验证
- 完整的关系图谱存储

## 安装和配置

### 环境要求

- Python 3.8+
- Android设备或模拟器
- ADB工具
- MySQL数据库

### 依赖安装

```bash
# 安装Python依赖
pip install uiautomator2
pip install autogen-agentchat
pip install sqlalchemy
pip install pymysql
pip install loguru

# 初始化UIAutomator2
python -m uiautomator2 init
```

### 数据库配置

1. 创建MySQL数据库：
```sql
CREATE DATABASE ui_analysis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 配置数据库连接（在`backend/config.py`中）：
```python
@dataclass
class DatabaseConfig:
    host: str = "localhost"
    port: int = 3306
    username: str = "root"
    password: str = "your_password"
    database: str = "ui_analysis"
```

### 设备配置

1. 启用Android设备的开发者选项和USB调试
2. 连接设备并确认ADB连接：
```bash
adb devices
```

## 使用方法

### 基本使用

```bash
# 分析指定应用
python -m backend.main --package com.example.app

# 指定设备和Activity
python -m backend.main --package com.example.app --device emulator-5554 --activity MainActivity

# 限制遍历范围
python -m backend.main --package com.example.app --max-depth 10 --max-pages 50
```

### 高级选项

```bash
# 禁用AI增强（加快分析速度）
python -m backend.main --package com.example.app --no-ai

# 不保存到数据库（仅生成报告）
python -m backend.main --package com.example.app --no-save-db
```

### 编程接口

```python
import asyncio
from backend.main import UIAnalysisSystem

async def analyze_app():
    # 创建分析系统
    system = UIAnalysisSystem(device_id="emulator-5554")
    
    # 检查系统要求
    if not system.check_system_requirements():
        return
    
    # 执行分析
    session = await system.analyze_app(
        package_name="com.example.app",
        max_depth=15,
        enable_ai=True
    )
    
    print(f"分析完成，发现 {len(session.pages)} 个页面")

# 运行分析
asyncio.run(analyze_app())
```

## 输出结果

### 分析报告

系统会生成以下文件：

1. **分析摘要报告** (`temp/analysis_report_*.json`)
   - 应用基本信息
   - 页面覆盖率统计
   - 元素统计信息
   - AI增强效果

2. **页面关系图谱** (`temp/page_graph_*.json`)
   - 页面节点信息
   - 导航关系边
   - 图谱统计数据

3. **页面截图** (`temp/screenshots/`)
   - 每个页面的PNG截图
   - 按页面ID命名

4. **XML源码** (`temp/xml_files/`)
   - 页面的XML结构文件
   - 用于调试和验证

### 数据库结构

系统使用以下主要数据表：

- `app_info`: 应用基本信息
- `analysis_sessions`: 分析会话记录
- `app_pages`: 页面信息
- `page_elements`: UI元素详情
- `page_navigations`: 页面导航关系

## 测试和调试

### 运行系统测试

```bash
# 运行完整测试套件
python temp/test_ui_analysis_system.py

# 运行特定测试
python temp/test_ui_analysis_system.py --test device_connection
python temp/test_ui_analysis_system.py --test element_extractor
```

### 常见问题排查

1. **设备连接失败**
   - 检查ADB连接：`adb devices`
   - 重启ADB服务：`adb kill-server && adb start-server`
   - 检查USB调试是否开启

2. **UIAutomator2服务异常**
   - 重新初始化：`python -m uiautomator2 init`
   - 检查设备权限设置

3. **数据库连接失败**
   - 验证数据库配置
   - 检查网络连接
   - 确认数据库服务运行状态

4. **AI增强失败**
   - 检查网络连接
   - 验证API密钥配置
   - 可以禁用AI增强继续分析

## 配置选项

### 系统配置

在`backend/config.py`中可以调整以下配置：

```python
# 设备配置
device.connection_timeout = 30  # 连接超时时间
device.retry_count = 3          # 重试次数

# 爬虫配置
crawler.max_depth = 20          # 最大遍历深度
crawler.max_pages = 100         # 最大页面数量
crawler.page_load_timeout = 30  # 页面加载超时

# AI配置
ai.max_retries = 3              # AI请求重试次数
ai.timeout = 60                 # AI请求超时时间
ai.fallback_enabled = True      # 启用降级处理
```

### 元素筛选规则

```python
# 可操作元素属性
clickable_attributes = ["clickable", "long_clickable", "checkable", "editable", "scrollable"]

# 排除的元素类型
exclude_classes = [
    "android.view.View",
    "android.widget.LinearLayout",
    "android.widget.RelativeLayout"
]

# 最小元素尺寸
min_size = {"width": 10, "height": 10}
```

## 扩展开发

### 添加新的元素分析规则

在`backend/element_extractor.py`中的`_should_include_element`方法中添加自定义筛选逻辑。

### 自定义AI提示词

在`backend/intelligent_agent.py`中的`_get_system_prompt`方法中修改AI分析提示词。

### 扩展数据库模型

在`backend/database.py`中添加新的数据表和字段。

## 性能优化

### 提升分析速度

1. 禁用AI增强：`--no-ai`
2. 减少遍历深度：`--max-depth 5`
3. 限制页面数量：`--max-pages 20`
4. 禁用截图：修改配置`screenshot_enabled = False`

### 内存优化

1. 分批处理大型应用
2. 定期清理临时文件
3. 使用数据库连接池

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献指南

欢迎提交Issue和Pull Request来改进系统功能。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issue页面
- 邮箱：[<EMAIL>]
