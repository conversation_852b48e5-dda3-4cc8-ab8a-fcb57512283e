"""
测试用例分析相关数据模型
支持自然语言用例解析、操作步骤提取、断言生成
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, JSON, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum

Base = declarative_base()

# Excel字段映射字典 - 基于真实Excel文件结构
EXCEL_FIELD_MAPPING = {
    'group': '*Group',
    'tcid': '*TCID',
    'casename': '*CaseName',
    'sort': 'Sort',
    'subgroup': '*SubGroup',
    'component': '*Component',
    'subcomponent': 'SubComponent',
    'subfunction': 'SubFunction',
    'secondfunction': 'SecondFunction',
    'type': '*Type',
    'casesettype': 'CaseSetType',
    'level': '*Level',
    'purpose': 'Purpose',
    'precondition': 'PreCondition',
    'steps': '*Steps',
    'expectresult': '*ExpectResult',
    'automated': '*Automated',
    'owner': '*Owner',
    'executeowner': '*ExecuteOwner',
    'keywords': 'Keywords',
    'phase': 'Phase',
    'testarea': 'TestArea',
    'excutetime': 'ExcuteTime',
    'standard': 'Standard',
    'simples': 'Simples',
    'environment': 'Environment',
    'os': 'OS',
    'android': 'Android',
    'cpu': 'CPU',
    'interactioncomponent': 'InteractionComponent',
    'brand': 'Brand',
    'sim': 'SIM',
    'source': 'Source',
    'matchproject': 'MatchProject',
    'testcodepath': 'TestCodePath',
    'appversion': 'Appversion',
    'specialability': 'SpecialAbility',
    'specialtype': 'SpecialType',
    'update_time': '更新时间',
    'auditresult': 'AuditResult',
    'auditremark': 'AuditRemark',
}


class ActionType(Enum):
    """操作类型枚举"""
    CLICK = "click"
    INPUT = "input"
    SWIPE = "swipe"
    SCROLL = "scroll"
    WAIT = "wait"
    BACK = "back"
    LAUNCH = "launch"
    CLOSE = "close"
    DRAG = "drag"
    LONG_CLICK = "long_click"


class AssertionType(Enum):
    """断言类型枚举"""
    ELEMENT_EXISTS = "element_exists"
    ELEMENT_NOT_EXISTS = "element_not_exists"
    TEXT_CONTAINS = "text_contains"
    TEXT_EQUALS = "text_equals"
    PAGE_TITLE = "page_title"
    TOAST_MESSAGE = "toast_message"
    DIALOG_APPEARS = "dialog_appears"
    PAGE_NAVIGATION = "page_navigation"


@dataclass
class ActionStep:
    """操作步骤数据类"""
    step_index: int
    action_type: str
    target_element: Optional[str] = None
    action_description: str = ""
    input_data: Optional[str] = None
    coordinates: Optional[tuple] = None
    wait_time: Optional[float] = None
    ai_action_text: str = ""  # Midscene.js AI操作描述


@dataclass
class AssertionStep:
    """断言步骤数据类"""
    step_index: int
    assertion_type: str
    target_element: Optional[str] = None
    expected_value: str = ""
    assertion_description: str = ""
    ai_assertion_text: str = ""  # Midscene.js AI断言描述


class TestCase(Base):
    """测试用例表"""
    __tablename__ = 'test_cases'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    case_id = Column(String(100), unique=True, nullable=False, comment='用例ID')
    
    # 基本信息
    module_name = Column(String(200), comment='模块名称')
    case_name = Column(String(500), nullable=False, comment='用例名称')
    test_purpose = Column(Text, comment='测试目的')
    package_name = Column(String(200), comment='应用包名')
    
    # 用例内容
    preconditions = Column(Text, comment='前置条件')
    test_steps = Column(Text, nullable=False, comment='测试步骤')
    expected_results = Column(Text, comment='预期结果')
    
    # 用例属性
    priority = Column(String(20), default='medium', comment='优先级: low, medium, high, critical')
    case_type = Column(String(50), comment='用例类型: functional, ui, integration, etc.')
    automation_level = Column(String(50), default='manual', comment='自动化级别: manual, semi-auto, full-auto')
    
    # 分析状态
    analysis_status = Column(String(50), default='pending', comment='分析状态: pending, analyzing, completed, failed')
    script_generation_status = Column(String(50), default='pending', comment='脚本生成状态')
    
    # 元数据
    case_metadata = Column(JSON, comment='用例元数据')
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    # 关联关系
    analysis_results = relationship("CaseAnalysisResult", back_populates="test_case", cascade="all, delete-orphan")
    generated_scripts = relationship("GeneratedScript", back_populates="test_case")


class CaseAnalysisResult(Base):
    """用例分析结果表"""
    __tablename__ = 'case_analysis_results'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    case_id = Column(Integer, ForeignKey('test_cases.id'), nullable=False, comment='测试用例ID')
    analysis_session_id = Column(String(100), comment='分析会话ID')
    
    # 分析结果
    extracted_actions = Column(JSON, comment='提取的操作步骤（ActionStep列表）')
    extracted_assertions = Column(JSON, comment='提取的断言步骤（AssertionStep列表）')
    
    # 元素匹配结果
    matched_elements = Column(JSON, comment='匹配的UI元素信息')
    element_mapping = Column(JSON, comment='元素映射关系')
    
    # 分析质量评估
    analysis_confidence = Column(Float, comment='分析置信度')
    completeness_score = Column(Float, comment='完整性评分')
    clarity_score = Column(Float, comment='清晰度评分')
    
    # 分析详情
    analysis_notes = Column(Text, comment='分析备注')
    identified_risks = Column(JSON, comment='识别的风险点')
    optimization_suggestions = Column(JSON, comment='优化建议')
    
    # 智能体信息
    analyzer_agent = Column(String(100), comment='分析智能体名称')
    analysis_model = Column(String(100), comment='使用的AI模型')
    processing_time = Column(Integer, comment='处理时间（毫秒）')
    
    # 时间戳
    analyzed_at = Column(DateTime, default=datetime.now, comment='分析时间')
    
    # 关联关系
    test_case = relationship("TestCase", back_populates="analysis_results")


class ElementMapping(Base):
    """元素映射表"""
    __tablename__ = 'element_mappings'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    case_analysis_id = Column(Integer, ForeignKey('case_analysis_results.id'), nullable=False, comment='用例分析ID')
    
    # 映射信息
    step_index = Column(Integer, comment='步骤索引')
    action_description = Column(Text, comment='操作描述')
    target_element_description = Column(Text, comment='目标元素描述')
    
    # 匹配的元素信息
    matched_element_id = Column(String(100), comment='匹配的元素ID')
    element_selector = Column(Text, comment='元素选择器')
    match_confidence = Column(Float, comment='匹配置信度')
    match_method = Column(String(50), comment='匹配方法: text, id, xpath, ai_semantic')
    
    # 备选元素
    alternative_elements = Column(JSON, comment='备选元素列表')
    
    # 时间戳
    mapped_at = Column(DateTime, default=datetime.now, comment='映射时间')


class CaseValidation(Base):
    """用例验证表"""
    __tablename__ = 'case_validations'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    case_id = Column(Integer, ForeignKey('test_cases.id'), nullable=False, comment='测试用例ID')
    
    # 验证结果
    validation_status = Column(String(50), comment='验证状态: passed, failed, warning')
    validation_score = Column(Float, comment='验证评分')
    
    # 验证详情
    missing_elements = Column(JSON, comment='缺失的元素')
    ambiguous_steps = Column(JSON, comment='模糊的步骤')
    incomplete_assertions = Column(JSON, comment='不完整的断言')
    
    # 验证建议
    improvement_suggestions = Column(JSON, comment='改进建议')
    
    # 验证者信息
    validator_agent = Column(String(100), comment='验证智能体')
    validation_model = Column(String(100), comment='验证模型')
    
    # 时间戳
    validated_at = Column(DateTime, default=datetime.now, comment='验证时间')


@dataclass
class RealTestCase:
    """真实Excel测试用例数据模型 - 基于实际Excel文件结构"""
    # 核心必填字段
    group: str = ""                    # *Group - 业务模块
    tcid: str = ""                     # *TCID - 测试用例ID
    casename: str = ""                 # *CaseName - 用例名称
    subgroup: str = ""                 # *SubGroup - 子组
    component: str = ""                # *Component - 组件
    type: str = ""                     # *Type - 测试类型
    level: str = ""                    # *Level - 优先级
    steps: str = ""                    # *Steps - 测试步骤
    expectresult: str = ""             # *ExpectResult - 预期结果

    # 可选字段
    sort: Optional[float] = None       # Sort - 排序
    subcomponent: str = ""             # SubComponent - 子组件
    subfunction: str = ""              # SubFunction - 子功能
    secondfunction: str = ""           # SecondFunction - 二级功能
    casesettype: Optional[float] = None # CaseSetType - 用例集类型
    purpose: str = ""                  # Purpose - 测试目的
    precondition: str = ""             # PreCondition - 前置条件
    automated: Optional[float] = None  # *Automated - 是否自动化
    owner: Optional[float] = None      # *Owner - 负责人
    executeowner: Optional[float] = None # *ExecuteOwner - 执行人
    keywords: str = ""                 # Keywords - 关键字
    phase: Optional[int] = None        # Phase - 阶段
    testarea: str = ""                 # TestArea - 测试区域
    excutetime: Optional[float] = None # ExcuteTime - 执行时间
    standard: Optional[float] = None   # Standard - 标准
    simples: Optional[float] = None    # Simples - 简单
    environment: Optional[float] = None # Environment - 环境
    os: Optional[float] = None         # OS - 操作系统
    android: Optional[float] = None    # Android - Android版本
    cpu: Optional[float] = None        # CPU - CPU类型
    interactioncomponent: str = ""     # InteractionComponent - 交互组件
    brand: str = ""                    # Brand - 品牌
    sim: str = ""                      # SIM - SIM卡
    source: str = ""                   # Source - 来源
    matchproject: Optional[float] = None # MatchProject - 匹配项目
    testcodepath: Optional[float] = None # TestCodePath - 测试代码路径
    appversion: str = ""               # Appversion - 应用版本
    specialability: Optional[float] = None # SpecialAbility - 特殊能力
    specialtype: Optional[float] = None # SpecialType - 特殊类型
    update_time: str = ""              # 更新时间
    auditresult: Optional[float] = None # AuditResult - 审核结果
    auditremark: Optional[float] = None # AuditRemark - 审核备注

    def to_standard_test_case(self) -> 'TestCase':
        """转换为标准测试用例格式"""
        return TestCase(
            case_id=self.tcid,
            case_name=self.casename,
            test_purpose=self.purpose,
            package_name="",  # 需要从组件映射中获取
            module_name=self.group,
            preconditions=self.precondition,
            test_steps=self.steps,
            expected_results=self.expectresult,
            priority=self.level,
            test_type=self.type
        )

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'group': self.group,
            'tcid': self.tcid,
            'casename': self.casename,
            'subgroup': self.subgroup,
            'component': self.component,
            'type': self.type,
            'level': self.level,
            'steps': self.steps,
            'expectresult': self.expectresult,
            'sort': self.sort,
            'subcomponent': self.subcomponent,
            'subfunction': self.subfunction,
            'secondfunction': self.secondfunction,
            'casesettype': self.casesettype,
            'purpose': self.purpose,
            'precondition': self.precondition,
            'automated': self.automated,
            'owner': self.owner,
            'executeowner': self.executeowner,
            'keywords': self.keywords,
            'phase': self.phase,
            'testarea': self.testarea,
            'excutetime': self.excutetime,
            'standard': self.standard,
            'simples': self.simples,
            'environment': self.environment,
            'os': self.os,
            'android': self.android,
            'cpu': self.cpu,
            'interactioncomponent': self.interactioncomponent,
            'brand': self.brand,
            'sim': self.sim,
            'source': self.source,
            'matchproject': self.matchproject,
            'testcodepath': self.testcodepath,
            'appversion': self.appversion,
            'specialability': self.specialability,
            'specialtype': self.specialtype,
            'update_time': self.update_time,
            'auditresult': self.auditresult,
            'auditremark': self.auditremark
        }


# 创建所有表的函数
def create_case_tables(engine):
    """创建用例相关表"""
    Base.metadata.create_all(engine)
