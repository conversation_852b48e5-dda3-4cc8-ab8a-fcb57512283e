"""
页面和元素相关数据模型
基于Page Object设计模式，支持UIautomator2元素提取和多模态语义增强
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, JSON, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

Base = declarative_base()


@dataclass
class ElementPosition:
    """元素位置信息"""
    left: int
    top: int
    right: int
    bottom: int
    
    @property
    def center_x(self) -> int:
        return (self.left + self.right) // 2
    
    @property
    def center_y(self) -> int:
        return (self.top + self.bottom) // 2
    
    @property
    def width(self) -> int:
        return self.right - self.left
    
    @property
    def height(self) -> int:
        return self.bottom - self.top


@dataclass
class ElementAttributes:
    """元素属性信息"""
    clickable: bool = False
    editable: bool = False
    scrollable: bool = False
    checkable: bool = False
    checked: bool = False
    enabled: bool = True
    focusable: bool = False
    focused: bool = False
    selected: bool = False
    visible: bool = True


class AppInfo(Base):
    """应用信息表"""
    __tablename__ = 'app_info'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    package_name = Column(String(200), unique=True, nullable=False, comment='应用包名')
    app_name = Column(String(200), comment='应用名称')
    app_version = Column(String(50), comment='应用版本')
    main_activity = Column(String(300), comment='主Activity')
    
    # 分析状态
    analysis_status = Column(String(50), default='pending', comment='分析状态: pending, analyzing, completed, failed')
    total_pages = Column(Integer, default=0, comment='总页面数')
    analyzed_pages = Column(Integer, default=0, comment='已分析页面数')
    total_elements = Column(Integer, default=0, comment='总元素数')
    enhanced_elements = Column(Integer, default=0, comment='已增强元素数')
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    # 关联关系
    pages = relationship("UIPage", back_populates="app", cascade="all, delete-orphan")
    analysis_sessions = relationship("AnalysisSession", back_populates="app")


class AnalysisSession(Base):
    """分析会话表"""
    __tablename__ = 'analysis_sessions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(100), unique=True, nullable=False, comment='会话ID')
    app_id = Column(Integer, ForeignKey('app_info.id'), nullable=False, comment='应用ID')
    
    # 会话配置
    max_depth = Column(Integer, default=10, comment='最大遍历深度')
    max_pages = Column(Integer, default=50, comment='最大页面数')
    enable_ai_enhancement = Column(Boolean, default=True, comment='是否启用AI增强')
    
    # 会话状态
    status = Column(String(50), default='running', comment='会话状态: running, completed, failed, cancelled')
    current_depth = Column(Integer, default=0, comment='当前遍历深度')
    pages_discovered = Column(Integer, default=0, comment='发现的页面数')
    elements_extracted = Column(Integer, default=0, comment='提取的元素数')
    
    # 错误信息
    error_message = Column(Text, comment='错误信息')
    
    # 时间戳
    started_at = Column(DateTime, default=datetime.now, comment='开始时间')
    completed_at = Column(DateTime, comment='完成时间')
    
    # 关联关系
    app = relationship("AppInfo", back_populates="analysis_sessions")
    pages = relationship("UIPage", back_populates="session")


class UIPage(Base):
    """UI页面表"""
    __tablename__ = 'ui_pages'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    page_id = Column(String(100), unique=True, nullable=False, comment='页面唯一ID')
    app_id = Column(Integer, ForeignKey('app_info.id'), nullable=False, comment='应用ID')
    session_id = Column(Integer, ForeignKey('analysis_sessions.id'), comment='分析会话ID')
    
    # 页面基本信息
    page_name = Column(String(200), comment='页面名称')
    activity_name = Column(String(300), comment='Activity名称')
    page_title = Column(String(500), comment='页面标题')
    page_url = Column(String(1000), comment='页面URL（如果是WebView）')
    
    # 页面特征
    page_hash = Column(String(64), comment='页面结构哈希值')
    key_elements_hash = Column(String(64), comment='关键元素哈希值')
    
    # 页面状态
    is_analyzed = Column(Boolean, default=False, comment='是否已分析')
    is_enhanced = Column(Boolean, default=False, comment='是否已语义增强')
    element_count = Column(Integer, default=0, comment='元素数量')
    actionable_element_count = Column(Integer, default=0, comment='可操作元素数量')
    
    # 页面层级和导航
    depth_level = Column(Integer, default=0, comment='页面层级深度')
    parent_page_id = Column(String(100), comment='父页面ID')
    
    # 文件路径
    screenshot_path = Column(String(500), comment='截图文件路径')
    xml_dump_path = Column(String(500), comment='XML文件路径')
    
    # 页面元数据
    page_metadata = Column(JSON, comment='页面元数据')
    
    # 时间戳
    discovered_at = Column(DateTime, default=datetime.now, comment='发现时间')
    analyzed_at = Column(DateTime, comment='分析时间')
    enhanced_at = Column(DateTime, comment='增强时间')
    
    # 关联关系
    app = relationship("AppInfo", back_populates="pages")
    session = relationship("AnalysisSession", back_populates="pages")
    elements = relationship("UIElement", back_populates="page", cascade="all, delete-orphan")
    navigations_from = relationship("PageNavigation", foreign_keys="PageNavigation.from_page_id", back_populates="from_page")
    navigations_to = relationship("PageNavigation", foreign_keys="PageNavigation.to_page_id", back_populates="to_page")


class UIElement(Base):
    """UI元素表"""
    __tablename__ = 'ui_elements'

    id = Column(Integer, primary_key=True, autoincrement=True)
    element_id = Column(String(100), nullable=False, comment='元素唯一ID')
    page_id = Column(Integer, ForeignKey('ui_pages.id'), nullable=False, comment='页面ID')

    # 元素基本信息
    element_type = Column(String(100), comment='元素类型')
    class_name = Column(String(200), comment='类名')
    resource_id = Column(String(200), comment='资源ID')
    text = Column(Text, comment='元素文本')
    content_desc = Column(Text, comment='内容描述')

    # 元素位置信息（JSON格式存储ElementPosition）
    position = Column(JSON, comment='元素位置信息')

    # 元素属性信息（JSON格式存储ElementAttributes）
    attributes = Column(JSON, comment='元素属性信息')

    # 元素层级信息
    xpath = Column(Text, comment='XPath路径')
    hierarchy_index = Column(Integer, comment='层级索引')
    parent_element_id = Column(String(100), comment='父元素ID')

    # AI语义增强信息
    semantic_description = Column(Text, comment='语义描述')
    expected_action = Column(String(200), comment='预期操作')
    element_category = Column(String(100), comment='元素分类')
    confidence_score = Column(Float, comment='置信度分数')
    visual_context = Column(Text, comment='视觉上下文描述')
    interaction_hints = Column(Text, comment='交互提示')

    # 元素状态
    is_actionable = Column(Boolean, default=False, comment='是否可操作')
    is_enhanced = Column(Boolean, default=False, comment='是否已语义增强')

    # 元素元数据
    element_metadata = Column(JSON, comment='元素元数据')

    # 时间戳
    extracted_at = Column(DateTime, default=datetime.now, comment='提取时间')
    enhanced_at = Column(DateTime, comment='增强时间')

    # 关联关系
    page = relationship("UIPage", back_populates="elements")

    def is_actionable_element(self) -> bool:
        """判断元素是否可操作"""
        if not self.attributes:
            return False
        attrs = self.attributes if isinstance(self.attributes, dict) else {}
        return attrs.get('clickable', False) or attrs.get('editable', False) or attrs.get('scrollable', False)


class PageNavigation(Base):
    """页面导航关系表"""
    __tablename__ = 'page_navigations'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    from_page_id = Column(Integer, ForeignKey('ui_pages.id'), nullable=False, comment='源页面ID')
    to_page_id = Column(Integer, ForeignKey('ui_pages.id'), nullable=False, comment='目标页面ID')
    
    # 导航信息
    trigger_element_id = Column(String(100), comment='触发元素ID')
    navigation_action = Column(String(100), comment='导航操作: click, swipe, back, etc.')
    navigation_description = Column(Text, comment='导航描述')
    
    # 导航元数据
    navigation_metadata = Column(JSON, comment='导航元数据')
    
    # 时间戳
    discovered_at = Column(DateTime, default=datetime.now, comment='发现时间')
    
    # 关联关系
    from_page = relationship("UIPage", foreign_keys=[from_page_id], back_populates="navigations_from")
    to_page = relationship("UIPage", foreign_keys=[to_page_id], back_populates="navigations_to")


# 创建所有表的函数
def create_page_tables(engine):
    """创建页面相关表"""
    Base.metadata.create_all(engine)
