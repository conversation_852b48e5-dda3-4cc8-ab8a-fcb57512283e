"""
新系统配置文件
基于Midscene.js+AutoGen框架的多智能体系统配置
"""
import os
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = "localhost"
    port: int = 3306
    username: str = "root"
    password: str = "root123456"
    database: str = "midscene_autogen_system"
    charset: str = "utf8mb4"
    
    @property
    def connection_url(self) -> str:
        """获取数据库连接URL"""
        return f"mysql+pymysql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}?charset={self.charset}"


@dataclass
class AutoGenConfig:
    """AutoGen智能体配置"""
    # 主要模型配置 - 使用Deepseek或其他支持的模型
    model_name: str = "deepseek-chat"
    api_key: str = os.getenv("DEEPSEEK_API_KEY", "")
    base_url: str = "https://api.deepseek.com"
    
    # 多模态模型配置
    multimodal_model: str = "qwen-vl-plus"
    multimodal_api_key: str = os.getenv("QWEN_API_KEY", "")
    multimodal_base_url: str = "https://dashscope.aliyuncs.com/api/v1"
    
    # 智能体配置
    max_retries: int = 3
    timeout: int = 120
    temperature: float = 0.1
    max_tokens: int = 4000


@dataclass
class UIAutomator2Config:
    """UIautomator2配置"""
    device_id: Optional[str] = None
    connection_timeout: int = 30
    operation_timeout: int = 10
    retry_count: int = 3
    wait_after_action: float = 1.0
    screenshot_quality: int = 80
    xml_compression: bool = True


@dataclass
class MidsceneConfig:
    """Midscene.js配置"""
    # Android设备配置
    device_connection_timeout: int = 30
    app_launch_timeout: int = 60
    action_timeout: int = 30
    
    # AI操作配置
    ai_action_context: str = "If any location, permission, user agreement, etc. popup, click agree. If login page pops up, close it."
    ai_confidence_threshold: float = 0.8
    
    # 脚本生成配置
    script_template_version: str = "v1.0"
    include_error_handling: bool = True
    include_logging: bool = True


@dataclass
class PathConfig:
    """路径配置"""
    project_root: Path = Path(__file__).parent.parent
    new_backend_dir: Path = project_root / "new_backend"
    tmp_dir: Path = new_backend_dir / "tmp"
    docs_dir: Path = new_backend_dir / "docs"
    
    # 数据目录
    data_dir: Path = new_backend_dir / "data"
    screenshots_dir: Path = tmp_dir / "screenshots"
    xml_files_dir: Path = tmp_dir / "xml_files"
    logs_dir: Path = tmp_dir / "logs"
    
    # 脚本输出目录
    scripts_output_dir: Path = project_root / "generated_scripts"
    
    def __post_init__(self):
        """创建必要的目录"""
        for dir_path in [
            self.tmp_dir, self.docs_dir, self.data_dir,
            self.screenshots_dir, self.xml_files_dir, self.logs_dir,
            self.scripts_output_dir
        ]:
            dir_path.mkdir(parents=True, exist_ok=True)


@dataclass
class AgentConfig:
    """智能体特定配置"""
    # 页面元素提取智能体
    element_extraction_batch_size: int = 50
    semantic_enhancement_enabled: bool = True
    
    # 页面遍历智能体
    max_traversal_depth: int = 10
    max_pages_per_app: int = 50
    page_similarity_threshold: float = 0.85
    
    # 用例分析智能体
    case_analysis_detail_level: str = "detailed"  # basic, detailed, comprehensive
    action_extraction_enabled: bool = True
    assertion_generation_enabled: bool = True
    
    # 脚本生成智能体
    script_optimization_enabled: bool = True
    code_style_check_enabled: bool = True
    
    # 脚本review智能体
    review_criteria_strict: bool = True
    auto_fix_enabled: bool = True


class SystemConfig:
    """系统配置管理器"""
    
    def __init__(self):
        self.database = DatabaseConfig()
        self.autogen = AutoGenConfig()
        self.uiautomator2 = UIAutomator2Config()
        self.midscene = MidsceneConfig()
        self.paths = PathConfig()
        self.agents = AgentConfig()
        
        # 从环境变量加载配置
        self._load_from_env()
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        # 数据库配置
        if os.getenv("DB_HOST"):
            self.database.host = os.getenv("DB_HOST")
        if os.getenv("DB_PORT"):
            self.database.port = int(os.getenv("DB_PORT"))
        if os.getenv("DB_USERNAME"):
            self.database.username = os.getenv("DB_USERNAME")
        if os.getenv("DB_PASSWORD"):
            self.database.password = os.getenv("DB_PASSWORD")
        if os.getenv("DB_DATABASE"):
            self.database.database = os.getenv("DB_DATABASE")
        
        # AutoGen配置
        if os.getenv("AUTOGEN_MODEL"):
            self.autogen.model_name = os.getenv("AUTOGEN_MODEL")
        if os.getenv("AUTOGEN_API_KEY"):
            self.autogen.api_key = os.getenv("AUTOGEN_API_KEY")
        if os.getenv("AUTOGEN_BASE_URL"):
            self.autogen.base_url = os.getenv("AUTOGEN_BASE_URL")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "database": self.database.__dict__,
            "autogen": self.autogen.__dict__,
            "uiautomator2": self.uiautomator2.__dict__,
            "midscene": self.midscene.__dict__,
            "paths": {k: str(v) for k, v in self.paths.__dict__.items()},
            "agents": self.agents.__dict__
        }


# 全局配置实例
config = SystemConfig()
