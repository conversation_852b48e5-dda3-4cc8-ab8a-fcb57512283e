"""
脚本审查智能体
自动脚本开发专家智能体，对生成的脚本进行review和优化
"""
import logging
from typing import Dict, Any, List, Optional, Tuple
import re
import json

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage
from autogen_core import CancellationToken

from backend.agents.case_info_agent import CaseElementInfo
from example.llms import deepseek_model_client

logger = logging.getLogger(__name__)


class ScriptReviewAgent:
    """脚本审查智能体"""
    
    def __init__(self):
        """初始化审查智能体"""
        self.agent = self._create_agent()
        
    def _create_agent(self) -> AssistantAgent:
        """创建AutoGen审查智能体"""
        system_message = """
你是一位资深的自动化测试开发专家，专门负责审查和优化Android自动化测试脚本。

你的职责：
1. 全面审查脚本的完整性和正确性
2. 验证是否完全覆盖了测试用例的所有步骤
3. 检查midsence.js API使用是否正确
4. 评估代码质量和最佳实践
5. 提供具体的改进建议
6. 确保脚本可以直接运行

审查标准：
- 语法正确性：TypeScript语法无误
- API正确性：midsence.js API调用正确
- 覆盖完整性：覆盖所有测试步骤和验证点
- 错误处理：包含适当的异常处理
- 代码质量：结构清晰，注释完整
- 性能优化：合理的等待时间和操作顺序
- 可维护性：代码易读易维护

请提供详细的审查报告，包含：
1. 总体评分（1-100分）
2. 发现的问题列表
3. 改进建议
4. 优化后的代码（如需要）

如果脚本质量良好无需修改，请回复"APPROVED"。
"""
        
        return AssistantAgent(
            name="script_review_expert",
            model_client=deepseek_model_client(),
            system_message=system_message
        )
    
    async def review_script(self, script_content: str, case_info: CaseElementInfo, 
                          generation_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        审查生成的脚本
        
        Args:
            script_content: 脚本内容
            case_info: 原始用例信息
            generation_metadata: 生成过程元数据
            
        Returns:
            Dict[str, Any]: 审查结果
        """
        try:
            logger.info(f"开始审查脚本: {case_info.test_case.tcid}")

            # 1. 基础检查
            basic_check = self._perform_basic_checks(script_content)

            # 2. 使用智能体进行深度审查
            ai_review = await self._perform_ai_review(script_content, case_info)

            # 3. 覆盖率检查
            coverage_check = self._check_test_coverage(script_content, case_info)

            # 4. 生成最终审查报告
            review_result = self._generate_review_report(
                basic_check, ai_review, coverage_check, script_content, case_info
            )

            logger.info(f"脚本审查完成，总分: {review_result['overall_score']}")
            return review_result
            
        except Exception as e:
            logger.error(f"脚本审查失败: {e}")
            raise
    
    def _perform_basic_checks(self, script_content: str) -> Dict[str, Any]:
        """执行基础检查"""
        checks = {
            "syntax_check": True,
            "import_check": False,
            "function_check": False,
            "async_check": False,
            "error_handling_check": False,
            "issues": []
        }
        
        try:
            # 检查导入语句
            if "import" in script_content and ("@midscene" in script_content or "Page" in script_content):
                checks["import_check"] = True
            else:
                checks["issues"].append("缺少必要的导入语句")
            
            # 检查函数定义
            if "test(" in script_content or "describe(" in script_content or "async" in script_content:
                checks["function_check"] = True
            else:
                checks["issues"].append("缺少测试函数定义")
            
            # 检查异步操作
            if "async" in script_content and "await" in script_content:
                checks["async_check"] = True
            else:
                checks["issues"].append("缺少异步操作处理")
            
            # 检查错误处理
            if "try" in script_content and "catch" in script_content:
                checks["error_handling_check"] = True
            else:
                checks["issues"].append("建议添加错误处理")
            
        except Exception as e:
            checks["syntax_check"] = False
            checks["issues"].append(f"语法检查失败: {e}")
        
        return checks
    
    async def _perform_ai_review(self, script_content: str, case_info: CaseElementInfo) -> Dict[str, Any]:
        """使用AI进行深度审查"""
        try:
            # 构建审查任务
            review_task = self._build_review_task(script_content, case_info)
            
            # 运行智能体审查
            result = await self.agent.run(task=review_task)
            
            if result and result.messages:
                review_content = result.messages[-1].content
                return self._parse_ai_review_result(review_content)
            else:
                logger.warning("AI审查返回空结果")
                return {"score": 70, "comments": "AI审查未能完成", "suggestions": []}
                
        except Exception as e:
            logger.error(f"AI审查失败: {e}")
            return {"score": 60, "comments": f"AI审查异常: {e}", "suggestions": []}
    
    def _build_review_task(self, script_content: str, case_info: CaseElementInfo) -> str:
        """构建审查任务"""
        task = f"""
请审查以下自动化测试脚本：

## 原始测试用例
- 用例ID: {case_info.test_case.tcid}
- 测试步骤: {case_info.test_case.steps}
- 预期结果: {case_info.test_case.expect_result}
- 前置条件: {case_info.test_case.pre_condition or '无'}

## 生成的脚本
```typescript
{script_content}
```

## 可用的UI元素信息
{json.dumps([{
    "type": elem.element_type,
    "text": elem.text or "",
    "resource_id": elem.resource_id or "",
    "description": elem.ai_description or elem.description or ""
} for elem in case_info.related_elements[:5]], ensure_ascii=False, indent=2)}

请从以下方面进行全面审查：
1. 脚本是否完整覆盖了所有测试步骤？
2. midsence.js API使用是否正确？
3. 代码结构和质量如何？
4. 是否包含适当的等待和验证？
5. 错误处理是否完善？
6. 是否可以直接运行？

请提供：
- 总体评分（1-100）
- 发现的具体问题
- 改进建议
- 如果需要，提供优化后的代码片段
"""
        return task
    
    def _parse_ai_review_result(self, review_content: str) -> Dict[str, Any]:
        """解析AI审查结果"""
        try:
            # 尝试提取评分
            score_match = re.search(r'评分[：:]\s*(\d+)', review_content)
            score = int(score_match.group(1)) if score_match else 75
            
            # 提取问题和建议
            issues = []
            suggestions = []
            
            lines = review_content.split('\n')
            current_section = None
            
            for line in lines:
                line = line.strip()
                if '问题' in line or 'Issues' in line:
                    current_section = 'issues'
                elif '建议' in line or 'Suggestions' in line:
                    current_section = 'suggestions'
                elif line.startswith('-') or line.startswith('•'):
                    if current_section == 'issues':
                        issues.append(line[1:].strip())
                    elif current_section == 'suggestions':
                        suggestions.append(line[1:].strip())
            
            return {
                "score": score,
                "comments": review_content,
                "issues": issues,
                "suggestions": suggestions,
                "approved": score >= 80 and "APPROVED" in review_content
            }
            
        except Exception as e:
            logger.error(f"解析AI审查结果失败: {e}")
            return {
                "score": 70,
                "comments": review_content,
                "issues": ["解析审查结果失败"],
                "suggestions": ["请人工检查审查结果"],
                "approved": False
            }
    
    def _check_test_coverage(self, script_content: str, case_info: CaseElementInfo) -> Dict[str, Any]:
        """检查测试覆盖率"""
        coverage = {
            "step_coverage": 0,
            "element_coverage": 0,
            "verification_coverage": 0,
            "missing_steps": [],
            "missing_verifications": []
        }
        
        try:
            # 分析测试步骤
            test_steps = case_info.test_case.steps.lower()
            script_lower = script_content.lower()

            # 检查常见操作覆盖
            operations = {
                "点击": ["click", "tap"],
                "输入": ["input", "type"],
                "等待": ["wait", "sleep"],
                "验证": ["assert", "expect", "verify"],
                "滑动": ["swipe", "scroll"]
            }

            covered_operations = 0
            total_operations = 0

            for chinese_op, english_ops in operations.items():
                if chinese_op in test_steps:
                    total_operations += 1
                    if any(eng_op in script_lower for eng_op in english_ops):
                        covered_operations += 1
                    else:
                        coverage["missing_steps"].append(f"缺少{chinese_op}操作的实现")

            if total_operations > 0:
                coverage["step_coverage"] = (covered_operations / total_operations) * 100

            # 检查验证点覆盖
            expected_result = (case_info.test_case.expect_result or "").lower()
            if "验证" in expected_result or "检查" in expected_result:
                if "assert" in script_lower or "expect" in script_lower:
                    coverage["verification_coverage"] = 100
                else:
                    coverage["verification_coverage"] = 0
                    coverage["missing_verifications"].append("缺少结果验证")
            else:
                coverage["verification_coverage"] = 100
            
            # 检查元素使用覆盖率
            used_elements = 0
            for element in case_info.related_elements[:5]:
                if element.text and element.text.lower() in script_lower:
                    used_elements += 1
                elif element.resource_id and element.resource_id.lower() in script_lower:
                    used_elements += 1
            
            if case_info.related_elements:
                coverage["element_coverage"] = (used_elements / min(len(case_info.related_elements), 5)) * 100
            
        except Exception as e:
            logger.error(f"覆盖率检查失败: {e}")
        
        return coverage
    
    def _generate_review_report(self, basic_check: Dict[str, Any], ai_review: Dict[str, Any], 
                              coverage_check: Dict[str, Any], script_content: str, 
                              case_info: CaseElementInfo) -> Dict[str, Any]:
        """生成最终审查报告"""
        # 计算总体评分
        basic_score = 100 - len(basic_check["issues"]) * 10
        ai_score = ai_review.get("score", 70)
        coverage_score = (coverage_check["step_coverage"] + coverage_check["verification_coverage"]) / 2
        
        overall_score = (basic_score * 0.3 + ai_score * 0.5 + coverage_score * 0.2)
        overall_score = max(0, min(100, overall_score))
        
        # 确定是否通过审查
        approved = (
            overall_score >= 75 and
            len(basic_check["issues"]) <= 2 and
            coverage_check["step_coverage"] >= 60 and
            ai_review.get("approved", False)
        )
        
        # 收集所有问题和建议
        all_issues = basic_check["issues"] + ai_review.get("issues", []) + coverage_check["missing_steps"]
        all_suggestions = ai_review.get("suggestions", []) + coverage_check["missing_verifications"]
        
        return {
            "overall_score": round(overall_score, 1),
            "approved": approved,
            "basic_check": basic_check,
            "ai_review": ai_review,
            "coverage_check": coverage_check,
            "issues": all_issues,
            "suggestions": all_suggestions,
            "review_summary": {
                "total_issues": len(all_issues),
                "critical_issues": len([issue for issue in all_issues if "缺少" in issue or "失败" in issue]),
                "coverage_score": round(coverage_score, 1),
                "quality_score": round((basic_score + ai_score) / 2, 1)
            },
            "final_script": script_content,
            "needs_revision": not approved
        }


async def main():
    """测试函数"""
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建审查智能体
    reviewer = ScriptReviewAgent()
    
    # 测试脚本
    test_script = '''
import { Page } from '@midscene/web';

describe('登录测试', () => {
  let page: Page;

  beforeAll(async () => {
    page = new Page();
  });

  test('用户登录', async () => {
    await page.click('登录按钮');
    await page.input('用户名输入框', '<EMAIL>');
    await page.input('密码输入框', '123456');
    await page.click('确认登录');
    await page.assert('登录成功');
  });
});
'''
    
    print("脚本审查智能体创建成功")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
