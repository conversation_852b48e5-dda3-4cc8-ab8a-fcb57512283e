"""
单脚本审查智能体
实现脚本质量检查，确保前置条件、操作步骤、预期结果完整覆盖
"""
import os
import sys
import json
import asyncio
import re
from typing import Dict, Any, Optional, List
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加相对路径导入
sys.path.append(str(Path(__file__).parent.parent))

from config import config
from utils.autogen_client import AutoGenClientManager
from utils.database_manager import DatabaseManager
from new_backend.models.script_models import GeneratedScript, ScriptReview
from new_backend.models.case_models import TestCase, CaseAnalysisResult


class ScriptReviewAgent:
    """单脚本审查智能体"""
    
    def __init__(self):
        """初始化脚本审查智能体"""
        self.config = config
        self.agent_name = "script_review_agent"
        
        # 核心组件
        self.autogen_client = AutoGenClientManager()
        self.db_manager = DatabaseManager()
        
        # 智能体
        self.review_agent = None
        
        # 审查规则
        self.review_rules = self._load_review_rules()
        
        # 初始化智能体
        self._initialize_agent()
    
    def _initialize_agent(self):
        """初始化脚本审查智能体"""
        try:
            logger.info("初始化脚本审查智能体...")
            
            # 创建脚本审查智能体
            self.review_agent = self.autogen_client.create_text_agent(
                name="script_review_agent",
                system_message=self._get_script_review_prompt()
            )
            
            if self.review_agent:
                logger.info("脚本审查智能体初始化成功")
            else:
                logger.warning("脚本审查智能体初始化失败，将使用降级模式")
            
        except Exception as e:
            logger.error(f"初始化脚本审查智能体失败: {e}")
            self.review_agent = None
    
    def _get_script_review_prompt(self) -> str:
        """获取脚本审查提示词"""
        return """你是一个专业的Android自动化测试脚本审查专家，专门审查基于Midscene.js框架的TypeScript测试脚本。

审查任务：对生成的自动化测试脚本进行全面的质量检查和评估。

审查维度：

1. **语法正确性**
   - TypeScript语法检查
   - 导入语句正确性
   - 函数调用语法
   - 异步操作处理

2. **逻辑完整性**
   - 前置条件覆盖
   - 操作步骤完整性
   - 预期结果验证
   - 异常处理逻辑

3. **Midscene.js最佳实践**
   - AI操作描述清晰性
   - aiAction使用规范
   - aiAssert断言完整
   - aiWaitFor等待合理
   - 设备连接管理

4. **可维护性**
   - 代码结构清晰
   - 注释充分详细
   - 变量命名规范
   - 函数拆分合理

5. **性能优化**
   - 等待时间合理
   - 操作顺序优化
   - 资源使用效率
   - 错误恢复机制

6. **测试覆盖度**
   - 用例步骤覆盖
   - 断言点覆盖
   - 边界条件考虑
   - 异常场景处理

返回格式：
{
    "overall_score": 85,
    "review_result": "pass",
    "quality_metrics": {
        "syntax_score": 90,
        "logic_score": 85,
        "best_practices_score": 80,
        "maintainability_score": 85,
        "performance_score": 80,
        "coverage_score": 90
    },
    "identified_issues": [
        {
            "severity": "high",
            "category": "logic",
            "description": "缺少关键断言验证",
            "line_number": 45,
            "suggestion": "添加页面跳转验证"
        }
    ],
    "optimization_suggestions": [
        {
            "category": "performance",
            "description": "优化等待时间",
            "current_code": "await sleep(5000);",
            "suggested_code": "await agent.aiWaitFor('页面加载完成');",
            "benefit": "提高测试执行效率和稳定性"
        }
    ],
    "compliance_check": {
        "midscene_apis": true,
        "error_handling": true,
        "device_management": true,
        "ai_descriptions": false
    },
    "review_summary": "脚本整体质量良好，建议优化AI操作描述的清晰度",
    "recommendations": [
        "添加更详细的操作描述",
        "完善异常处理逻辑",
        "优化等待策略"
    ]
}

请提供详细的审查结果和具体的改进建议。"""
    
    async def review_script(self, script: GeneratedScript, 
                          test_case: Optional[TestCase] = None,
                          analysis_result: Optional[CaseAnalysisResult] = None) -> Dict[str, Any]:
        """
        审查测试脚本
        
        Args:
            script: 生成的脚本对象
            test_case: 原始测试用例（可选）
            analysis_result: 用例分析结果（可选）
            
        Returns:
            Dict[str, Any]: 审查结果
        """
        try:
            logger.info(f"开始审查脚本: {script.script_name}")
            
            # 准备审查输入
            review_input = self._prepare_review_input(script, test_case, analysis_result)
            
            # 使用AI智能体审查
            if self.review_agent and self.autogen_client.is_available():
                review_result = await self._review_with_ai(review_input)
            else:
                # 降级处理
                logger.warning("使用降级模式审查脚本")
                review_result = self._review_with_rules(script, test_case, analysis_result)
            
            # 验证和完善审查结果
            validated_result = self._validate_review_result(review_result, script)
            
            # 保存审查结果
            saved_review = self._save_review_result(script, validated_result)
            
            logger.info(f"脚本审查完成: {script.script_name}")
            return {
                'success': True,
                'review_result': validated_result,
                'saved_review_id': saved_review.id if saved_review else None
            }
            
        except Exception as e:
            logger.error(f"审查脚本失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _prepare_review_input(self, script: GeneratedScript, 
                            test_case: Optional[TestCase] = None,
                            analysis_result: Optional[CaseAnalysisResult] = None) -> str:
        """准备审查输入"""
        try:
            input_parts = [
                "请审查以下Midscene.js自动化测试脚本：",
                "",
                "=== 脚本信息 ===",
                f"脚本名称: {script.script_name}",
                f"脚本描述: {script.script_description}",
                f"应用包名: {script.package_name}",
                f"生成方法: {script.generation_method}",
                f"Midscene版本: {script.midscene_version}",
                "",
                "=== 脚本内容 ===",
                "```typescript",
                script.script_content,
                "```",
                ""
            ]
            
            # 添加原始测试用例信息
            if test_case:
                input_parts.extend([
                    "=== 原始测试用例 ===",
                    f"用例名称: {test_case.case_name}",
                    f"测试目的: {test_case.test_purpose or '未指定'}",
                    "",
                    "前置条件:",
                    test_case.preconditions or "无特殊前置条件",
                    "",
                    "测试步骤:",
                    test_case.test_steps,
                    "",
                    "预期结果:",
                    test_case.expected_results or "无明确预期结果",
                    ""
                ])
            
            # 添加分析结果信息
            if analysis_result:
                input_parts.extend([
                    "=== 用例分析结果 ===",
                    f"操作步骤数: {len(analysis_result.extracted_actions)}",
                    f"断言数: {len(analysis_result.extracted_assertions)}",
                    f"分析置信度: {analysis_result.analysis_confidence}",
                    ""
                ])
                
                if analysis_result.extracted_actions:
                    input_parts.append("提取的操作步骤:")
                    for i, action in enumerate(analysis_result.extracted_actions, 1):
                        input_parts.append(f"{i}. {action.get('action_description', '')}")
                    input_parts.append("")
                
                if analysis_result.extracted_assertions:
                    input_parts.append("提取的断言:")
                    for i, assertion in enumerate(analysis_result.extracted_assertions, 1):
                        input_parts.append(f"{i}. {assertion.get('assertion_description', '')}")
                    input_parts.append("")
            
            input_parts.extend([
                "请对脚本进行全面审查，重点关注：",
                "1. 脚本是否正确实现了测试用例的所有步骤",
                "2. 断言是否充分覆盖了预期结果",
                "3. Midscene.js API使用是否规范",
                "4. 错误处理和资源管理是否完善",
                "5. 代码质量和可维护性",
                "",
                "请返回详细的JSON格式审查结果。"
            ])
            
            return "\n".join(input_parts)
            
        except Exception as e:
            logger.error(f"准备审查输入失败: {e}")
            return f"审查脚本: {script.script_name}\n内容: {script.script_content[:500]}..."
    
    async def _review_with_ai(self, review_input: str) -> Dict[str, Any]:
        """使用AI智能体审查"""
        try:
            # 调用AI智能体
            result = await self.autogen_client.run_text_task(
                agent=self.review_agent,
                content=review_input
            )
            
            if result.get('success'):
                # 解析AI返回的结果
                content = result.get('content', '')
                review_data = self._parse_ai_review_result(content)
                
                if review_data:
                    return review_data
                else:
                    logger.warning("AI审查结果解析失败")
                    return self._create_empty_review_result()
            else:
                logger.warning(f"AI审查失败: {result.get('error')}")
                return self._create_empty_review_result()
                
        except Exception as e:
            logger.error(f"AI审查异常: {e}")
            return self._create_empty_review_result()
    
    def _parse_ai_review_result(self, content: str) -> Optional[Dict[str, Any]]:
        """解析AI审查结果"""
        try:
            # 提取JSON部分
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            
            if start_idx >= 0 and end_idx > start_idx:
                json_str = content[start_idx:end_idx]
                result = json.loads(json_str)
                
                # 验证必要字段
                required_fields = ['overall_score', 'review_result', 'quality_metrics']
                if all(field in result for field in required_fields):
                    return result
            
            return None
            
        except json.JSONDecodeError as e:
            logger.warning(f"解析AI审查结果JSON失败: {e}")
            return None
        except Exception as e:
            logger.warning(f"解析AI审查结果异常: {e}")
            return None
    
    def _review_with_rules(self, script: GeneratedScript, 
                         test_case: Optional[TestCase] = None,
                         analysis_result: Optional[CaseAnalysisResult] = None) -> Dict[str, Any]:
        """使用规则审查脚本（降级模式）"""
        try:
            logger.info("使用规则审查脚本")
            
            script_content = script.script_content
            issues = []
            suggestions = []
            
            # 语法检查
            syntax_score = self._check_syntax(script_content, issues)
            
            # 逻辑检查
            logic_score = self._check_logic(script_content, test_case, analysis_result, issues)
            
            # 最佳实践检查
            practices_score = self._check_best_practices(script_content, issues, suggestions)
            
            # 可维护性检查
            maintainability_score = self._check_maintainability(script_content, issues)
            
            # 性能检查
            performance_score = self._check_performance(script_content, suggestions)
            
            # 覆盖度检查
            coverage_score = self._check_coverage(script_content, test_case, analysis_result, issues)
            
            # 计算总分
            overall_score = (syntax_score + logic_score + practices_score + 
                           maintainability_score + performance_score + coverage_score) / 6
            
            # 确定审查结果
            if overall_score >= 80:
                review_result = "pass"
            elif overall_score >= 60:
                review_result = "pass_with_suggestions"
            else:
                review_result = "fail"
            
            return {
                'overall_score': round(overall_score, 1),
                'review_result': review_result,
                'quality_metrics': {
                    'syntax_score': syntax_score,
                    'logic_score': logic_score,
                    'best_practices_score': practices_score,
                    'maintainability_score': maintainability_score,
                    'performance_score': performance_score,
                    'coverage_score': coverage_score
                },
                'identified_issues': issues,
                'optimization_suggestions': suggestions,
                'compliance_check': self._check_compliance(script_content),
                'review_summary': f'基于规则的审查完成，总分: {overall_score:.1f}',
                'recommendations': self._generate_recommendations(issues, suggestions)
            }
            
        except Exception as e:
            logger.error(f"规则审查失败: {e}")
            return self._create_empty_review_result()
    
    def _check_syntax(self, script_content: str, issues: List[Dict]) -> float:
        """检查语法正确性"""
        try:
            score = 100.0
            
            # 检查基本导入
            if 'import' not in script_content:
                issues.append({
                    'severity': 'high',
                    'category': 'syntax',
                    'description': '缺少必要的导入语句',
                    'suggestion': '添加Midscene.js相关导入'
                })
                score -= 20
            
            # 检查AndroidAgent导入
            if 'AndroidAgent' not in script_content:
                issues.append({
                    'severity': 'high',
                    'category': 'syntax',
                    'description': '缺少AndroidAgent导入',
                    'suggestion': '添加AndroidAgent导入'
                })
                score -= 15
            
            # 检查async/await使用
            if 'await' in script_content and 'async' not in script_content:
                issues.append({
                    'severity': 'medium',
                    'category': 'syntax',
                    'description': '使用了await但函数未声明为async',
                    'suggestion': '将函数声明为async'
                })
                score -= 10
            
            # 检查基本语法错误
            syntax_patterns = [
                (r'}\s*{', '可能存在语法错误：连续的大括号'),
                (r';\s*;', '可能存在多余的分号'),
                (r'await\s+(?![\w\.])', 'await后缺少表达式')
            ]
            
            for pattern, description in syntax_patterns:
                if re.search(pattern, script_content):
                    issues.append({
                        'severity': 'medium',
                        'category': 'syntax',
                        'description': description,
                        'suggestion': '检查并修复语法错误'
                    })
                    score -= 5
            
            return max(score, 0)
            
        except Exception as e:
            logger.warning(f"语法检查失败: {e}")
            return 50.0
    
    def _check_logic(self, script_content: str, test_case: Optional[TestCase],
                   analysis_result: Optional[CaseAnalysisResult], issues: List[Dict]) -> float:
        """检查逻辑完整性"""
        try:
            score = 100.0
            
            # 检查设备连接
            if 'device.connect' not in script_content:
                issues.append({
                    'severity': 'high',
                    'category': 'logic',
                    'description': '缺少设备连接逻辑',
                    'suggestion': '添加await device.connect();'
                })
                score -= 20
            
            # 检查应用启动
            if 'device.launch' not in script_content and 'launch' not in script_content:
                issues.append({
                    'severity': 'high',
                    'category': 'logic',
                    'description': '缺少应用启动逻辑',
                    'suggestion': '添加应用启动代码'
                })
                score -= 20
            
            # 检查错误处理
            if 'try' not in script_content or 'catch' not in script_content:
                issues.append({
                    'severity': 'medium',
                    'category': 'logic',
                    'description': '缺少错误处理逻辑',
                    'suggestion': '添加try-catch错误处理'
                })
                score -= 15
            
            # 检查资源清理
            if 'finally' not in script_content and 'disconnect' not in script_content:
                issues.append({
                    'severity': 'medium',
                    'category': 'logic',
                    'description': '缺少资源清理逻辑',
                    'suggestion': '添加设备断开连接逻辑'
                })
                score -= 10
            
            # 检查操作步骤覆盖
            if analysis_result and analysis_result.extracted_actions:
                action_count = len(analysis_result.extracted_actions)
                ai_action_count = script_content.count('aiAction')
                
                if ai_action_count < action_count * 0.8:  # 至少覆盖80%的操作
                    issues.append({
                        'severity': 'medium',
                        'category': 'logic',
                        'description': f'操作步骤覆盖不足：期望{action_count}个，实际{ai_action_count}个',
                        'suggestion': '增加缺失的操作步骤'
                    })
                    score -= 15
            
            return max(score, 0)

        except Exception as e:
            logger.warning(f"逻辑检查失败: {e}")
            return 50.0

    def _check_best_practices(self, script_content: str, issues: List[Dict],
                            suggestions: List[Dict]) -> float:
        """检查最佳实践"""
        try:
            score = 100.0

            # 检查AI操作描述
            ai_actions = re.findall(r'aiAction\([\'"]([^\'"]*)[\'"]', script_content)
            for action in ai_actions:
                if len(action) < 5:  # 描述太短
                    suggestions.append({
                        'category': 'best_practices',
                        'description': '优化AI操作描述',
                        'current_code': f"aiAction('{action}')",
                        'suggested_code': f"aiAction('详细描述操作意图')",
                        'benefit': '提高AI理解准确性'
                    })
                    score -= 5

            # 检查等待策略
            if 'sleep(' in script_content:
                suggestions.append({
                    'category': 'best_practices',
                    'description': '使用AI等待替代固定延时',
                    'current_code': 'await sleep(3000);',
                    'suggested_code': "await agent.aiWaitFor('页面加载完成');",
                    'benefit': '提高测试稳定性和效率'
                })
                score -= 10

            # 检查断言使用
            ai_assert_count = script_content.count('aiAssert')
            if ai_assert_count == 0:
                issues.append({
                    'severity': 'medium',
                    'category': 'best_practices',
                    'description': '缺少AI断言验证',
                    'suggestion': '添加aiAssert验证关键状态'
                })
                score -= 15

            # 检查注释完整性
            comment_lines = len([line for line in script_content.split('\n') if line.strip().startswith('//')])
            total_lines = len(script_content.split('\n'))
            comment_ratio = comment_lines / max(total_lines, 1)

            if comment_ratio < 0.1:  # 注释比例低于10%
                suggestions.append({
                    'category': 'best_practices',
                    'description': '增加代码注释',
                    'benefit': '提高代码可读性和维护性'
                })
                score -= 10

            return max(score, 0)

        except Exception as e:
            logger.warning(f"最佳实践检查失败: {e}")
            return 50.0

    def _check_maintainability(self, script_content: str, issues: List[Dict]) -> float:
        """检查可维护性"""
        try:
            score = 100.0

            # 检查函数长度
            lines = script_content.split('\n')
            function_lines = 0
            in_function = False

            for line in lines:
                if 'function' in line or 'async' in line:
                    in_function = True
                    function_lines = 0
                elif in_function:
                    function_lines += 1
                    if function_lines > 100:  # 函数过长
                        issues.append({
                            'severity': 'low',
                            'category': 'maintainability',
                            'description': '函数过长，建议拆分',
                            'suggestion': '将长函数拆分为多个小函数'
                        })
                        score -= 10
                        break

            # 检查变量命名
            var_patterns = re.findall(r'(?:const|let|var)\s+(\w+)', script_content)
            for var_name in var_patterns:
                if len(var_name) < 3 or var_name.lower() == var_name.upper():
                    issues.append({
                        'severity': 'low',
                        'category': 'maintainability',
                        'description': f'变量命名不规范: {var_name}',
                        'suggestion': '使用有意义的变量名'
                    })
                    score -= 2

            # 检查硬编码值
            hardcoded_patterns = [
                r'sleep\(\d+\)',  # 硬编码延时
                r'[\'"][^\'\"]*\d{4,}[^\'\"]*[\'"]',  # 可能的硬编码ID
            ]

            for pattern in hardcoded_patterns:
                matches = re.findall(pattern, script_content)
                if matches:
                    issues.append({
                        'severity': 'low',
                        'category': 'maintainability',
                        'description': '存在硬编码值',
                        'suggestion': '考虑使用配置或常量'
                    })
                    score -= 5
                    break

            return max(score, 0)

        except Exception as e:
            logger.warning(f"可维护性检查失败: {e}")
            return 50.0

    def _check_performance(self, script_content: str, suggestions: List[Dict]) -> float:
        """检查性能优化"""
        try:
            score = 100.0

            # 检查不必要的等待
            sleep_matches = re.findall(r'sleep\((\d+)\)', script_content)
            for sleep_time in sleep_matches:
                if int(sleep_time) > 5000:  # 超过5秒的等待
                    suggestions.append({
                        'category': 'performance',
                        'description': '优化长时间等待',
                        'current_code': f'sleep({sleep_time})',
                        'suggested_code': "aiWaitFor('具体等待条件')",
                        'benefit': '减少测试执行时间'
                    })
                    score -= 10

            # 检查重复操作
            ai_actions = re.findall(r'aiAction\([\'"]([^\'"]*)[\'"]', script_content)
            action_counts = {}
            for action in ai_actions:
                action_counts[action] = action_counts.get(action, 0) + 1

            for action, count in action_counts.items():
                if count > 3:  # 重复操作超过3次
                    suggestions.append({
                        'category': 'performance',
                        'description': '考虑优化重复操作',
                        'benefit': '提高代码复用性'
                    })
                    score -= 5
                    break

            return max(score, 0)

        except Exception as e:
            logger.warning(f"性能检查失败: {e}")
            return 50.0

    def _check_coverage(self, script_content: str, test_case: Optional[TestCase],
                      analysis_result: Optional[CaseAnalysisResult], issues: List[Dict]) -> float:
        """检查测试覆盖度"""
        try:
            score = 100.0

            # 检查断言覆盖
            if analysis_result and analysis_result.extracted_assertions:
                expected_assertions = len(analysis_result.extracted_assertions)
                actual_assertions = script_content.count('aiAssert')

                coverage_ratio = actual_assertions / max(expected_assertions, 1)
                if coverage_ratio < 0.8:  # 断言覆盖率低于80%
                    issues.append({
                        'severity': 'medium',
                        'category': 'coverage',
                        'description': f'断言覆盖不足：期望{expected_assertions}个，实际{actual_assertions}个',
                        'suggestion': '添加缺失的断言验证'
                    })
                    score -= 20

            # 检查前置条件覆盖
            if test_case and test_case.preconditions:
                # 简单检查是否有应用启动等前置操作
                if 'launch' not in script_content:
                    issues.append({
                        'severity': 'medium',
                        'category': 'coverage',
                        'description': '前置条件覆盖不足',
                        'suggestion': '确保满足所有前置条件'
                    })
                    score -= 15

            # 检查异常场景覆盖
            if 'catch' not in script_content:
                issues.append({
                    'severity': 'low',
                    'category': 'coverage',
                    'description': '缺少异常场景处理',
                    'suggestion': '添加异常情况的处理逻辑'
                })
                score -= 10

            return max(score, 0)

        except Exception as e:
            logger.warning(f"覆盖度检查失败: {e}")
            return 50.0

    def _check_compliance(self, script_content: str) -> Dict[str, bool]:
        """检查合规性"""
        try:
            return {
                'midscene_apis': any(api in script_content for api in ['aiAction', 'aiAssert', 'aiWaitFor']),
                'error_handling': 'try' in script_content and 'catch' in script_content,
                'device_management': 'device.connect' in script_content,
                'ai_descriptions': 'aiAction(' in script_content and len(re.findall(r'aiAction\([\'"]([^\'"]*)[\'"]', script_content)) > 0
            }
        except Exception as e:
            logger.warning(f"合规性检查失败: {e}")
            return {'midscene_apis': False, 'error_handling': False, 'device_management': False, 'ai_descriptions': False}

    def _generate_recommendations(self, issues: List[Dict], suggestions: List[Dict]) -> List[str]:
        """生成改进建议"""
        try:
            recommendations = []

            # 基于问题严重程度生成建议
            high_issues = [issue for issue in issues if issue.get('severity') == 'high']
            medium_issues = [issue for issue in issues if issue.get('severity') == 'medium']

            if high_issues:
                recommendations.append("优先修复高严重性问题")
            if medium_issues:
                recommendations.append("处理中等严重性问题")
            if suggestions:
                recommendations.append("考虑性能和最佳实践优化建议")

            # 添加通用建议
            if not recommendations:
                recommendations.append("脚本质量良好，可以考虑进一步优化")

            return recommendations

        except Exception as e:
            logger.warning(f"生成建议失败: {e}")
            return ["请手动检查脚本质量"]

    def _create_empty_review_result(self) -> Dict[str, Any]:
        """创建空的审查结果"""
        return {
            'overall_score': 0.0,
            'review_result': 'fail',
            'quality_metrics': {
                'syntax_score': 0,
                'logic_score': 0,
                'best_practices_score': 0,
                'maintainability_score': 0,
                'performance_score': 0,
                'coverage_score': 0
            },
            'identified_issues': [{'severity': 'high', 'category': 'system', 'description': '审查过程失败'}],
            'optimization_suggestions': [],
            'compliance_check': {'midscene_apis': False, 'error_handling': False, 'device_management': False, 'ai_descriptions': False},
            'review_summary': '审查失败',
            'recommendations': ['请检查脚本内容和审查配置']
        }

    def _validate_review_result(self, review_result: Dict[str, Any], script: GeneratedScript) -> Dict[str, Any]:
        """验证和完善审查结果"""
        try:
            # 确保必要字段存在
            if 'overall_score' not in review_result:
                review_result['overall_score'] = 50.0
            if 'review_result' not in review_result:
                review_result['review_result'] = 'unknown'
            if 'quality_metrics' not in review_result:
                review_result['quality_metrics'] = {}

            # 添加审查元数据
            review_result['review_metadata'] = {
                'script_id': script.script_id,
                'script_name': script.script_name,
                'review_time': datetime.now().isoformat(),
                'reviewer_agent': self.agent_name,
                'script_length': len(script.script_content),
                'lines_of_code': len(script.script_content.split('\n'))
            }

            return review_result

        except Exception as e:
            logger.error(f"验证审查结果失败: {e}")
            return review_result

    def _save_review_result(self, script: GeneratedScript, review_result: Dict[str, Any]) -> Optional[ScriptReview]:
        """保存审查结果到数据库"""
        try:
            # 创建审查结果对象
            script_review = ScriptReview(
                script_id=script.id,
                review_session_id=f"review_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                overall_score=review_result.get('overall_score', 0.0),
                review_result=review_result.get('review_result', 'unknown'),
                quality_metrics=review_result.get('quality_metrics', {}),
                identified_issues=review_result.get('identified_issues', []),
                optimization_suggestions=review_result.get('optimization_suggestions', []),
                compliance_check=review_result.get('compliance_check', {}),
                review_summary=review_result.get('review_summary', ''),
                recommendations=review_result.get('recommendations', []),
                reviewer_agent=self.agent_name,
                review_model=self.config.autogen.model_name
            )

            # 保存到数据库
            saved_review = self.db_manager.save_script_review(script_review)

            if saved_review:
                # 更新脚本状态
                script.status = 'reviewed'
                script.updated_at = datetime.now()
                self.db_manager.save_generated_script(script)

                logger.info(f"脚本审查结果保存成功: {script.script_name}")

            return saved_review

        except Exception as e:
            logger.error(f"保存审查结果失败: {e}")
            return None

    def get_review_summary(self, review_result: Dict[str, Any]) -> Dict[str, Any]:
        """获取审查摘要"""
        try:
            quality_metrics = review_result.get('quality_metrics', {})
            issues = review_result.get('identified_issues', [])
            suggestions = review_result.get('optimization_suggestions', [])

            # 统计问题严重程度
            issue_severity_counts = {}
            for issue in issues:
                severity = issue.get('severity', 'unknown')
                issue_severity_counts[severity] = issue_severity_counts.get(severity, 0) + 1

            # 统计建议类别
            suggestion_categories = {}
            for suggestion in suggestions:
                category = suggestion.get('category', 'unknown')
                suggestion_categories[category] = suggestion_categories.get(category, 0) + 1

            summary = {
                'overall_score': review_result.get('overall_score', 0.0),
                'review_result': review_result.get('review_result', 'unknown'),
                'quality_breakdown': quality_metrics,
                'issue_summary': {
                    'total_issues': len(issues),
                    'by_severity': issue_severity_counts
                },
                'suggestion_summary': {
                    'total_suggestions': len(suggestions),
                    'by_category': suggestion_categories
                },
                'compliance_status': review_result.get('compliance_check', {}),
                'pass_threshold': 80.0,
                'needs_improvement': review_result.get('overall_score', 0.0) < 80.0
            }

            return summary

        except Exception as e:
            logger.error(f"生成审查摘要失败: {e}")
            return {}

    def _load_review_rules(self) -> Dict[str, Any]:
        """加载审查规则"""
        try:
            # 这里可以从配置文件或数据库加载审查规则
            return {
                'syntax_weight': 0.2,
                'logic_weight': 0.25,
                'best_practices_weight': 0.2,
                'maintainability_weight': 0.15,
                'performance_weight': 0.1,
                'coverage_weight': 0.1,
                'pass_threshold': 80.0,
                'warning_threshold': 60.0
            }
        except Exception as e:
            logger.warning(f"加载审查规则失败: {e}")
            return {}

    def cleanup(self):
        """清理资源"""
        try:
            if self.autogen_client:
                self.autogen_client.cleanup()

            logger.info("脚本审查智能体资源清理完成")

        except Exception as e:
            logger.warning(f"清理脚本审查智能体资源失败: {e}")
