"""
脚本生成系统数据模型
用于存储测试用例、脚本信息和模块映射关系
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import Dict, Any, Optional

Base = declarative_base()


class BusinessModule(Base):
    """业务模块表"""
    __tablename__ = 'business_modules'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    module_name = Column(String(100), unique=True, nullable=False, comment='业务模块名称')
    description = Column(Text, comment='模块描述')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    # 关联关系
    test_cases = relationship("TestCase", back_populates="business_module")
    package_components = relationship("PackageComponent", back_populates="business_module")


class PackageComponent(Base):
    """包组件映射表"""
    __tablename__ = 'package_components'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    business_module_id = Column(Integer, ForeignKey('business_modules.id'), nullable=False, comment='业务模块ID')
    package_name = Column(String(500), nullable=False, comment='包名')
    component_name = Column(String(500), nullable=False, comment='组件名称')
    description = Column(Text, comment='组件描述')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    # 关联关系
    business_module = relationship("BusinessModule", back_populates="package_components")


class TestCase(Base):
    """测试用例表"""
    __tablename__ = 'test_cases'

    id = Column(Integer, primary_key=True, autoincrement=True)
    # 基础字段（对应Excel的必填字段）
    tcid = Column(String(100), unique=True, nullable=False, comment='测试用例ID (*TCID)')
    case_name = Column(String(500), nullable=False, comment='用例名称 (*CaseName)')
    group_name = Column(String(100), comment='分组 (*Group)')
    sub_group = Column(String(100), comment='子分组 (*SubGroup)')
    component = Column(String(100), comment='组件 (*Component)')
    case_type = Column(String(50), comment='用例类型 (*Type)')
    level = Column(String(20), comment='用例级别 (*Level)')
    steps = Column(Text, nullable=False, comment='测试步骤 (*Steps)')
    expect_result = Column(Text, comment='预期结果 (*ExpectResult)')
    automated = Column(String(20), comment='自动化标识 (*Automated)')
    owner = Column(String(100), comment='负责人 (*Owner)')
    execute_owner = Column(String(100), comment='执行人 (*ExecuteOwner)')

    # 可选字段
    sort = Column(Integer, comment='排序')
    sub_component = Column(String(100), comment='子组件')
    sub_function = Column(String(100), comment='子功能')
    second_function = Column(String(100), comment='二级功能')
    case_set_type = Column(String(50), comment='用例集类型')
    purpose = Column(Text, comment='测试目的')
    pre_condition = Column(Text, comment='前置条件')
    keywords = Column(String(200), comment='关键字')
    phase = Column(String(50), comment='阶段')
    test_area = Column(String(100), comment='测试区域')
    execute_time = Column(String(50), comment='执行时间')
    standard = Column(String(100), comment='标准')
    simples = Column(String(100), comment='样本')
    environment = Column(String(100), comment='环境')
    os = Column(String(50), comment='操作系统')
    android = Column(String(50), comment='Android版本')
    cpu = Column(String(50), comment='CPU')
    interaction_component = Column(String(100), comment='交互组件')
    brand = Column(String(50), comment='品牌')
    sim = Column(String(50), comment='SIM卡')
    source = Column(String(100), comment='来源')
    match_project = Column(String(100), comment='匹配项目')
    test_code_path = Column(String(500), comment='测试代码路径')
    app_version = Column(String(50), comment='应用版本')
    special_ability = Column(String(100), comment='特殊能力')
    special_type = Column(String(100), comment='特殊类型')
    update_time = Column(DateTime, comment='更新时间')
    audit_result = Column(String(50), comment='审核结果')
    audit_remark = Column(Text, comment='审核备注')

    # 业务模块关联
    business_module_id = Column(Integer, ForeignKey('business_modules.id'), comment='业务模块ID')

    # 系统字段
    status = Column(String(20), default='待处理', comment='处理状态')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    # 关联关系
    business_module = relationship("BusinessModule", back_populates="test_cases")
    generated_scripts = relationship("GeneratedScript", back_populates="test_case")


class GeneratedScript(Base):
    """生成的脚本表"""
    __tablename__ = 'generated_scripts'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    test_case_id = Column(Integer, ForeignKey('test_cases.id'), nullable=False, comment='测试用例ID')
    script_name = Column(String(200), nullable=False, comment='脚本名称')
    script_content = Column(Text, nullable=False, comment='脚本内容')
    script_type = Column(String(50), default='typescript', comment='脚本类型')
    framework = Column(String(50), default='midsence.js', comment='使用框架')
    
    # 生成过程信息
    generation_status = Column(String(50), default='生成中', comment='生成状态')
    review_status = Column(String(50), default='待审查', comment='审查状态')
    review_comments = Column(Text, comment='审查意见')
    
    # 元数据
    page_elements_used = Column(JSON, comment='使用的页面元素信息')
    generation_metadata = Column(JSON, comment='生成过程元数据')
    
    # 文件信息
    file_path = Column(String(500), comment='生成的文件路径')
    file_size = Column(Integer, comment='文件大小(字节)')
    
    # 时间信息
    generation_started_at = Column(DateTime, comment='生成开始时间')
    generation_completed_at = Column(DateTime, comment='生成完成时间')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    # 关联关系
    test_case = relationship("TestCase", back_populates="generated_scripts")
    script_versions = relationship("ScriptVersion", back_populates="script")


class ScriptVersion(Base):
    """脚本版本表"""
    __tablename__ = 'script_versions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    script_id = Column(Integer, ForeignKey('generated_scripts.id'), nullable=False, comment='脚本ID')
    version_number = Column(String(20), nullable=False, comment='版本号')
    script_content = Column(Text, nullable=False, comment='脚本内容')
    change_description = Column(Text, comment='变更说明')
    generated_by = Column(String(100), comment='生成者(智能体名称)')
    
    # 质量评估
    quality_score = Column(Integer, comment='质量评分(1-100)')
    coverage_score = Column(Integer, comment='覆盖率评分(1-100)')
    
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    
    # 关联关系
    script = relationship("GeneratedScript", back_populates="script_versions")


class AgentSession(Base):
    """智能体会话表"""
    __tablename__ = 'agent_sessions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(100), unique=True, nullable=False, comment='会话ID')
    test_case_id = Column(Integer, ForeignKey('test_cases.id'), nullable=False, comment='测试用例ID')
    
    # 会话状态
    status = Column(String(50), default='进行中', comment='会话状态')
    current_agent = Column(String(100), comment='当前处理的智能体')
    
    # 处理过程
    case_analysis_completed = Column(Boolean, default=False, comment='用例分析完成')
    script_generation_completed = Column(Boolean, default=False, comment='脚本生成完成')
    script_review_completed = Column(Boolean, default=False, comment='脚本审查完成')
    file_generation_completed = Column(Boolean, default=False, comment='文件生成完成')
    
    # 结果信息
    final_script_id = Column(Integer, ForeignKey('generated_scripts.id'), comment='最终脚本ID')
    error_message = Column(Text, comment='错误信息')
    
    # 时间信息
    started_at = Column(DateTime, default=datetime.now, comment='开始时间')
    completed_at = Column(DateTime, comment='完成时间')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')


class AgentMessage(Base):
    """智能体消息表"""
    __tablename__ = 'agent_messages'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(100), ForeignKey('agent_sessions.session_id'), nullable=False, comment='会话ID')
    agent_name = Column(String(100), nullable=False, comment='智能体名称')
    message_type = Column(String(50), nullable=False, comment='消息类型')
    message_content = Column(Text, nullable=False, comment='消息内容')
    
    # 元数据
    message_metadata = Column(JSON, comment='消息元数据')
    processing_time = Column(Integer, comment='处理时间(毫秒)')
    
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')


# 创建所有表的函数
def create_tables(engine):
    """创建所有表"""
    Base.metadata.create_all(engine)


# 删除所有表的函数
def drop_tables(engine):
    """删除所有表"""
    Base.metadata.drop_all(engine)
