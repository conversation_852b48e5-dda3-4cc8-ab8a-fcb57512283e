"""
智能体会话和工作流相关数据模型
支持AutoGen智能体协作、会话管理、工作流编排
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, JSON, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum

Base = declarative_base()


class SessionStatus(Enum):
    """会话状态枚举"""
    CREATED = "created"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


class MessageType(Enum):
    """消息类型枚举"""
    USER_INPUT = "user_input"
    AGENT_RESPONSE = "agent_response"
    SYSTEM_MESSAGE = "system_message"
    ERROR_MESSAGE = "error_message"
    TOOL_CALL = "tool_call"
    TOOL_RESULT = "tool_result"
    WORKFLOW_EVENT = "workflow_event"


class WorkflowStatus(Enum):
    """工作流状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


class AgentSession(Base):
    """智能体会话表"""
    __tablename__ = 'agent_sessions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(100), unique=True, nullable=False, comment='会话ID')
    
    # 会话基本信息
    session_name = Column(String(200), comment='会话名称')
    session_type = Column(String(100), comment='会话类型: single_agent, multi_agent, workflow')
    primary_agent = Column(String(100), comment='主要智能体')
    participating_agents = Column(JSON, comment='参与的智能体列表')
    
    # 会话状态
    status = Column(String(50), default='created', comment='会话状态')
    current_step = Column(String(200), comment='当前步骤')
    progress_percentage = Column(Float, default=0.0, comment='进度百分比')
    
    # 会话配置
    session_config = Column(JSON, comment='会话配置')
    max_turns = Column(Integer, default=50, comment='最大轮次')
    timeout_seconds = Column(Integer, default=3600, comment='超时时间（秒）')
    
    # 会话上下文
    initial_context = Column(JSON, comment='初始上下文')
    current_context = Column(JSON, comment='当前上下文')
    session_variables = Column(JSON, comment='会话变量')
    
    # 会话统计
    total_messages = Column(Integer, default=0, comment='总消息数')
    agent_messages = Column(Integer, default=0, comment='智能体消息数')
    user_messages = Column(Integer, default=0, comment='用户消息数')
    error_count = Column(Integer, default=0, comment='错误次数')
    
    # 会话结果
    final_result = Column(JSON, comment='最终结果')
    success_status = Column(Boolean, comment='成功状态')
    error_message = Column(Text, comment='错误信息')
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    started_at = Column(DateTime, comment='开始时间')
    completed_at = Column(DateTime, comment='完成时间')
    last_activity_at = Column(DateTime, default=datetime.now, comment='最后活动时间')
    
    # 关联关系
    messages = relationship("AgentMessage", back_populates="session", cascade="all, delete-orphan")
    workflow_executions = relationship("WorkflowExecution", back_populates="session")


class AgentMessage(Base):
    """智能体消息表"""
    __tablename__ = 'agent_messages'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(Integer, ForeignKey('agent_sessions.id'), nullable=False, comment='会话ID')
    message_id = Column(String(100), unique=True, nullable=False, comment='消息ID')
    
    # 消息基本信息
    message_type = Column(String(50), nullable=False, comment='消息类型')
    sender_agent = Column(String(100), comment='发送者智能体')
    receiver_agent = Column(String(100), comment='接收者智能体')
    
    # 消息内容
    message_content = Column(Text, nullable=False, comment='消息内容')
    message_format = Column(String(50), default='text', comment='消息格式: text, json, markdown, html')
    
    # 消息元数据
    message_metadata = Column(JSON, comment='消息元数据')
    tool_calls = Column(JSON, comment='工具调用信息')
    tool_results = Column(JSON, comment='工具结果')
    
    # 消息状态
    is_processed = Column(Boolean, default=False, comment='是否已处理')
    processing_time = Column(Integer, comment='处理时间（毫秒）')
    
    # 消息质量
    confidence_score = Column(Float, comment='置信度分数')
    relevance_score = Column(Float, comment='相关性分数')
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    processed_at = Column(DateTime, comment='处理时间')
    
    # 关联关系
    session = relationship("AgentSession", back_populates="messages")


class WorkflowExecution(Base):
    """工作流执行表"""
    __tablename__ = 'workflow_executions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    execution_id = Column(String(100), unique=True, nullable=False, comment='执行ID')
    session_id = Column(Integer, ForeignKey('agent_sessions.id'), comment='关联会话ID')
    
    # 工作流基本信息
    workflow_name = Column(String(200), nullable=False, comment='工作流名称')
    workflow_type = Column(String(100), comment='工作流类型: sequential, parallel, conditional')
    workflow_definition = Column(JSON, comment='工作流定义')
    
    # 执行状态
    status = Column(String(50), default='pending', comment='执行状态')
    current_step_index = Column(Integer, default=0, comment='当前步骤索引')
    total_steps = Column(Integer, comment='总步骤数')
    completed_steps = Column(Integer, default=0, comment='已完成步骤数')
    
    # 执行配置
    execution_config = Column(JSON, comment='执行配置')
    retry_count = Column(Integer, default=0, comment='重试次数')
    max_retries = Column(Integer, default=3, comment='最大重试次数')
    
    # 执行结果
    execution_result = Column(JSON, comment='执行结果')
    step_results = Column(JSON, comment='步骤结果')
    error_details = Column(JSON, comment='错误详情')
    
    # 执行统计
    total_execution_time = Column(Integer, comment='总执行时间（毫秒）')
    average_step_time = Column(Integer, comment='平均步骤时间（毫秒）')
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    started_at = Column(DateTime, comment='开始时间')
    completed_at = Column(DateTime, comment='完成时间')
    
    # 关联关系
    session = relationship("AgentSession", back_populates="workflow_executions")
    step_executions = relationship("WorkflowStepExecution", back_populates="workflow", cascade="all, delete-orphan")


class WorkflowStepExecution(Base):
    """工作流步骤执行表"""
    __tablename__ = 'workflow_step_executions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    workflow_id = Column(Integer, ForeignKey('workflow_executions.id'), nullable=False, comment='工作流ID')
    step_id = Column(String(100), nullable=False, comment='步骤ID')
    
    # 步骤信息
    step_name = Column(String(200), comment='步骤名称')
    step_type = Column(String(100), comment='步骤类型: agent_task, tool_call, condition, loop')
    step_index = Column(Integer, comment='步骤索引')
    
    # 执行信息
    assigned_agent = Column(String(100), comment='分配的智能体')
    step_input = Column(JSON, comment='步骤输入')
    step_output = Column(JSON, comment='步骤输出')
    
    # 执行状态
    status = Column(String(50), default='pending', comment='执行状态')
    retry_count = Column(Integer, default=0, comment='重试次数')
    
    # 执行结果
    success = Column(Boolean, comment='是否成功')
    error_message = Column(Text, comment='错误信息')
    execution_time = Column(Integer, comment='执行时间（毫秒）')
    
    # 时间戳
    started_at = Column(DateTime, comment='开始时间')
    completed_at = Column(DateTime, comment='完成时间')
    
    # 关联关系
    workflow = relationship("WorkflowExecution", back_populates="step_executions")


class AgentPerformance(Base):
    """智能体性能统计表"""
    __tablename__ = 'agent_performance'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    agent_name = Column(String(100), nullable=False, comment='智能体名称')
    
    # 统计时间范围
    period_start = Column(DateTime, nullable=False, comment='统计开始时间')
    period_end = Column(DateTime, nullable=False, comment='统计结束时间')
    
    # 性能指标
    total_sessions = Column(Integer, default=0, comment='总会话数')
    successful_sessions = Column(Integer, default=0, comment='成功会话数')
    failed_sessions = Column(Integer, default=0, comment='失败会话数')
    average_session_duration = Column(Integer, comment='平均会话时长（毫秒）')
    
    # 消息统计
    total_messages = Column(Integer, default=0, comment='总消息数')
    average_response_time = Column(Integer, comment='平均响应时间（毫秒）')
    
    # 质量指标
    average_confidence_score = Column(Float, comment='平均置信度')
    average_relevance_score = Column(Float, comment='平均相关性')
    
    # 错误统计
    total_errors = Column(Integer, default=0, comment='总错误数')
    error_rate = Column(Float, comment='错误率')
    
    # 时间戳
    calculated_at = Column(DateTime, default=datetime.now, comment='计算时间')


# 创建所有表的函数
def create_session_tables(engine):
    """创建会话相关表"""
    Base.metadata.create_all(engine)
