"""
Android应用控制器
负责应用的启动、停止、状态检查等操作
"""
import subprocess
import time
import re
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime
from loguru import logger

from .config import config
from .models.app_model import AppInfo

# 尝试导入UI2相关模块
try:
    import uiautomator2 as u2
    from example.utils.uiautomator2_manager import UIAutomator2Manager
    UI2_AVAILABLE = True
except ImportError:
    logger.warning("UIAutomator2未安装，将使用ADB命令启动应用")
    UI2_AVAILABLE = False


class AndroidAppController:
    """Android应用控制器"""
    
    def __init__(self, device_id: Optional[str] = None):
        """
        初始化应用控制器
        
        Args:
            device_id: 设备ID，为None时使用默认设备
        """
        self.device_id = device_id or config.device.device_id
        self.connection_timeout = config.device.connection_timeout
        self.operation_timeout = config.device.operation_timeout
        self.retry_count = config.device.retry_count

        # UI2相关初始化
        self.ui2_device = None
        self.ui_manager = None
        if UI2_AVAILABLE:
            try:
                self.ui_manager = UIAutomator2Manager()
                self._init_ui2_device()
            except Exception as e:
                logger.warning(f"UI2初始化失败: {e}")
                self.ui2_device = None

    def _init_ui2_device(self):
        """初始化UI2设备连接"""
        try:
            if not self.ui_manager.check_service_health(self.device_id):
                logger.warning("UIAutomator2服务不健康，尝试重启...")
                if not self.ui_manager.restart_service(self.device_id):
                    logger.error("重启UIAutomator2服务失败")
                    return False

            # 连接设备
            if self.device_id:
                self.ui2_device = u2.connect(self.device_id)
            else:
                self.ui2_device = u2.connect()

            # 验证连接
            device_info = self.ui2_device.device_info
            logger.info(f"UI2设备连接成功: {device_info.get('brand', 'Unknown')} {device_info.get('model', 'Unknown')}")
            return True

        except Exception as e:
            logger.error(f"UI2设备初始化失败: {e}")
            self.ui2_device = None
            return False

    def _run_adb_command(self, command: List[str], timeout: int = None) -> Tuple[bool, str, str]:
        """
        执行ADB命令
        
        Args:
            command: ADB命令列表
            timeout: 超时时间
            
        Returns:
            Tuple[bool, str, str]: (成功标志, 标准输出, 错误输出)
        """
        try:
            # 构建完整命令
            full_command = ["adb"]
            if self.device_id:
                full_command.extend(["-s", self.device_id])
            full_command.extend(command)
            
            timeout = timeout or self.operation_timeout
            
            logger.debug(f"执行ADB命令: {' '.join(full_command)}")
            
            result = subprocess.run(
                full_command,
                capture_output=True,
                text=True,
                timeout=timeout,
                encoding='utf-8',
                errors='ignore'  # 忽略编码错误
            )

            success = result.returncode == 0
            stdout = result.stdout.strip() if result.stdout else ""
            stderr = result.stderr.strip() if result.stderr else ""
            
            if not success:
                logger.warning(f"ADB命令执行失败: {stderr}")
            
            return success, stdout, stderr
            
        except subprocess.TimeoutExpired:
            logger.error(f"ADB命令执行超时: {' '.join(full_command)}")
            return False, "", "命令执行超时"
        except Exception as e:
            logger.error(f"ADB命令执行异常: {e}")
            return False, "", str(e)
    
    def check_device_connection(self) -> bool:
        """
        检查设备连接状态
        
        Returns:
            bool: 设备是否连接
        """
        try:
            logger.info(f"检查设备连接状态: {self.device_id or 'default'}")
            
            success, stdout, stderr = self._run_adb_command(["devices"])
            if not success:
                logger.error(f"无法获取设备列表: {stderr}")
                return False
            
            # 解析设备列表
            lines = stdout.split('\n')
            for line in lines[1:]:  # 跳过标题行
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) >= 2:
                        device_serial = parts[0]
                        device_status = parts[1]
                        
                        # 如果指定了设备ID，检查是否匹配
                        if self.device_id:
                            if device_serial == self.device_id and device_status == "device":
                                logger.info(f"设备 {self.device_id} 已连接")
                                return True
                        else:
                            # 如果没有指定设备ID，检查是否有可用设备
                            if device_status == "device":
                                logger.info(f"发现可用设备: {device_serial}")
                                return True
            
            logger.warning("未找到可用设备")
            return False
            
        except Exception as e:
            logger.error(f"检查设备连接失败: {e}")
            return False
    
    def get_device_info(self) -> Dict[str, Any]:
        """
        获取设备信息
        
        Returns:
            Dict[str, Any]: 设备信息
        """
        device_info = {
            'device_id': self.device_id,
            'connected': False,
            'brand': '',
            'model': '',
            'android_version': '',
            'sdk_version': '',
            'screen_size': '',
            'density': ''
        }
        
        try:
            if not self.check_device_connection():
                return device_info
            
            device_info['connected'] = True
            
            # 获取设备属性
            properties = [
                ('brand', 'ro.product.brand'),
                ('model', 'ro.product.model'),
                ('android_version', 'ro.build.version.release'),
                ('sdk_version', 'ro.build.version.sdk'),
                ('density', 'ro.sf.lcd_density')
            ]
            
            for key, prop in properties:
                success, stdout, _ = self._run_adb_command(['shell', 'getprop', prop])
                if success:
                    device_info[key] = stdout
            
            # 获取屏幕尺寸
            success, stdout, _ = self._run_adb_command(['shell', 'wm', 'size'])
            if success:
                match = re.search(r'(\d+)x(\d+)', stdout)
                if match:
                    device_info['screen_size'] = f"{match.group(1)}x{match.group(2)}"
            
            logger.info(f"设备信息获取成功: {device_info['brand']} {device_info['model']}")
            return device_info
            
        except Exception as e:
            logger.error(f"获取设备信息失败: {e}")
            return device_info
    
    def get_app_info(self, package_name: str) -> Optional[AppInfo]:
        """
        获取应用信息
        
        Args:
            package_name: 应用包名
            
        Returns:
            Optional[AppInfo]: 应用信息，如果应用未安装返回None
        """
        try:
            logger.info(f"获取应用信息: {package_name}")
            
            # 检查应用是否安装
            success, stdout, _ = self._run_adb_command(['shell', 'pm', 'list', 'packages', package_name])
            if not success or package_name not in stdout:
                logger.warning(f"应用未安装: {package_name}")
                return None
            
            app_info = AppInfo(package_name=package_name)
            
            # 获取应用详细信息
            success, stdout, _ = self._run_adb_command(['shell', 'dumpsys', 'package', package_name])
            if success:
                # 解析应用信息
                lines = stdout.split('\n')
                for line in lines:
                    line = line.strip()
                    if 'versionName=' in line:
                        match = re.search(r'versionName=([^\s]+)', line)
                        if match:
                            app_info.version_name = match.group(1)
                    elif 'versionCode=' in line:
                        match = re.search(r'versionCode=(\d+)', line)
                        if match:
                            app_info.version_code = match.group(1)
                    elif 'targetSdk=' in line:
                        match = re.search(r'targetSdk=(\d+)', line)
                        if match:
                            app_info.target_sdk = int(match.group(1))
                    elif 'minSdk=' in line:
                        match = re.search(r'minSdk=(\d+)', line)
                        if match:
                            app_info.min_sdk = int(match.group(1))
            
            # 获取主Activity
            success, stdout, _ = self._run_adb_command(['shell', 'pm', 'dump', package_name])
            if success:
                # 查找主Activity
                lines = stdout.split('\n')
                for i, line in enumerate(lines):
                    if 'android.intent.action.MAIN' in line:
                        # 向上查找Activity名称
                        for j in range(max(0, i-10), i):
                            if 'Activity' in lines[j] and package_name in lines[j]:
                                match = re.search(rf'{package_name}/([^\s]+)', lines[j])
                                if match:
                                    app_info.main_activity = match.group(1)
                                    break
                        break
            
            logger.info(f"应用信息获取成功: {app_info.version_name}")
            return app_info
            
        except Exception as e:
            logger.error(f"获取应用信息失败: {e}")
            return None
    
    def is_app_installed(self, package_name: str) -> bool:
        """
        检查应用是否已安装
        
        Args:
            package_name: 应用包名
            
        Returns:
            bool: 应用是否已安装
        """
        try:
            success, stdout, _ = self._run_adb_command(['shell', 'pm', 'list', 'packages', package_name])
            return success and package_name in stdout
        except Exception as e:
            logger.error(f"检查应用安装状态失败: {e}")
            return False
    
    def is_app_running(self, package_name: str) -> bool:
        """
        检查应用是否正在运行
        
        Args:
            package_name: 应用包名
            
        Returns:
            bool: 应用是否正在运行
        """
        try:
            success, stdout, _ = self._run_adb_command(['shell', 'ps', '|', 'grep', package_name])
            return success and package_name in stdout
        except Exception as e:
            logger.error(f"检查应用运行状态失败: {e}")
            return False
    
    def get_current_activity(self) -> Optional[str]:
        """
        获取当前前台Activity
        
        Returns:
            Optional[str]: 当前Activity名称
        """
        try:
            # 尝试多种方法获取当前Activity
            methods = [
                ['shell', 'dumpsys', 'activity', 'activities', '|', 'grep', 'mResumedActivity'],
                ['shell', 'dumpsys', 'activity', 'top', '|', 'grep', 'ACTIVITY'],
                ['shell', 'dumpsys', 'window', 'windows', '|', 'grep', 'mCurrentFocus']
            ]
            
            for method in methods:
                success, stdout, _ = self._run_adb_command(method)
                if success and stdout:
                    # 解析Activity名称
                    match = re.search(r'([a-zA-Z0-9_.]+)/([a-zA-Z0-9_.]+)', stdout)
                    if match:
                        package = match.group(1)
                        activity = match.group(2)
                        return f"{package}/{activity}"
            
            return None
            
        except Exception as e:
            logger.error(f"获取当前Activity失败: {e}")
            return None

    def launch_app_with_ui2(self, package_name: str, activity_name: str = None) -> bool:
        """
        使用UI2启动应用

        Args:
            package_name: 应用包名
            activity_name: Activity名称（可选）

        Returns:
            bool: 启动是否成功
        """
        try:
            if not self.ui2_device:
                logger.warning("UI2设备未初始化，尝试重新初始化...")
                if not self._init_ui2_device():
                    return False

            logger.info(f"使用UI2启动应用: {package_name}")

            # 先停止应用
            self.ui2_device.app_stop(package_name)
            time.sleep(1)

            # 启动应用
            if activity_name:
                # 指定Activity启动
                if not activity_name.startswith('.'):
                    activity_name = f".{activity_name}"
                self.ui2_device.app_start(package_name, activity=activity_name, stop=True)
            else:
                # 默认启动
                self.ui2_device.app_start(package_name, stop=True)

            # 等待启动
            time.sleep(3)

            # 验证启动
            current_app = self.ui2_device.app_current()
            if current_app.get('package') == package_name:
                logger.info(f"UI2启动成功: {current_app}")
                return True
            else:
                logger.warning(f"UI2启动可能失败，当前应用: {current_app}")
                # 再等待一下
                time.sleep(2)
                current_app = self.ui2_device.app_current()
                if current_app.get('package') == package_name:
                    logger.info(f"UI2启动成功（延迟确认）: {current_app}")
                    return True
                else:
                    logger.error("UI2启动验证失败")
                    return False

        except Exception as e:
            logger.error(f"UI2启动应用异常: {e}")
            return False

    def launch_app(self, package_name: str, activity_name: str = None, wait_time: int = 3) -> bool:
        """
        启动应用

        Args:
            package_name: 应用包名
            activity_name: Activity名称，为None时启动主Activity
            wait_time: 启动后等待时间（秒）

        Returns:
            bool: 启动是否成功
        """
        try:
            logger.info(f"启动应用: {package_name}")

            # 检查设备连接
            if not self.check_device_connection():
                logger.error("设备未连接")
                return False

            # 检查应用是否安装
            if not self.is_app_installed(package_name):
                logger.error(f"应用未安装: {package_name}")
                return False

            # 方法1: 优先使用UI2启动
            if UI2_AVAILABLE and self.ui2_device:
                logger.info("尝试使用UI2启动应用...")
                if self.launch_app_with_ui2(package_name, activity_name):
                    logger.info("✅ UI2启动成功")
                    return True
                else:
                    logger.warning("UI2启动失败，降级到ADB命令")

            # 方法2: 降级到ADB命令启动
            logger.info("使用ADB命令启动应用...")

            # 强制停止应用（确保干净状态）
            self.force_stop_app(package_name)
            time.sleep(1)

            # 构建启动命令
            if activity_name:
                if not activity_name.startswith('.'):
                    activity_name = f".{activity_name}"
                launch_target = f"{package_name}/{activity_name}"
            else:
                # 获取主Activity
                app_info = self.get_app_info(package_name)
                if app_info and app_info.main_activity:
                    launch_target = f"{package_name}/{app_info.main_activity}"
                else:
                    # 使用默认启动方式
                    launch_target = package_name

            # 启动应用
            if launch_target == package_name:
                # 使用monkey启动
                success, stdout, stderr = self._run_adb_command([
                    'shell', 'monkey', '-p', package_name, '-c', 'android.intent.category.LAUNCHER', '1'
                ])
            else:
                # 使用am start启动
                success, stdout, stderr = self._run_adb_command([
                    'shell', 'am', 'start', '-n', launch_target
                ])

            if not success:
                logger.error(f"启动应用失败: {stderr}")
                return False

            # 等待应用启动
            time.sleep(wait_time)

            # 验证应用是否启动成功
            current_activity = self.get_current_activity()
            if current_activity and package_name in current_activity:
                logger.info(f"应用启动成功: {current_activity}")
                return True
            else:
                logger.warning(f"应用可能未完全启动，当前Activity: {current_activity}")
                # 给应用更多时间启动
                time.sleep(2)
                current_activity = self.get_current_activity()
                if current_activity and package_name in current_activity:
                    logger.info(f"应用启动成功（延迟确认）: {current_activity}")
                    return True
                else:
                    logger.error("应用启动验证失败")
                    return False

        except Exception as e:
            logger.error(f"启动应用异常: {e}")
            return False

    def force_stop_app(self, package_name: str) -> bool:
        """
        强制停止应用

        Args:
            package_name: 应用包名

        Returns:
            bool: 停止是否成功
        """
        try:
            logger.info(f"强制停止应用: {package_name}")

            success, stdout, stderr = self._run_adb_command(['shell', 'am', 'force-stop', package_name])
            if success:
                logger.info(f"应用已停止: {package_name}")
                return True
            else:
                logger.warning(f"停止应用失败: {stderr}")
                return False

        except Exception as e:
            logger.error(f"停止应用异常: {e}")
            return False

    def clear_app_data(self, package_name: str) -> bool:
        """
        清除应用数据

        Args:
            package_name: 应用包名

        Returns:
            bool: 清除是否成功
        """
        try:
            logger.info(f"清除应用数据: {package_name}")

            # 先停止应用
            self.force_stop_app(package_name)

            success, stdout, stderr = self._run_adb_command(['shell', 'pm', 'clear', package_name])
            if success:
                logger.info(f"应用数据已清除: {package_name}")
                return True
            else:
                logger.warning(f"清除应用数据失败: {stderr}")
                return False

        except Exception as e:
            logger.error(f"清除应用数据异常: {e}")
            return False

    def restart_app(self, package_name: str, activity_name: str = None, clear_data: bool = False) -> bool:
        """
        重启应用

        Args:
            package_name: 应用包名
            activity_name: Activity名称
            clear_data: 是否清除应用数据

        Returns:
            bool: 重启是否成功
        """
        try:
            logger.info(f"重启应用: {package_name}")

            # 停止应用
            self.force_stop_app(package_name)

            # 清除数据（可选）
            if clear_data:
                self.clear_app_data(package_name)

            # 等待一下
            time.sleep(2)

            # 启动应用
            return self.launch_app(package_name, activity_name)

        except Exception as e:
            logger.error(f"重启应用异常: {e}")
            return False

    def wait_for_app_launch(self, package_name: str, timeout: int = 30) -> bool:
        """
        等待应用启动完成（优先使用UI2验证）

        Args:
            package_name: 应用包名
            timeout: 超时时间（秒）

        Returns:
            bool: 应用是否启动完成
        """
        try:
            logger.info(f"等待应用启动完成: {package_name}")

            start_time = time.time()
            while time.time() - start_time < timeout:
                # 优先使用UI2验证
                if UI2_AVAILABLE and self.ui2_device:
                    try:
                        current_app = self.ui2_device.app_current()
                        if current_app.get('package') == package_name:
                            logger.info(f"应用启动完成（UI2验证）: {current_app}")
                            return True
                    except Exception as e:
                        logger.debug(f"UI2验证失败，尝试ADB: {e}")

                # 降级到ADB验证
                try:
                    current_activity = self.get_current_activity()
                    if current_activity and package_name in current_activity:
                        logger.info(f"应用启动完成（ADB验证）: {current_activity}")
                        return True
                except Exception as e:
                    logger.debug(f"ADB验证也失败: {e}")

                time.sleep(1)

            logger.warning(f"等待应用启动超时: {package_name}")
            return False

        except Exception as e:
            logger.error(f"等待应用启动异常: {e}")
            return False

    def get_app_process_info(self, package_name: str) -> Dict[str, Any]:
        """
        获取应用进程信息

        Args:
            package_name: 应用包名

        Returns:
            Dict[str, Any]: 进程信息
        """
        process_info = {
            'package_name': package_name,
            'running': False,
            'pid': None,
            'memory_usage': 0,
            'cpu_usage': 0.0
        }

        try:
            # 检查进程是否运行
            success, stdout, _ = self._run_adb_command(['shell', 'ps', '|', 'grep', package_name])
            if success and package_name in stdout:
                process_info['running'] = True

                # 解析PID
                lines = stdout.split('\n')
                for line in lines:
                    if package_name in line:
                        parts = line.split()
                        if len(parts) >= 2:
                            try:
                                process_info['pid'] = int(parts[1])
                                break
                            except ValueError:
                                continue

            # 获取内存使用情况
            if process_info['running']:
                success, stdout, _ = self._run_adb_command(['shell', 'dumpsys', 'meminfo', package_name])
                if success:
                    # 解析内存信息
                    match = re.search(r'TOTAL\s+(\d+)', stdout)
                    if match:
                        process_info['memory_usage'] = int(match.group(1))

            return process_info

        except Exception as e:
            logger.error(f"获取应用进程信息失败: {e}")
            return process_info
