#!/usr/bin/env python3
"""
演示脚本生成智能体优化效果
展示优化前后的对比和新功能
"""
import sys
from pathlib import Path

# 添加相对路径导入
sys.path.append(str(Path(__file__).parent.parent))


def demo_natural_language_generation():
    """演示自然语言描述生成功能"""
    print("="*80)
    print("演示自然语言描述生成功能")
    print("="*80)
    
    # 模拟ScriptGenerationAgent的方法
    def generate_natural_language_description(target_element: str, action_type: str) -> str:
        """模拟自然语言描述生成"""
        element_mappings = {
            '按钮': '按钮', 'button': '按钮',
            '输入框': '输入框', 'input': '输入框', 'edittext': '输入框',
            '图标': '图标', 'icon': '图标'
        }
        
        position_keywords = {
            '顶部': '顶部', '底部': '底部', '左侧': '左侧', '右侧': '右侧',
            '右上': '右上角', '左下': '左下角'
        }
        
        target_lower = target_element.lower()
        
        # 检测元素类型
        element_type = ""
        for key, value in element_mappings.items():
            if key in target_lower:
                element_type = value
                break
        
        # 检测位置信息
        position = ""
        for key, value in position_keywords.items():
            if key in target_element:
                position = value
                break
        
        # 构建自然语言描述
        if action_type == 'click' or action_type == 'tap':
            if position and element_type:
                return f"点击{position}的{target_element}"
            else:
                return f"点击{target_element}"
        elif action_type == 'input':
            if position:
                return f"在{position}的{target_element}输入内容"
            else:
                return f"在{target_element}输入内容"
        else:
            return f"对{target_element}执行{action_type}操作"
    
    # 测试用例
    test_cases = [
        ("登录按钮", "click"),
        ("顶部搜索框", "input"),
        ("底部发送按钮", "click"),
        ("右上角设置图标", "click"),
        ("用户名输入框", "input"),
        ("左侧菜单按钮", "click"),
        ("页面内容区域", "swipe")
    ]
    
    print("自然语言描述生成示例:")
    print("-" * 60)
    for element, action in test_cases:
        result = generate_natural_language_description(element, action)
        print(f"输入: {element} + {action}")
        print(f"输出: {result}")
        print()


def demo_element_mapping_utilization():
    """演示元素库信息利用"""
    print("="*80)
    print("演示元素库信息利用")
    print("="*80)
    
    # 模拟元素映射数据
    element_mappings = [
        {
            "target_description": "登录按钮",
            "element_type": "Button",
            "element_text": "登录",
            "match_confidence": 0.95
        },
        {
            "target_description": "搜索输入框",
            "element_type": "EditText", 
            "element_text": "请输入搜索内容",
            "match_confidence": 0.90
        },
        {
            "target_description": "设置图标",
            "element_type": "ImageView",
            "element_text": "",
            "match_confidence": 0.85
        },
        {
            "target_description": "踩icon",
            "element_type": "ImageView",
            "element_text": "踩",
            "match_confidence": 0.88
        }
    ]
    
    def generate_element_natural_description(mapping):
        """模拟元素自然语言描述生成"""
        target_desc = mapping.get('target_description', '')
        element_type = mapping.get('element_type', '')
        element_text = mapping.get('element_text', '')
        
        if element_text and element_text != 'N/A':
            if element_type in ['Button']:
                return f"'{element_text}'按钮"
            elif element_type in ['EditText']:
                return f"'{element_text}'输入框"
            elif element_type in ['TextView']:
                return f"显示'{element_text}'的文本"
            else:
                return f"'{element_text}'"
        else:
            return target_desc
    
    print("元素库信息转换为自然语言描述:")
    print("-" * 60)
    for mapping in element_mappings:
        natural_desc = generate_element_natural_description(mapping)
        print(f"目标描述: {mapping['target_description']}")
        print(f"元素类型: {mapping['element_type']}")
        print(f"元素文本: {mapping['element_text'] or '无'}")
        print(f"自然语言描述: {natural_desc}")
        print(f"匹配置信度: {mapping['match_confidence']:.2f}")
        print()


def demo_script_comparison():
    """演示优化前后脚本对比"""
    print("="*80)
    print("演示优化前后脚本对比")
    print("="*80)
    
    print("优化前的脚本生成:")
    print("-" * 40)
    old_script = """
// 基础的操作描述
await agent.aiAction('点击登录按钮');
await agent.aiAction('输入用户名');
await agent.aiAction('输入密码');
await agent.aiAssert("用户已成功登录");
"""
    print(old_script)
    
    print("优化后的脚本生成:")
    print("-" * 40)
    new_script = """
// 基于元素库信息的精确自然语言描述
await agent.aiTap('页面底部的登录按钮');
await agent.aiInput('testuser', '用户名输入框');
await agent.aiInput('password123', '密码输入框');
await agent.aiWaitFor('登录成功，页面跳转到用户主页');
await agent.aiAssert('页面显示用户头像和欢迎信息');

// 更精确的位置描述
await agent.aiAction('点击右上角的设置图标');
await agent.aiAction('在顶部搜索框输入 "关键词"');
await agent.aiAction('向下滑动到页面底部');
"""
    print(new_script)


def demo_prompt_improvements():
    """演示Prompt改进点"""
    print("="*80)
    print("演示Prompt改进点")
    print("="*80)
    
    improvements = [
        {
            "改进点": "强调自然语言描述位置",
            "优化前": "await agent.aiAction('点击按钮');",
            "优化后": "await agent.aiAction('点击页面底部的发送按钮');"
        },
        {
            "改进点": "利用元素库信息",
            "优化前": "目标: 登录按钮",
            "优化后": "目标: 登录按钮\n建议的自然语言描述: 点击'登录'按钮\n元素文本: 登录\n匹配置信度: 0.95"
        },
        {
            "改进点": "添加设备配置",
            "优化前": "const device = new AndroidDevice(devices[0].udid);",
            "优化后": "const device = new AndroidDevice(devices[0].udid, {\n    imeStrategy: \"yadb-for-non-ascii\"\n});"
        },
        {
            "改进点": "中文操作上下文",
            "优化前": "aiActionContext: 'If any popup, click agree.'",
            "优化后": "aiActionContext: '如果出现位置、权限、用户协议等弹窗，点击同意。如果出现登录页面，关闭它'"
        }
    ]
    
    for improvement in improvements:
        print(f"改进点: {improvement['改进点']}")
        print(f"优化前: {improvement['优化前']}")
        print(f"优化后: {improvement['优化后']}")
        print("-" * 60)


def demo_api_method_priority():
    """演示API方法优先级"""
    print("="*80)
    print("演示API方法优先级")
    print("="*80)
    
    api_methods = [
        {
            "方法": "aiAction",
            "优先级": "最高",
            "描述": "支持完整自然语言描述的通用操作",
            "示例": "await agent.aiAction('点击页面底部的发送按钮');"
        },
        {
            "方法": "aiTap",
            "优先级": "高",
            "描述": "精确点击指定元素",
            "示例": "await agent.aiTap('登录按钮');"
        },
        {
            "方法": "aiInput",
            "优先级": "高",
            "描述": "在指定输入框输入内容",
            "示例": "await agent.aiInput('用户名', '用户名输入框');"
        },
        {
            "方法": "aiWaitFor",
            "优先级": "高",
            "描述": "等待特定状态或条件",
            "示例": "await agent.aiWaitFor('页面完全加载，显示主要内容');"
        },
        {
            "方法": "aiAssert",
            "优先级": "高",
            "描述": "验证页面状态或内容",
            "示例": "await agent.aiAssert('页面显示用户登录成功的提示信息');"
        }
    ]
    
    print("Midscene.js API方法优先级和使用建议:")
    print("-" * 60)
    for method in api_methods:
        print(f"方法: {method['方法']}")
        print(f"优先级: {method['优先级']}")
        print(f"描述: {method['描述']}")
        print(f"示例: {method['示例']}")
        print()


def main():
    """主函数"""
    print("脚本生成智能体优化效果演示")
    print("基于参考脚本: backend/testcase/ella语音_texai_aialg_va_new_feedback_0010_test.ts")
    print()
    
    # 演示各个功能
    demo_natural_language_generation()
    print()
    
    demo_element_mapping_utilization()
    print()
    
    demo_script_comparison()
    print()
    
    demo_prompt_improvements()
    print()
    
    demo_api_method_priority()
    
    print("="*80)
    print("总结")
    print("="*80)
    print("✓ 优先使用支持自然语言描述位置的API方法")
    print("✓ 充分利用单用例分析智能体传递的元素库信息")
    print("✓ 生成更精确、更可读的自动化测试脚本")
    print("✓ 符合Midscene.js框架的最佳实践")
    print("✓ 提供完整的错误处理和资源管理")
    print()
    print("优化文件位置: new_backend/agents/script_generation_agent.py")
    print("主要优化方法: _get_script_generation_prompt(), _prepare_generation_input()")
    print("新增辅助方法: _generate_natural_language_description(), _generate_ai_assertion_text(), _generate_element_natural_description()")


if __name__ == "__main__":
    main()
