"""
单用例分析智能体
实现用例解析智能体，将自然语言用例转换为结构化的操作步骤和断言
"""
import os
import sys
import json
import asyncio
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加相对路径导入
sys.path.append(str(Path(__file__).parent.parent))

from config import config
from utils.autogen_client import AutoGenClientManager
from utils.database_manager import DatabaseManager
from models.case_models import TestCase, CaseAnalysisResult, ActionStep, AssertionStep, ElementMapping
from models.page_models import UIElement


class CaseAnalysisAgent:
    """单用例分析智能体"""
    
    def __init__(self):
        """初始化用例分析智能体"""
        self.config = config
        self.agent_name = "case_analysis_agent"
        
        # 核心组件
        self.autogen_client = AutoGenClientManager()
        self.db_manager = DatabaseManager()
        
        # 智能体
        self.analysis_agent = None
        
        # 初始化智能体
        self._initialize_agent()
    
    def _initialize_agent(self):
        """初始化用例分析智能体"""
        try:
            logger.info("初始化用例分析智能体...")
            
            # 创建用例分析智能体
            self.analysis_agent = self.autogen_client.create_text_agent(
                name="case_analysis_agent",
                system_message=self._get_case_analysis_prompt()
            )
            
            if self.analysis_agent:
                logger.info("用例分析智能体初始化成功")
            else:
                logger.warning("用例分析智能体初始化失败，将使用降级模式")
            
        except Exception as e:
            logger.error(f"初始化用例分析智能体失败: {e}")
            self.analysis_agent = None
    
    def _get_case_analysis_prompt(self) -> str:
        """获取用例分析提示词"""
        return """你是一个专业的Android自动化测试用例分析专家。

任务：分析自然语言测试用例，提取操作步骤和断言，返回结构化的JSON结果。

分析要点：
1. 识别测试用例的前置条件、操作步骤、预期结果
2. 将操作步骤转换为具体的Android操作（点击、输入、滑动等）
3. 将预期结果转换为可验证的断言
4. 匹配UI元素和操作目标
5. 生成适用于Midscene.js框架的AI操作描述

支持的操作类型：
- click: 点击操作
- input: 文本输入
- swipe: 滑动操作
- scroll: 滚动操作
- wait: 等待操作
- back: 返回操作
- launch: 启动应用
- close: 关闭应用

支持的断言类型：
- element_exists: 元素存在
- element_not_exists: 元素不存在
- text_contains: 文本包含
- text_equals: 文本相等
- page_title: 页面标题
- toast_message: Toast消息
- dialog_appears: 对话框出现
- page_navigation: 页面跳转

返回格式：
{
    "extracted_actions": [
        {
            "step_index": 1,
            "action_type": "click",
            "target_element": "登录按钮",
            "action_description": "点击登录按钮",
            "input_data": null,
            "ai_action_text": "点击登录按钮"
        }
    ],
    "extracted_assertions": [
        {
            "step_index": 1,
            "assertion_type": "page_navigation",
            "target_element": null,
            "expected_value": "登录成功页面",
            "assertion_description": "验证跳转到登录成功页面",
            "ai_assertion_text": "页面显示登录成功"
        }
    ],
    "analysis_confidence": 0.95,
    "completeness_score": 0.90,
    "clarity_score": 0.85,
    "analysis_notes": "用例分析完成，识别了X个操作步骤和Y个断言",
    "identified_risks": ["可能的风险点"],
    "optimization_suggestions": ["优化建议"]
}

请确保返回有效的JSON格式，并提供详细的分析结果。"""
    
    async def analyze_test_case(self, test_case: TestCase, 
                              app_elements: Optional[List[UIElement]] = None) -> Dict[str, Any]:
        """
        分析测试用例
        
        Args:
            test_case: 测试用例对象
            app_elements: 应用的UI元素列表（用于元素匹配）
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        try:
            logger.info(f"开始分析测试用例: {test_case.case_name}")
            
            # 准备分析输入
            analysis_input = self._prepare_analysis_input(test_case, app_elements)
            
            # 使用AI智能体分析
            if self.analysis_agent and self.autogen_client.is_available():
                analysis_result = await self._analyze_with_ai(analysis_input)
            else:
                # 降级处理
                logger.warning("使用降级模式分析用例")
                analysis_result = self._analyze_with_fallback(test_case)
            
            # 验证和完善分析结果
            validated_result = self._validate_analysis_result(analysis_result, test_case)
            
            # 元素匹配
            if app_elements:
                element_mapping = self._match_elements(validated_result, app_elements)
                validated_result['element_mapping'] = element_mapping
            
            # 保存分析结果
            saved_result = self._save_analysis_result(test_case, validated_result)
            
            logger.info(f"用例分析完成: {test_case.case_name}")
            return {
                'success': True,
                'analysis_result': validated_result,
                'saved_result_id': saved_result.id if saved_result else None
            }
            
        except Exception as e:
            logger.error(f"分析测试用例失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _prepare_analysis_input(self, test_case: TestCase, 
                              app_elements: Optional[List[UIElement]] = None) -> str:
        """准备分析输入"""
        try:
            input_parts = [
                "请分析以下测试用例：",
                "",
                f"用例ID: {test_case.case_id}",
                f"用例名称: {test_case.case_name}",
                f"测试目的: {test_case.test_purpose or '未指定'}",
                f"应用包名: {test_case.package_name}",
                "",
                "前置条件:",
                test_case.preconditions or "无特殊前置条件",
                "",
                "测试步骤:",
                test_case.test_steps,
                "",
                "预期结果:",
                test_case.expected_results or "无明确预期结果"
            ]
            
            # 添加可用元素信息
            if app_elements:
                input_parts.extend([
                    "",
                    "可用的UI元素（用于匹配操作目标）:",
                    ""
                ])
                
                # 只包含可操作元素和有文本的元素
                relevant_elements = [
                    elem for elem in app_elements 
                    if elem.is_actionable or elem.text or elem.resource_id
                ][:50]  # 限制数量避免输入过长
                
                for i, elem in enumerate(relevant_elements, 1):
                    element_desc = f"{i}. {elem.element_type}"
                    if elem.text:
                        element_desc += f" - 文本: '{elem.text}'"
                    if elem.resource_id:
                        element_desc += f" - ID: {elem.resource_id}"
                    if elem.semantic_description:
                        element_desc += f" - 描述: {elem.semantic_description}"
                    
                    input_parts.append(element_desc)
            
            input_parts.extend([
                "",
                "请根据以上信息进行详细分析，返回JSON格式的结构化结果。"
            ])
            
            return "\n".join(input_parts)
            
        except Exception as e:
            logger.error(f"准备分析输入失败: {e}")
            return f"分析用例: {test_case.case_name}\n步骤: {test_case.test_steps}"
    
    async def _analyze_with_ai(self, analysis_input: str) -> Dict[str, Any]:
        """使用AI智能体分析"""
        try:
            # 调用AI智能体
            result = await self.autogen_client.run_text_task(
                agent=self.analysis_agent,
                content=analysis_input
            )
            
            if result.get('success'):
                # 解析AI返回的JSON结果
                content = result.get('content', '')
                analysis_data = self._parse_ai_analysis_result(content)
                
                if analysis_data:
                    return analysis_data
                else:
                    logger.warning("AI分析结果解析失败")
                    return self._create_empty_analysis_result()
            else:
                logger.warning(f"AI分析失败: {result.get('error')}")
                return self._create_empty_analysis_result()
                
        except Exception as e:
            logger.error(f"AI分析异常: {e}")
            return self._create_empty_analysis_result()
    
    def _parse_ai_analysis_result(self, content: str) -> Optional[Dict[str, Any]]:
        """解析AI分析结果"""
        try:
            # 提取JSON部分
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            
            if start_idx >= 0 and end_idx > start_idx:
                json_str = content[start_idx:end_idx]
                result = json.loads(json_str)
                
                # 验证必要字段
                required_fields = ['extracted_actions', 'extracted_assertions', 'analysis_confidence']
                if all(field in result for field in required_fields):
                    return result
            
            return None
            
        except json.JSONDecodeError as e:
            logger.warning(f"解析AI分析结果JSON失败: {e}")
            return None
        except Exception as e:
            logger.warning(f"解析AI分析结果异常: {e}")
            return None
    
    def _analyze_with_fallback(self, test_case: TestCase) -> Dict[str, Any]:
        """降级分析处理"""
        try:
            logger.info("使用规则分析用例")
            
            # 简单的规则分析
            actions = self._extract_actions_by_rules(test_case.test_steps)
            assertions = self._extract_assertions_by_rules(test_case.expected_results or "")
            
            return {
                'extracted_actions': actions,
                'extracted_assertions': assertions,
                'analysis_confidence': 0.6,
                'completeness_score': 0.7,
                'clarity_score': 0.6,
                'analysis_notes': '基于规则的降级分析',
                'identified_risks': ['降级模式分析，可能不够准确'],
                'optimization_suggestions': ['建议使用AI模式重新分析']
            }
            
        except Exception as e:
            logger.error(f"降级分析失败: {e}")
            return self._create_empty_analysis_result()
    
    def _extract_actions_by_rules(self, test_steps: str) -> List[Dict[str, Any]]:
        """基于规则提取操作步骤"""
        try:
            actions = []
            
            # 简单的关键词匹配
            action_keywords = {
                'click': ['点击', '按', '选择', '触摸'],
                'input': ['输入', '填写', '录入'],
                'swipe': ['滑动', '拖拽', '滑'],
                'scroll': ['滚动', '翻页'],
                'wait': ['等待', '暂停'],
                'back': ['返回', '后退'],
                'launch': ['打开', '启动', '进入'],
                'close': ['关闭', '退出']
            }
            
            lines = test_steps.split('\n')
            step_index = 1
            
            for line in lines:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                # 移除步骤编号
                import re
                line = re.sub(r'^\d+[\.、]\s*', '', line)
                
                # 匹配操作类型
                action_type = 'click'  # 默认
                for action, keywords in action_keywords.items():
                    if any(keyword in line for keyword in keywords):
                        action_type = action
                        break
                
                # 提取目标元素
                target_element = self._extract_target_from_text(line)
                
                # 提取输入数据
                input_data = self._extract_input_data(line) if action_type == 'input' else None
                
                action = {
                    'step_index': step_index,
                    'action_type': action_type,
                    'target_element': target_element,
                    'action_description': line,
                    'input_data': input_data,
                    'ai_action_text': line
                }
                
                actions.append(action)
                step_index += 1
            
            return actions
            
        except Exception as e:
            logger.warning(f"规则提取操作步骤失败: {e}")
            return []
    
    def _extract_assertions_by_rules(self, expected_results: str) -> List[Dict[str, Any]]:
        """基于规则提取断言"""
        try:
            assertions = []
            
            if not expected_results:
                return assertions
            
            # 简单的断言关键词匹配
            assertion_keywords = {
                'element_exists': ['显示', '出现', '存在', '可见'],
                'text_contains': ['包含', '含有'],
                'text_equals': ['等于', '为'],
                'page_navigation': ['跳转', '进入', '到达'],
                'toast_message': ['提示', 'toast', '消息'],
                'dialog_appears': ['弹窗', '对话框', '弹出']
            }
            
            lines = expected_results.split('\n')
            step_index = 1
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # 匹配断言类型
                assertion_type = 'element_exists'  # 默认
                for assertion, keywords in assertion_keywords.items():
                    if any(keyword in line for keyword in keywords):
                        assertion_type = assertion
                        break
                
                # 提取期望值
                expected_value = self._extract_expected_value(line)
                
                assertion = {
                    'step_index': step_index,
                    'assertion_type': assertion_type,
                    'target_element': None,
                    'expected_value': expected_value,
                    'assertion_description': line,
                    'ai_assertion_text': line
                }
                
                assertions.append(assertion)
                step_index += 1
            
            return assertions
            
        except Exception as e:
            logger.warning(f"规则提取断言失败: {e}")
            return []
    
    def _extract_target_from_text(self, text: str) -> Optional[str]:
        """从文本中提取目标元素"""
        try:
            # 简单的目标提取
            import re
            
            # 匹配引号中的内容
            quoted_match = re.search(r'["\']([^"\']+)["\']', text)
            if quoted_match:
                return quoted_match.group(1)
            
            # 匹配常见的UI元素词汇
            ui_elements = ['按钮', '输入框', '文本框', '标签', '图片', '列表', '菜单']
            for element in ui_elements:
                if element in text:
                    # 提取元素前的描述词
                    pattern = rf'(\S+){element}'
                    match = re.search(pattern, text)
                    if match:
                        return match.group(1) + element
            
            return None
            
        except Exception as e:
            logger.warning(f"提取目标元素失败: {e}")
            return None
    
    def _extract_input_data(self, text: str) -> Optional[str]:
        """从文本中提取输入数据"""
        try:
            import re
            
            # 匹配引号中的内容
            quoted_match = re.search(r'["\']([^"\']+)["\']', text)
            if quoted_match:
                return quoted_match.group(1)
            
            # 匹配"输入XXX"模式
            input_match = re.search(r'输入\s*(.+)', text)
            if input_match:
                return input_match.group(1).strip()
            
            return None
            
        except Exception as e:
            logger.warning(f"提取输入数据失败: {e}")
            return None
    
    def _extract_expected_value(self, text: str) -> str:
        """从文本中提取期望值"""
        try:
            import re
            
            # 匹配引号中的内容
            quoted_match = re.search(r'["\']([^"\']+)["\']', text)
            if quoted_match:
                return quoted_match.group(1)
            
            # 返回整个文本作为期望值
            return text

        except Exception as e:
            logger.warning(f"提取期望值失败: {e}")
            return text

    def _create_empty_analysis_result(self) -> Dict[str, Any]:
        """创建空的分析结果"""
        return {
            'extracted_actions': [],
            'extracted_assertions': [],
            'analysis_confidence': 0.0,
            'completeness_score': 0.0,
            'clarity_score': 0.0,
            'analysis_notes': '分析失败',
            'identified_risks': ['分析过程出现错误'],
            'optimization_suggestions': ['请检查用例格式和内容']
        }

    def _validate_analysis_result(self, analysis_result: Dict[str, Any],
                                test_case: TestCase) -> Dict[str, Any]:
        """验证和完善分析结果"""
        try:
            # 确保必要字段存在
            if 'extracted_actions' not in analysis_result:
                analysis_result['extracted_actions'] = []
            if 'extracted_assertions' not in analysis_result:
                analysis_result['extracted_assertions'] = []

            # 验证操作步骤
            validated_actions = []
            for action in analysis_result.get('extracted_actions', []):
                if isinstance(action, dict) and 'action_type' in action:
                    # 确保必要字段
                    validated_action = {
                        'step_index': action.get('step_index', len(validated_actions) + 1),
                        'action_type': action.get('action_type', 'click'),
                        'target_element': action.get('target_element'),
                        'action_description': action.get('action_description', ''),
                        'input_data': action.get('input_data'),
                        'ai_action_text': action.get('ai_action_text', action.get('action_description', ''))
                    }
                    validated_actions.append(validated_action)

            # 验证断言步骤
            validated_assertions = []
            for assertion in analysis_result.get('extracted_assertions', []):
                if isinstance(assertion, dict) and 'assertion_type' in assertion:
                    validated_assertion = {
                        'step_index': assertion.get('step_index', len(validated_assertions) + 1),
                        'assertion_type': assertion.get('assertion_type', 'element_exists'),
                        'target_element': assertion.get('target_element'),
                        'expected_value': assertion.get('expected_value', ''),
                        'assertion_description': assertion.get('assertion_description', ''),
                        'ai_assertion_text': assertion.get('ai_assertion_text', assertion.get('assertion_description', ''))
                    }
                    validated_assertions.append(validated_assertion)

            # 更新分析结果
            analysis_result['extracted_actions'] = validated_actions
            analysis_result['extracted_assertions'] = validated_assertions

            # 计算质量评分
            if 'analysis_confidence' not in analysis_result:
                analysis_result['analysis_confidence'] = 0.7
            if 'completeness_score' not in analysis_result:
                analysis_result['completeness_score'] = len(validated_actions) / max(len(test_case.test_steps.split('\n')), 1)
            if 'clarity_score' not in analysis_result:
                analysis_result['clarity_score'] = 0.8

            # 添加分析元数据
            analysis_result['analysis_metadata'] = {
                'case_id': test_case.case_id,
                'case_name': test_case.case_name,
                'package_name': test_case.package_name,
                'analysis_time': datetime.now().isoformat(),
                'total_actions': len(validated_actions),
                'total_assertions': len(validated_assertions)
            }

            return analysis_result

        except Exception as e:
            logger.error(f"验证分析结果失败: {e}")
            return analysis_result

    def _match_elements(self, analysis_result: Dict[str, Any],
                       app_elements: List[UIElement]) -> Dict[str, Any]:
        """匹配UI元素"""
        try:
            element_mapping = {}

            # 为每个操作步骤匹配元素
            for action in analysis_result.get('extracted_actions', []):
                target_element = action.get('target_element')
                if target_element:
                    matched_element = self._find_matching_element(target_element, app_elements)
                    if matched_element:
                        element_mapping[f"action_{action['step_index']}"] = {
                            'target_description': target_element,
                            'matched_element_id': matched_element.element_id,
                            'element_type': matched_element.element_type,
                            'element_text': matched_element.text,
                            'element_resource_id': matched_element.resource_id,
                            'match_confidence': self._calculate_match_confidence(target_element, matched_element)
                        }

            # 为断言匹配元素
            for assertion in analysis_result.get('extracted_assertions', []):
                target_element = assertion.get('target_element')
                if target_element:
                    matched_element = self._find_matching_element(target_element, app_elements)
                    if matched_element:
                        element_mapping[f"assertion_{assertion['step_index']}"] = {
                            'target_description': target_element,
                            'matched_element_id': matched_element.element_id,
                            'element_type': matched_element.element_type,
                            'element_text': matched_element.text,
                            'element_resource_id': matched_element.resource_id,
                            'match_confidence': self._calculate_match_confidence(target_element, matched_element)
                        }

            return element_mapping

        except Exception as e:
            logger.error(f"匹配UI元素失败: {e}")
            return {}

    def _find_matching_element(self, target_description: str,
                             app_elements: List[UIElement]) -> Optional[UIElement]:
        """查找匹配的UI元素"""
        try:
            if not target_description:
                return None

            target_lower = target_description.lower()
            best_match = None
            best_score = 0.0

            for element in app_elements:
                score = 0.0

                # 文本匹配
                if element.text and target_lower in element.text.lower():
                    score += 0.8
                elif element.text and element.text.lower() in target_lower:
                    score += 0.6

                # 资源ID匹配
                if element.resource_id and target_lower in element.resource_id.lower():
                    score += 0.7

                # 语义描述匹配
                if element.semantic_description and target_lower in element.semantic_description.lower():
                    score += 0.5

                # 内容描述匹配
                if element.content_desc and target_lower in element.content_desc.lower():
                    score += 0.4

                # 更新最佳匹配
                if score > best_score and score > 0.3:  # 最低匹配阈值
                    best_score = score
                    best_match = element

            return best_match

        except Exception as e:
            logger.warning(f"查找匹配元素失败: {e}")
            return None

    def _calculate_match_confidence(self, target_description: str,
                                  matched_element: UIElement) -> float:
        """计算匹配置信度"""
        try:
            if not target_description or not matched_element:
                return 0.0

            target_lower = target_description.lower()
            confidence = 0.0

            # 精确文本匹配
            if matched_element.text and matched_element.text.lower() == target_lower:
                confidence = 1.0
            # 包含匹配
            elif matched_element.text and target_lower in matched_element.text.lower():
                confidence = 0.8
            # 资源ID匹配
            elif matched_element.resource_id and target_lower in matched_element.resource_id.lower():
                confidence = 0.7
            # 语义匹配
            elif matched_element.semantic_description and target_lower in matched_element.semantic_description.lower():
                confidence = 0.6
            else:
                confidence = 0.3

            return min(confidence, 1.0)

        except Exception as e:
            logger.warning(f"计算匹配置信度失败: {e}")
            return 0.0

    def _save_analysis_result(self, test_case: TestCase,
                            analysis_result: Dict[str, Any]) -> Optional[CaseAnalysisResult]:
        """保存分析结果到数据库"""
        try:
            # 创建分析结果对象
            case_analysis = CaseAnalysisResult(
                case_id=test_case.id,
                analysis_session_id=f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                extracted_actions=analysis_result.get('extracted_actions', []),
                extracted_assertions=analysis_result.get('extracted_assertions', []),
                matched_elements=analysis_result.get('element_mapping', {}),
                analysis_confidence=analysis_result.get('analysis_confidence', 0.0),
                completeness_score=analysis_result.get('completeness_score', 0.0),
                clarity_score=analysis_result.get('clarity_score', 0.0),
                analysis_notes=analysis_result.get('analysis_notes', ''),
                identified_risks=analysis_result.get('identified_risks', []),
                optimization_suggestions=analysis_result.get('optimization_suggestions', []),
                analyzer_agent=self.agent_name,
                analysis_model=self.config.autogen.model_name
            )

            # 保存到数据库
            saved_result = self.db_manager.save_case_analysis_result(case_analysis)

            if saved_result:
                # 更新测试用例状态
                test_case.analysis_status = 'completed'
                test_case.updated_at = datetime.now()
                self.db_manager.save_test_case(test_case)

                logger.info(f"用例分析结果保存成功: {test_case.case_name}")

            return saved_result

        except Exception as e:
            logger.error(f"保存分析结果失败: {e}")
            return None

    def get_analysis_summary(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """获取分析摘要"""
        try:
            actions = analysis_result.get('extracted_actions', [])
            assertions = analysis_result.get('extracted_assertions', [])

            # 统计操作类型
            action_types = {}
            for action in actions:
                action_type = action.get('action_type', 'unknown')
                action_types[action_type] = action_types.get(action_type, 0) + 1

            # 统计断言类型
            assertion_types = {}
            for assertion in assertions:
                assertion_type = assertion.get('assertion_type', 'unknown')
                assertion_types[assertion_type] = assertion_types.get(assertion_type, 0) + 1

            summary = {
                'total_actions': len(actions),
                'total_assertions': len(assertions),
                'action_types': action_types,
                'assertion_types': assertion_types,
                'analysis_confidence': analysis_result.get('analysis_confidence', 0.0),
                'completeness_score': analysis_result.get('completeness_score', 0.0),
                'clarity_score': analysis_result.get('clarity_score', 0.0),
                'has_element_mapping': bool(analysis_result.get('element_mapping')),
                'identified_risks_count': len(analysis_result.get('identified_risks', [])),
                'optimization_suggestions_count': len(analysis_result.get('optimization_suggestions', []))
            }

            return summary

        except Exception as e:
            logger.error(f"生成分析摘要失败: {e}")
            return {}

    def cleanup(self):
        """清理资源"""
        try:
            if self.autogen_client:
                self.autogen_client.cleanup()

            logger.info("用例分析智能体资源清理完成")

        except Exception as e:
            logger.warning(f"清理用例分析智能体资源失败: {e}")
