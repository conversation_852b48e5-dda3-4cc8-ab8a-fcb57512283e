[2025-09-01T17:22:26.729+08:00] Using commonContextParser for action: locate
[2025-09-01T17:22:48.395+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-01_17-22-26-wqc1z81j.html
[2025-09-01T17:22:51.793+08:00] Using commonContextParser for action: extract
[2025-09-01T17:22:55.707+08:00] Using commonContextParser for action: extract
[2025-09-01T17:23:05.291+08:00] Using commonContextParser for action: extract
[2025-09-01T17:23:09.123+08:00] Using commonContextParser for action: extract
[2025-09-01T17:23:21.782+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-01_17-22-26-wqc1z81j.html
[2025-09-01T17:23:45.012+08:00] Using commonContextParser for action: locate
[2025-09-01T17:23:57.726+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-01_17-23-44-3yje8jx1.html
[2025-09-01T17:23:59.217+08:00] Using commonContextParser for action: extract
[2025-09-01T17:24:01.092+08:00] Using commonContextParser for action: extract
[2025-09-01T17:24:05.321+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-01_17-23-44-3yje8jx1.html
[2025-09-01T17:24:05.321+08:00] Using commonContextParser for action: locate
[2025-09-01T17:24:11.978+08:00] Using commonContextParser for action: locate
[2025-09-01T17:24:15.401+08:00] Using commonContextParser for action: locate
[2025-09-01T17:24:19.292+08:00] Using commonContextParser for action: locate
[2025-09-01T17:24:24.665+08:00] Using commonContextParser for action: locate
[2025-09-01T17:24:29.777+08:00] Using commonContextParser for action: locate
[2025-09-01T17:24:34.005+08:00] Using commonContextParser for action: locate
[2025-09-01T17:24:37.243+08:00] Using commonContextParser for action: locate
[2025-09-01T17:24:41.173+08:00] Using commonContextParser for action: locate
[2025-09-01T17:24:46.234+08:00] Using commonContextParser for action: locate
[2025-09-01T17:24:49.696+08:00] Using commonContextParser for action: locate
[2025-09-01T17:24:53.497+08:00] Using commonContextParser for action: locate
[2025-09-01T17:24:58.299+08:00] Using commonContextParser for action: locate
[2025-09-01T17:25:01.654+08:00] Using commonContextParser for action: locate
[2025-09-01T17:25:05.497+08:00] Using commonContextParser for action: locate
[2025-09-01T17:25:10.689+08:00] Using commonContextParser for action: locate
[2025-09-01T17:25:13.817+08:00] Using commonContextParser for action: locate
[2025-09-01T17:25:17.787+08:00] Using commonContextParser for action: locate
[2025-09-01T17:25:24.164+08:00] Using commonContextParser for action: locate
[2025-09-01T17:25:29.680+08:00] Using commonContextParser for action: locate
[2025-09-01T17:25:38.143+08:00] Using commonContextParser for action: locate
[2025-09-01T17:25:43.297+08:00] Using commonContextParser for action: locate
[2025-09-01T17:25:46.707+08:00] Using commonContextParser for action: locate
[2025-09-01T17:25:49.478+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-01_17-23-44-3yje8jx1.html
[2025-09-01T17:26:13.567+08:00] Using commonContextParser for action: locate
[2025-09-01T17:26:24.096+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-01_17-26-13-x2d4nr1g.html
[2025-09-01T17:27:14.088+08:00] Using commonContextParser for action: locate
[2025-09-01T17:27:26.929+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-01_17-27-13-chy55z8e.html
[2025-09-01T17:28:23.662+08:00] Using commonContextParser for action: locate
[2025-09-01T17:28:34.403+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-01_17-28-22-hrr25i7j.html
[2025-09-01T17:28:35.990+08:00] Using commonContextParser for action: extract
[2025-09-01T17:28:38.060+08:00] Using commonContextParser for action: extract
[2025-09-01T17:28:42.856+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-01_17-28-22-hrr25i7j.html
[2025-09-01T17:28:42.857+08:00] Using commonContextParser for action: locate
[2025-09-01T17:28:50.706+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-01_17-28-22-hrr25i7j.html
[2025-09-01T17:35:50.863+08:00] Using commonContextParser for action: locate
[2025-09-01T17:36:01.671+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-01_17-35-50-x6cdh8vi.html
[2025-09-01T17:56:53.163+08:00] Using commonContextParser for action: locate
[2025-09-01T17:57:02.274+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-01_17-56-52-7ooy2g3d.html
[2025-09-01T17:57:23.809+08:00] Using commonContextParser for action: locate
[2025-09-01T17:57:31.674+08:00] Using commonContextParser for action: locate
[2025-09-01T17:57:38.793+08:00] Using commonContextParser for action: locate
[2025-09-01T17:57:46.461+08:00] Using commonContextParser for action: locate
[2025-09-01T17:57:56.595+08:00] Using commonContextParser for action: locate
[2025-09-01T17:58:04.277+08:00] Using commonContextParser for action: locate
[2025-09-01T17:58:12.091+08:00] Using commonContextParser for action: locate
[2025-09-01T17:58:20.768+08:00] Using commonContextParser for action: locate
[2025-09-01T17:58:28.101+08:00] Using commonContextParser for action: locate
[2025-09-01T17:58:35.867+08:00] Using commonContextParser for action: locate
[2025-09-01T17:58:44.647+08:00] Using commonContextParser for action: locate
[2025-09-01T17:58:51.806+08:00] Using commonContextParser for action: locate
[2025-09-01T17:58:59.565+08:00] Using commonContextParser for action: locate
[2025-09-01T17:59:07.472+08:00] Using commonContextParser for action: locate
[2025-09-01T17:59:56.805+08:00] Using commonContextParser for action: locate
[2025-09-01T18:00:02.740+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-01_17-59-56-2t1idyxr.html
[2025-09-01T18:01:00.688+08:00] Using commonContextParser for action: locate
[2025-09-01T18:01:06.785+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-01_18-01-00-w8ddzfjx.html
[2025-09-01T18:16:44.335+08:00] Using commonContextParser for action: locate
[2025-09-01T18:16:47.336+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-01_18-16-43-2d5h0kj3.html
[2025-09-01T18:19:31.363+08:00] Using commonContextParser for action: locate
[2025-09-01T18:19:34.792+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-01_18-19-30-ba6m4cma.html
[2025-09-01T18:20:06.101+08:00] Using commonContextParser for action: locate
[2025-09-01T18:20:13.375+08:00] Using commonContextParser for action: locate
[2025-09-01T18:20:17.656+08:00] Using commonContextParser for action: locate
[2025-09-01T18:20:22.389+08:00] Using commonContextParser for action: locate
[2025-09-01T18:20:31.321+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-01_18-20-05-m52zzrtz.html
[2025-09-01T18:20:36.336+08:00] Using commonContextParser for action: locate
[2025-09-01T18:20:43.473+08:00] Using commonContextParser for action: locate
[2025-09-01T18:20:47.766+08:00] Using commonContextParser for action: locate
[2025-09-01T18:20:52.873+08:00] Using commonContextParser for action: locate
[2025-09-01T18:20:59.377+08:00] Using commonContextParser for action: locate
[2025-09-01T18:21:05.820+08:00] Using commonContextParser for action: locate
[2025-09-01T18:21:11.228+08:00] Using commonContextParser for action: locate
[2025-09-01T18:21:15.795+08:00] Using commonContextParser for action: locate
[2025-09-01T18:21:20.732+08:00] Using commonContextParser for action: locate
[2025-09-01T18:21:27.481+08:00] Using commonContextParser for action: locate
[2025-09-01T18:21:33.213+08:00] Using commonContextParser for action: locate
[2025-09-01T18:21:39.221+08:00] Using commonContextParser for action: locate
[2025-09-01T18:21:43.543+08:00] Using commonContextParser for action: locate
[2025-09-01T18:21:48.313+08:00] Using commonContextParser for action: locate
[2025-09-01T18:21:55.338+08:00] Using commonContextParser for action: locate
[2025-09-01T18:22:01.405+08:00] Using commonContextParser for action: locate
[2025-09-01T18:22:06.999+08:00] Using commonContextParser for action: locate
[2025-09-01T18:22:11.159+08:00] Using commonContextParser for action: locate
[2025-09-01T18:22:15.913+08:00] Using commonContextParser for action: locate
[2025-09-01T18:22:22.976+08:00] Using commonContextParser for action: locate
[2025-09-01T18:22:28.854+08:00] Using commonContextParser for action: locate
[2025-09-01T18:22:32.219+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-01_18-20-05-m52zzrtz.html
[2025-09-01T19:17:56.435+08:00] Using commonContextParser for action: locate
[2025-09-01T19:18:05.056+08:00] Using commonContextParser for action: locate
[2025-09-01T19:18:08.702+08:00] Using commonContextParser for action: locate
[2025-09-01T19:18:12.983+08:00] Using commonContextParser for action: locate
[2025-09-01T19:18:18.645+08:00] Using commonContextParser for action: locate
[2025-09-01T19:18:22.573+08:00] Using commonContextParser for action: locate
[2025-09-01T19:18:27.108+08:00] Using commonContextParser for action: locate
[2025-09-01T19:18:32.947+08:00] Using commonContextParser for action: locate
[2025-09-01T19:18:37.344+08:00] Using commonContextParser for action: locate
[2025-09-01T19:18:42.418+08:00] Using commonContextParser for action: locate
[2025-09-01T19:18:48.591+08:00] Using commonContextParser for action: locate
[2025-09-01T19:18:53.270+08:00] Using commonContextParser for action: locate
[2025-09-01T19:18:58.695+08:00] Using commonContextParser for action: locate
[2025-09-01T19:19:05.209+08:00] Using commonContextParser for action: locate
[2025-09-01T19:19:09.766+08:00] Using commonContextParser for action: locate
[2025-09-01T19:19:14.222+08:00] Using commonContextParser for action: locate
[2025-09-01T19:19:20.901+08:00] Using commonContextParser for action: locate
[2025-09-01T19:19:25.111+08:00] Using commonContextParser for action: locate
[2025-09-01T19:19:29.985+08:00] Using commonContextParser for action: locate
[2025-09-01T19:19:36.476+08:00] Using commonContextParser for action: locate
[2025-09-01T19:19:40.114+08:00] Using commonContextParser for action: locate
[2025-09-01T19:19:44.910+08:00] Using commonContextParser for action: locate
[2025-09-01T19:19:51.078+08:00] Using commonContextParser for action: locate
[2025-09-01T19:19:54.957+08:00] Using commonContextParser for action: locate
[2025-09-01T19:20:00.808+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-01_19-17-55-vff8jz6x.html
[2025-09-01T19:29:00.614+08:00] Using commonContextParser for action: locate
[2025-09-01T19:29:24.258+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-01_19-28-59-eqsi8l8z.html
[2025-09-01T19:35:32.217+08:00] Using commonContextParser for action: locate
[2025-09-01T19:35:43.915+08:00] Using commonContextParser for action: locate
[2025-09-01T19:35:48.868+08:00] Using commonContextParser for action: locate
[2025-09-01T19:35:54.220+08:00] Using commonContextParser for action: locate
[2025-09-01T19:36:03.275+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-01_19-35-31-zw2173vm.html
[2025-09-10T10:57:52.561+08:00] Using commonContextParser for action: locate
[2025-09-10T10:58:00.855+08:00] Using commonContextParser for action: locate
[2025-09-10T10:58:04.887+08:00] Using commonContextParser for action: locate
[2025-09-10T10:58:09.383+08:00] Using commonContextParser for action: locate
[2025-09-10T10:58:21.284+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_10-57-51-ohxt6oyc.html
[2025-09-10T10:58:26.296+08:00] Using commonContextParser for action: locate
[2025-09-10T11:01:16.105+08:00] Using commonContextParser for action: extract
[2025-09-10T11:01:17.885+08:00] Using commonContextParser for action: extract
[2025-09-10T11:01:27.873+08:00] Using commonContextParser for action: extract
[2025-09-10T11:01:31.510+08:00] Using commonContextParser for action: extract
[2025-09-10T11:01:43.445+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_11-01-14-8povym9t.html
[2025-09-10T11:02:09.730+08:00] Using commonContextParser for action: extract
[2025-09-10T11:02:11.650+08:00] Using commonContextParser for action: extract
[2025-09-10T11:02:17.397+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_11-02-02-391u2d7x.html
[2025-09-10T11:02:17.398+08:00] callActionInActionSpace Input , {
  value: "what's the weather today",
  locate: {
    prompt: '底部输入框',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  value: "what's the weather today",
  locate: {
    prompt: '底部输入框',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T11:02:17.398+08:00] actionPlan {
  type: 'Input',
  param: {
    value: "what's the weather today",
    locate: {
      prompt: '底部输入框',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T11:02:17.401+08:00] Using commonContextParser for action: locate
[2025-09-10T11:02:25.030+08:00] Using commonContextParser for action: locate
[2025-09-10T11:02:31.903+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_11-02-02-391u2d7x.html
[2025-09-10T11:02:33.203+08:00] Using commonContextParser for action: extract
[2025-09-10T11:02:34.868+08:00] Using commonContextParser for action: extract
[2025-09-10T11:02:40.658+08:00] Using commonContextParser for action: extract
[2025-09-10T11:02:42.487+08:00] Using commonContextParser for action: extract
[2025-09-10T11:02:48.130+08:00] Using commonContextParser for action: extract
[2025-09-10T11:02:49.925+08:00] Using commonContextParser for action: extract
[2025-09-10T11:02:57.170+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_11-02-02-391u2d7x.html
[2025-09-10T11:03:11.590+08:00] Using commonContextParser for action: extract
[2025-09-10T11:03:16.248+08:00] Using commonContextParser for action: extract
[2025-09-10T11:03:33.766+08:00] Using commonContextParser for action: extract
[2025-09-10T11:03:35.535+08:00] Using commonContextParser for action: extract
[2025-09-10T11:03:40.534+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_11-03-26-vlm3dtbt.html
[2025-09-10T11:03:40.535+08:00] callActionInActionSpace Input , {
  value: "what's the weather today",
  locate: {
    prompt: '底部输入框',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  value: "what's the weather today",
  locate: {
    prompt: '底部输入框',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T11:03:40.535+08:00] actionPlan {
  type: 'Input',
  param: {
    value: "what's the weather today",
    locate: {
      prompt: '底部输入框',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T11:03:40.538+08:00] Using commonContextParser for action: locate
[2025-09-10T11:03:46.967+08:00] Using commonContextParser for action: locate
[2025-09-10T11:03:53.363+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_11-03-26-vlm3dtbt.html
[2025-09-10T11:03:53.363+08:00] callActionInActionSpace Tap , {
  locate: {
    prompt: '发送按钮',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  locate: {
    prompt: '发送按钮',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T11:03:53.363+08:00] actionPlan {
  type: 'Tap',
  param: {
    locate: {
      prompt: '发送按钮',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T11:03:53.364+08:00] Using commonContextParser for action: locate
[2025-09-10T11:04:00.680+08:00] Using commonContextParser for action: locate
[2025-09-10T11:04:04.391+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_11-03-26-vlm3dtbt.html
[2025-09-10T11:04:05.757+08:00] Using commonContextParser for action: extract
[2025-09-10T11:04:07.518+08:00] Using commonContextParser for action: extract
[2025-09-10T11:04:12.380+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_11-03-26-vlm3dtbt.html
[2025-09-10T11:04:42.141+08:00] Using commonContextParser for action: extract
[2025-09-10T11:04:43.775+08:00] Using commonContextParser for action: extract
[2025-09-10T11:04:48.118+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_11-04-35-tf7hk5jd.html
[2025-09-10T11:04:48.119+08:00] callActionInActionSpace Input , {
  value: "what's the weather today",
  locate: {
    prompt: '底部输入框',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  value: "what's the weather today",
  locate: {
    prompt: '底部输入框',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T11:04:48.119+08:00] actionPlan {
  type: 'Input',
  param: {
    value: "what's the weather today",
    locate: {
      prompt: '底部输入框',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T11:04:48.121+08:00] Using commonContextParser for action: locate
[2025-09-10T11:04:55.176+08:00] Using commonContextParser for action: locate
[2025-09-10T11:05:01.518+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_11-04-35-tf7hk5jd.html
[2025-09-10T11:05:01.518+08:00] callActionInActionSpace Tap , {
  locate: {
    prompt: '发送按钮',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  locate: {
    prompt: '发送按钮',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T11:05:01.518+08:00] actionPlan {
  type: 'Tap',
  param: {
    locate: {
      prompt: '发送按钮',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T11:05:01.519+08:00] Using commonContextParser for action: locate
[2025-09-10T11:05:09.457+08:00] Using commonContextParser for action: locate
[2025-09-10T11:05:12.931+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_11-04-35-tf7hk5jd.html
[2025-09-10T11:05:14.225+08:00] Using commonContextParser for action: extract
[2025-09-10T11:05:15.948+08:00] Using commonContextParser for action: extract
[2025-09-10T11:05:20.393+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_11-04-35-tf7hk5jd.html
[2025-09-10T11:05:20.394+08:00] Using commonContextParser for action: locate
[2025-09-10T11:05:29.533+08:00] Using commonContextParser for action: locate
[2025-09-10T11:05:32.631+08:00] Using commonContextParser for action: locate
[2025-09-10T11:05:36.161+08:00] Using commonContextParser for action: locate
[2025-09-10T11:05:43.463+08:00] Using commonContextParser for action: locate
[2025-09-10T11:05:46.609+08:00] Using commonContextParser for action: locate
[2025-09-10T11:05:50.144+08:00] Using commonContextParser for action: locate
[2025-09-10T11:05:57.245+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_11-04-35-tf7hk5jd.html
[2025-09-10T11:05:58.600+08:00] Using commonContextParser for action: extract
[2025-09-10T11:06:00.339+08:00] Using commonContextParser for action: extract
[2025-09-10T11:06:06.984+08:00] Using commonContextParser for action: extract
[2025-09-10T11:06:08.825+08:00] Using commonContextParser for action: extract
[2025-09-10T11:10:42.973+08:00] Using commonContextParser for action: extract
[2025-09-10T11:10:44.763+08:00] Using commonContextParser for action: extract
[2025-09-10T11:10:49.467+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_11-10-35-bowfhzf5.html
[2025-09-10T11:10:49.468+08:00] callActionInActionSpace Input , {
  value: "what's the weather today",
  locate: {
    prompt: '底部输入框',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  value: "what's the weather today",
  locate: {
    prompt: '底部输入框',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T11:10:49.468+08:00] actionPlan {
  type: 'Input',
  param: {
    value: "what's the weather today",
    locate: {
      prompt: '底部输入框',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T11:10:49.469+08:00] Using commonContextParser for action: locate
[2025-09-10T11:10:57.574+08:00] Using commonContextParser for action: locate
[2025-09-10T11:11:04.037+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_11-10-35-bowfhzf5.html
[2025-09-10T11:11:04.038+08:00] callActionInActionSpace Tap , {
  locate: {
    prompt: '发送按钮',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  locate: {
    prompt: '发送按钮',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T11:11:04.038+08:00] actionPlan {
  type: 'Tap',
  param: {
    locate: {
      prompt: '发送按钮',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T11:11:04.038+08:00] Using commonContextParser for action: locate
[2025-09-10T11:11:11.366+08:00] Using commonContextParser for action: locate
[2025-09-10T11:11:15.014+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_11-10-35-bowfhzf5.html
[2025-09-10T11:11:16.298+08:00] Using commonContextParser for action: extract
[2025-09-10T11:11:18.048+08:00] Using commonContextParser for action: extract
[2025-09-10T11:11:22.345+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_11-10-35-bowfhzf5.html
[2025-09-10T12:06:36.808+08:00] Using commonContextParser for action: extract
[2025-09-10T12:06:38.634+08:00] Using commonContextParser for action: extract
[2025-09-10T12:06:42.756+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-06-29-8z91iu90.html
[2025-09-10T12:06:42.757+08:00] callActionInActionSpace Input , {
  value: "what's the weather today",
  locate: {
    prompt: '底部输入框',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  value: "what's the weather today",
  locate: {
    prompt: '底部输入框',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T12:06:42.757+08:00] actionPlan {
  type: 'Input',
  param: {
    value: "what's the weather today",
    locate: {
      prompt: '底部输入框',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T12:06:42.758+08:00] Using commonContextParser for action: locate
[2025-09-10T12:06:49.555+08:00] Using commonContextParser for action: locate
[2025-09-10T12:06:55.915+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-06-29-8z91iu90.html
[2025-09-10T12:06:55.915+08:00] callActionInActionSpace Tap , {
  locate: {
    prompt: '发送按钮',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  locate: {
    prompt: '发送按钮',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T12:06:55.915+08:00] actionPlan {
  type: 'Tap',
  param: {
    locate: {
      prompt: '发送按钮',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T12:06:55.916+08:00] Using commonContextParser for action: locate
[2025-09-10T12:07:02.339+08:00] Using commonContextParser for action: locate
[2025-09-10T12:07:05.995+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-06-29-8z91iu90.html
[2025-09-10T12:07:07.476+08:00] Using commonContextParser for action: extract
[2025-09-10T12:07:09.327+08:00] Using commonContextParser for action: extract
[2025-09-10T12:07:14.000+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-06-29-8z91iu90.html
[2025-09-10T12:07:14.001+08:00] callActionInActionSpace Scroll , { locate: undefined } , { locate: undefined }
[2025-09-10T12:07:14.001+08:00] actionPlan { type: 'Scroll', param: { locate: undefined }, thought: '' }
[2025-09-10T12:07:15.220+08:00] Using commonContextParser for action: locate
[2025-09-10T12:07:20.734+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-06-29-8z91iu90.html
[2025-09-10T12:07:20.735+08:00] callActionInActionSpace Tap , {
  locate: {
    prompt: '踩icon',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  locate: {
    prompt: '踩icon',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T12:07:20.735+08:00] actionPlan {
  type: 'Tap',
  param: {
    locate: {
      prompt: '踩icon',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T12:07:20.736+08:00] Using commonContextParser for action: locate
[2025-09-10T12:07:28.636+08:00] Using commonContextParser for action: locate
[2025-09-10T12:07:32.220+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-06-29-8z91iu90.html
[2025-09-10T12:07:33.528+08:00] Using commonContextParser for action: extract
[2025-09-10T12:07:35.220+08:00] Using commonContextParser for action: extract
[2025-09-10T12:07:43.945+08:00] Using commonContextParser for action: extract
[2025-09-10T12:07:45.728+08:00] Using commonContextParser for action: extract
[2025-09-10T12:07:54.705+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-06-29-8z91iu90.html
[2025-09-10T12:11:03.297+08:00] Using commonContextParser for action: extract
[2025-09-10T12:11:06.844+08:00] Using commonContextParser for action: extract
[2025-09-10T12:11:19.562+08:00] Using commonContextParser for action: extract
[2025-09-10T12:11:21.436+08:00] Using commonContextParser for action: extract
[2025-09-10T12:11:26.009+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-11-12-x628i90v.html
[2025-09-10T12:11:26.010+08:00] callActionInActionSpace Input , {
  value: "what's the weather today",
  locate: {
    prompt: '底部输入框',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  value: "what's the weather today",
  locate: {
    prompt: '底部输入框',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T12:11:26.010+08:00] actionPlan {
  type: 'Input',
  param: {
    value: "what's the weather today",
    locate: {
      prompt: '底部输入框',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T12:11:26.012+08:00] Using commonContextParser for action: locate
[2025-09-10T12:11:36.067+08:00] Using commonContextParser for action: locate
[2025-09-10T12:11:42.467+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-11-12-x628i90v.html
[2025-09-10T12:11:42.468+08:00] callActionInActionSpace Tap , {
  locate: {
    prompt: '发送按钮',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  locate: {
    prompt: '发送按钮',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T12:11:42.468+08:00] actionPlan {
  type: 'Tap',
  param: {
    locate: {
      prompt: '发送按钮',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T12:11:42.468+08:00] Using commonContextParser for action: locate
[2025-09-10T12:11:49.559+08:00] Using commonContextParser for action: locate
[2025-09-10T12:11:53.092+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-11-12-x628i90v.html
[2025-09-10T12:11:54.333+08:00] Using commonContextParser for action: extract
[2025-09-10T12:11:56.115+08:00] Using commonContextParser for action: extract
[2025-09-10T12:12:01.295+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-11-12-x628i90v.html
[2025-09-10T12:12:01.295+08:00] callActionInActionSpace Scroll , { locate: undefined } , { locate: undefined }
[2025-09-10T12:12:01.295+08:00] actionPlan { type: 'Scroll', param: { locate: undefined }, thought: '' }
[2025-09-10T12:12:02.522+08:00] Using commonContextParser for action: locate
[2025-09-10T12:12:08.191+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-11-12-x628i90v.html
[2025-09-10T12:12:08.192+08:00] callActionInActionSpace Tap , {
  locate: {
    prompt: '踩icon',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  locate: {
    prompt: '踩icon',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T12:12:08.192+08:00] actionPlan {
  type: 'Tap',
  param: {
    locate: {
      prompt: '踩icon',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T12:12:08.192+08:00] Using commonContextParser for action: locate
[2025-09-10T12:12:17.413+08:00] Using commonContextParser for action: locate
[2025-09-10T12:12:21.095+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-11-12-x628i90v.html
[2025-09-10T12:12:22.388+08:00] Using commonContextParser for action: extract
[2025-09-10T12:12:24.061+08:00] Using commonContextParser for action: extract
[2025-09-10T12:12:29.676+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-11-12-x628i90v.html
[2025-09-10T12:13:17.128+08:00] Using commonContextParser for action: extract
[2025-09-10T12:13:19.022+08:00] Using commonContextParser for action: extract
[2025-09-10T12:13:23.375+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-13-12-csjacfbq.html
[2025-09-10T12:13:23.376+08:00] callActionInActionSpace Input , {
  value: "what's the weather today",
  locate: {
    prompt: '底部输入框',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  value: "what's the weather today",
  locate: {
    prompt: '底部输入框',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T12:13:23.376+08:00] actionPlan {
  type: 'Input',
  param: {
    value: "what's the weather today",
    locate: {
      prompt: '底部输入框',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T12:13:23.377+08:00] Using commonContextParser for action: locate
[2025-09-10T12:13:30.005+08:00] Using commonContextParser for action: locate
[2025-09-10T12:13:36.363+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-13-12-csjacfbq.html
[2025-09-10T12:13:36.364+08:00] callActionInActionSpace Tap , {
  locate: {
    prompt: '发送按钮',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  locate: {
    prompt: '发送按钮',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T12:13:36.364+08:00] actionPlan {
  type: 'Tap',
  param: {
    locate: {
      prompt: '发送按钮',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T12:13:36.364+08:00] Using commonContextParser for action: locate
[2025-09-10T12:13:43.595+08:00] Using commonContextParser for action: locate
[2025-09-10T12:13:47.164+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-13-12-csjacfbq.html
[2025-09-10T12:13:48.474+08:00] Using commonContextParser for action: extract
[2025-09-10T12:13:50.194+08:00] Using commonContextParser for action: extract
[2025-09-10T12:13:54.678+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-13-12-csjacfbq.html
[2025-09-10T12:13:54.678+08:00] callActionInActionSpace Scroll , { locate: undefined } , { locate: undefined }
[2025-09-10T12:13:54.678+08:00] actionPlan { type: 'Scroll', param: { locate: undefined }, thought: '' }
[2025-09-10T12:13:55.982+08:00] Using commonContextParser for action: locate
[2025-09-10T12:14:01.652+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-13-12-csjacfbq.html
[2025-09-10T12:14:01.653+08:00] callActionInActionSpace Tap , {
  locate: {
    prompt: '踩icon',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  locate: {
    prompt: '踩icon',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T12:14:01.653+08:00] actionPlan {
  type: 'Tap',
  param: {
    locate: {
      prompt: '踩icon',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T12:14:01.653+08:00] Using commonContextParser for action: locate
[2025-09-10T12:14:09.871+08:00] Using commonContextParser for action: locate
[2025-09-10T12:14:13.412+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-13-12-csjacfbq.html
[2025-09-10T12:14:14.759+08:00] Using commonContextParser for action: extract
[2025-09-10T12:14:16.522+08:00] Using commonContextParser for action: extract
[2025-09-10T12:14:20.279+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-13-12-csjacfbq.html
[2025-09-10T12:14:21.612+08:00] Using commonContextParser for action: extract
[2025-09-10T12:14:23.316+08:00] Using commonContextParser for action: extract
[2025-09-10T12:14:27.197+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-13-12-csjacfbq.html
[2025-09-10T12:14:28.491+08:00] Using commonContextParser for action: extract
[2025-09-10T12:14:30.346+08:00] Using commonContextParser for action: extract
[2025-09-10T12:14:34.599+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-13-12-csjacfbq.html
[2025-09-10T12:14:35.910+08:00] Using commonContextParser for action: extract
[2025-09-10T12:14:37.695+08:00] Using commonContextParser for action: extract
[2025-09-10T12:14:42.032+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-13-12-csjacfbq.html
[2025-09-10T12:15:59.930+08:00] Using commonContextParser for action: extract
[2025-09-10T12:16:01.819+08:00] Using commonContextParser for action: extract
[2025-09-10T12:16:06.235+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-15-55-rs5ki1hd.html
[2025-09-10T12:16:06.237+08:00] callActionInActionSpace Input , {
  value: "what's the weather today",
  locate: {
    prompt: '底部输入框',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  value: "what's the weather today",
  locate: {
    prompt: '底部输入框',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T12:16:06.237+08:00] actionPlan {
  type: 'Input',
  param: {
    value: "what's the weather today",
    locate: {
      prompt: '底部输入框',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T12:16:06.238+08:00] Using commonContextParser for action: locate
[2025-09-10T12:16:13.743+08:00] Using commonContextParser for action: locate
[2025-09-10T12:16:20.157+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-15-55-rs5ki1hd.html
[2025-09-10T12:16:20.157+08:00] callActionInActionSpace Tap , {
  locate: {
    prompt: '发送按钮',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  locate: {
    prompt: '发送按钮',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T12:16:20.157+08:00] actionPlan {
  type: 'Tap',
  param: {
    locate: {
      prompt: '发送按钮',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T12:16:20.158+08:00] Using commonContextParser for action: locate
[2025-09-10T12:16:27.697+08:00] Using commonContextParser for action: locate
[2025-09-10T12:16:31.247+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-15-55-rs5ki1hd.html
[2025-09-10T12:16:32.575+08:00] Using commonContextParser for action: extract
[2025-09-10T12:16:34.389+08:00] Using commonContextParser for action: extract
[2025-09-10T12:16:38.701+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-15-55-rs5ki1hd.html
[2025-09-10T12:16:38.701+08:00] callActionInActionSpace Scroll , { locate: undefined } , { locate: undefined }
[2025-09-10T12:16:38.701+08:00] actionPlan { type: 'Scroll', param: { locate: undefined }, thought: '' }
[2025-09-10T12:16:40.042+08:00] Using commonContextParser for action: locate
[2025-09-10T12:16:45.737+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-15-55-rs5ki1hd.html
[2025-09-10T12:16:45.737+08:00] callActionInActionSpace Tap , {
  locate: {
    prompt: '踩icon',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  locate: {
    prompt: '踩icon',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T12:16:45.737+08:00] actionPlan {
  type: 'Tap',
  param: {
    locate: {
      prompt: '踩icon',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T12:16:45.738+08:00] Using commonContextParser for action: locate
[2025-09-10T12:16:52.669+08:00] Using commonContextParser for action: locate
[2025-09-10T12:16:56.241+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-15-55-rs5ki1hd.html
[2025-09-10T12:16:57.497+08:00] Using commonContextParser for action: extract
[2025-09-10T12:16:59.191+08:00] Using commonContextParser for action: extract
[2025-09-10T12:17:03.856+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-15-55-rs5ki1hd.html
[2025-09-10T12:17:05.182+08:00] Using commonContextParser for action: extract
[2025-09-10T12:17:06.953+08:00] Using commonContextParser for action: extract
[2025-09-10T12:17:11.374+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-15-55-rs5ki1hd.html
[2025-09-10T12:17:12.665+08:00] Using commonContextParser for action: extract
[2025-09-10T12:17:14.366+08:00] Using commonContextParser for action: extract
[2025-09-10T12:17:18.642+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-15-55-rs5ki1hd.html
[2025-09-10T12:17:19.931+08:00] Using commonContextParser for action: extract
[2025-09-10T12:17:21.617+08:00] Using commonContextParser for action: extract
[2025-09-10T12:17:26.474+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-15-55-rs5ki1hd.html
[2025-09-10T12:17:26.476+08:00] Using commonContextParser for action: locate
[2025-09-10T12:17:33.964+08:00] Using commonContextParser for action: locate
[2025-09-10T12:17:36.980+08:00] Using commonContextParser for action: locate
[2025-09-10T12:17:40.510+08:00] Using commonContextParser for action: locate
[2025-09-10T12:17:47.197+08:00] Using commonContextParser for action: locate
[2025-09-10T12:17:50.330+08:00] Using commonContextParser for action: locate
[2025-09-10T12:17:53.714+08:00] Using commonContextParser for action: locate
[2025-09-10T12:18:01.673+08:00] Using commonContextParser for action: locate
[2025-09-10T12:18:04.608+08:00] Using commonContextParser for action: locate
[2025-09-10T12:18:08.060+08:00] Using commonContextParser for action: locate
[2025-09-10T12:18:15.472+08:00] Using commonContextParser for action: locate
[2025-09-10T12:18:18.465+08:00] Using commonContextParser for action: locate
[2025-09-10T12:18:22.054+08:00] Using commonContextParser for action: locate
[2025-09-10T12:18:29.303+08:00] Using commonContextParser for action: locate
[2025-09-10T12:18:32.292+08:00] Using commonContextParser for action: locate
[2025-09-10T12:18:35.773+08:00] Using commonContextParser for action: locate
[2025-09-10T12:18:43.274+08:00] Using commonContextParser for action: locate
[2025-09-10T12:18:46.290+08:00] Using commonContextParser for action: locate
[2025-09-10T12:18:49.835+08:00] Using commonContextParser for action: locate
[2025-09-10T12:18:56.364+08:00] Using commonContextParser for action: locate
[2025-09-10T12:18:59.496+08:00] Using commonContextParser for action: locate
[2025-09-10T12:19:02.929+08:00] Using commonContextParser for action: locate
[2025-09-10T12:20:05.813+08:00] Using commonContextParser for action: extract
[2025-09-10T12:20:07.614+08:00] Using commonContextParser for action: extract
[2025-09-10T12:20:12.330+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-20-01-suneg47g.html
[2025-09-10T12:20:12.331+08:00] callActionInActionSpace Input , {
  value: "what's the weather today",
  locate: {
    prompt: '底部输入框',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  value: "what's the weather today",
  locate: {
    prompt: '底部输入框',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T12:20:12.332+08:00] actionPlan {
  type: 'Input',
  param: {
    value: "what's the weather today",
    locate: {
      prompt: '底部输入框',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T12:20:12.333+08:00] Using commonContextParser for action: locate
[2025-09-10T12:20:19.206+08:00] Using commonContextParser for action: locate
[2025-09-10T12:20:25.662+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-20-01-suneg47g.html
[2025-09-10T12:20:25.663+08:00] callActionInActionSpace Tap , {
  locate: {
    prompt: '发送按钮',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  locate: {
    prompt: '发送按钮',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T12:20:25.663+08:00] actionPlan {
  type: 'Tap',
  param: {
    locate: {
      prompt: '发送按钮',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T12:20:25.663+08:00] Using commonContextParser for action: locate
[2025-09-10T12:20:32.649+08:00] Using commonContextParser for action: locate
[2025-09-10T12:20:36.165+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-20-01-suneg47g.html
[2025-09-10T12:20:37.448+08:00] Using commonContextParser for action: extract
[2025-09-10T12:20:39.268+08:00] Using commonContextParser for action: extract
[2025-09-10T12:20:44.006+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-20-01-suneg47g.html
[2025-09-10T12:20:44.006+08:00] callActionInActionSpace Scroll , { locate: undefined } , { locate: undefined }
[2025-09-10T12:20:44.006+08:00] actionPlan { type: 'Scroll', param: { locate: undefined }, thought: '' }
[2025-09-10T12:20:45.335+08:00] Using commonContextParser for action: locate
[2025-09-10T12:20:51.214+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-20-01-suneg47g.html
[2025-09-10T12:20:51.215+08:00] callActionInActionSpace Tap , {
  locate: {
    prompt: '踩icon',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  locate: {
    prompt: '踩icon',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T12:20:51.215+08:00] actionPlan {
  type: 'Tap',
  param: {
    locate: {
      prompt: '踩icon',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T12:20:51.215+08:00] Using commonContextParser for action: locate
[2025-09-10T12:20:59.360+08:00] Using commonContextParser for action: locate
[2025-09-10T12:21:03.063+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-20-01-suneg47g.html
[2025-09-10T12:21:04.351+08:00] Using commonContextParser for action: extract
[2025-09-10T12:21:06.064+08:00] Using commonContextParser for action: extract
[2025-09-10T12:21:12.030+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-20-01-suneg47g.html
[2025-09-10T12:21:13.314+08:00] Using commonContextParser for action: extract
[2025-09-10T12:21:15.046+08:00] Using commonContextParser for action: extract
[2025-09-10T12:21:19.747+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-20-01-suneg47g.html
[2025-09-10T12:21:21.049+08:00] Using commonContextParser for action: extract
[2025-09-10T12:21:22.719+08:00] Using commonContextParser for action: extract
[2025-09-10T12:21:27.222+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-20-01-suneg47g.html
[2025-09-10T12:21:28.534+08:00] Using commonContextParser for action: extract
[2025-09-10T12:21:30.315+08:00] Using commonContextParser for action: extract
[2025-09-10T12:21:34.885+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-20-01-suneg47g.html
[2025-09-10T12:21:34.886+08:00] Using commonContextParser for action: locate
[2025-09-10T12:21:41.368+08:00] Using commonContextParser for action: locate
[2025-09-10T12:21:44.292+08:00] Using commonContextParser for action: locate
[2025-09-10T12:21:47.709+08:00] Using commonContextParser for action: locate
[2025-09-10T12:21:54.426+08:00] Using commonContextParser for action: locate
[2025-09-10T12:21:57.530+08:00] Using commonContextParser for action: locate
[2025-09-10T12:22:00.979+08:00] Using commonContextParser for action: locate
[2025-09-10T12:22:08.065+08:00] Using commonContextParser for action: locate
[2025-09-10T12:22:11.131+08:00] Using commonContextParser for action: locate
[2025-09-10T12:22:14.535+08:00] Using commonContextParser for action: locate
[2025-09-10T12:22:22.895+08:00] Using commonContextParser for action: locate
[2025-09-10T12:22:25.823+08:00] Using commonContextParser for action: locate
[2025-09-10T12:22:29.180+08:00] Using commonContextParser for action: locate
[2025-09-10T12:22:35.592+08:00] Using commonContextParser for action: locate
[2025-09-10T12:22:38.681+08:00] Using commonContextParser for action: locate
[2025-09-10T12:22:53.905+08:00] Using commonContextParser for action: extract
[2025-09-10T12:22:55.847+08:00] Using commonContextParser for action: extract
[2025-09-10T12:23:00.394+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-22-49-i74k4q2g.html
[2025-09-10T12:23:00.395+08:00] callActionInActionSpace Input , {
  value: "what's the weather today",
  locate: {
    prompt: '底部输入框',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  value: "what's the weather today",
  locate: {
    prompt: '底部输入框',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T12:23:00.395+08:00] actionPlan {
  type: 'Input',
  param: {
    value: "what's the weather today",
    locate: {
      prompt: '底部输入框',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T12:23:00.396+08:00] Using commonContextParser for action: locate
[2025-09-10T12:23:07.481+08:00] Using commonContextParser for action: locate
[2025-09-10T12:23:13.880+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-22-49-i74k4q2g.html
[2025-09-10T12:23:13.880+08:00] callActionInActionSpace Tap , {
  locate: {
    prompt: '发送按钮',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  locate: {
    prompt: '发送按钮',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T12:23:13.880+08:00] actionPlan {
  type: 'Tap',
  param: {
    locate: {
      prompt: '发送按钮',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T12:23:13.881+08:00] Using commonContextParser for action: locate
[2025-09-10T12:23:20.974+08:00] Using commonContextParser for action: locate
[2025-09-10T12:23:24.410+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-22-49-i74k4q2g.html
[2025-09-10T12:23:25.707+08:00] Using commonContextParser for action: extract
[2025-09-10T12:23:27.580+08:00] Using commonContextParser for action: extract
[2025-09-10T12:23:32.304+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-22-49-i74k4q2g.html
[2025-09-10T12:23:32.305+08:00] callActionInActionSpace Scroll , { locate: undefined } , { locate: undefined }
[2025-09-10T12:23:32.305+08:00] actionPlan { type: 'Scroll', param: { locate: undefined }, thought: '' }
[2025-09-10T12:23:33.588+08:00] Using commonContextParser for action: locate
[2025-09-10T12:23:39.400+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-22-49-i74k4q2g.html
[2025-09-10T12:23:39.401+08:00] callActionInActionSpace Tap , {
  locate: {
    prompt: '踩icon',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  locate: {
    prompt: '踩icon',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T12:23:39.401+08:00] actionPlan {
  type: 'Tap',
  param: {
    locate: {
      prompt: '踩icon',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T12:23:39.401+08:00] Using commonContextParser for action: locate
[2025-09-10T12:23:52.727+08:00] Using commonContextParser for action: locate
[2025-09-10T12:23:56.141+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-22-49-i74k4q2g.html
[2025-09-10T12:23:57.369+08:00] Using commonContextParser for action: extract
[2025-09-10T12:23:59.175+08:00] Using commonContextParser for action: extract
[2025-09-10T12:24:04.134+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-22-49-i74k4q2g.html
[2025-09-10T12:24:05.489+08:00] Using commonContextParser for action: extract
[2025-09-10T12:24:07.238+08:00] Using commonContextParser for action: extract
[2025-09-10T12:24:11.314+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-22-49-i74k4q2g.html
[2025-09-10T12:24:12.592+08:00] Using commonContextParser for action: extract
[2025-09-10T12:24:14.345+08:00] Using commonContextParser for action: extract
[2025-09-10T12:24:18.642+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-22-49-i74k4q2g.html
[2025-09-10T12:24:19.910+08:00] Using commonContextParser for action: extract
[2025-09-10T12:24:21.623+08:00] Using commonContextParser for action: extract
[2025-09-10T12:24:26.269+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-22-49-i74k4q2g.html
[2025-09-10T12:24:26.270+08:00] callActionInActionSpace Tap , {
  locate: {
    prompt: 'other/suggestions',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  locate: {
    prompt: 'other/suggestions',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T12:24:26.270+08:00] actionPlan {
  type: 'Tap',
  param: {
    locate: {
      prompt: 'other/suggestions',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T12:24:26.270+08:00] Using commonContextParser for action: locate
[2025-09-10T12:24:33.515+08:00] Using commonContextParser for action: locate
[2025-09-10T12:24:37.112+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-22-49-i74k4q2g.html
[2025-09-10T12:24:38.428+08:00] Using commonContextParser for action: extract
[2025-09-10T12:24:40.096+08:00] Using commonContextParser for action: extract
[2025-09-10T12:24:44.247+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-22-49-i74k4q2g.html
[2025-09-10T12:24:45.530+08:00] Using commonContextParser for action: extract
[2025-09-10T12:24:47.169+08:00] Using commonContextParser for action: extract
[2025-09-10T12:24:51.906+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_12-22-49-i74k4q2g.html
[2025-09-10T14:51:41.203+08:00] Using commonContextParser for action: extract
[2025-09-10T14:51:43.880+08:00] Using commonContextParser for action: extract
[2025-09-10T14:51:50.170+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_14-51-35-3vorpp4r.html
[2025-09-10T14:51:50.172+08:00] callActionInActionSpace Input , {
  value: "what's the weather today",
  locate: {
    prompt: '底部输入框',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  value: "what's the weather today",
  locate: {
    prompt: '底部输入框',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T14:51:50.172+08:00] actionPlan {
  type: 'Input',
  param: {
    value: "what's the weather today",
    locate: {
      prompt: '底部输入框',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T14:51:50.175+08:00] Using commonContextParser for action: locate
[2025-09-10T14:52:01.424+08:00] Using commonContextParser for action: locate
[2025-09-10T14:52:11.579+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_14-51-35-3vorpp4r.html
[2025-09-10T14:52:11.583+08:00] callActionInActionSpace Tap , {
  locate: {
    prompt: '发送按钮',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  locate: {
    prompt: '发送按钮',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T14:52:11.583+08:00] actionPlan {
  type: 'Tap',
  param: {
    locate: {
      prompt: '发送按钮',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T14:52:11.585+08:00] Using commonContextParser for action: locate
[2025-09-10T14:52:20.202+08:00] Using commonContextParser for action: locate
[2025-09-10T14:52:25.285+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_14-51-35-3vorpp4r.html
[2025-09-10T14:52:26.890+08:00] Using commonContextParser for action: extract
[2025-09-10T14:52:29.351+08:00] Using commonContextParser for action: extract
[2025-09-10T14:52:35.017+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_14-51-35-3vorpp4r.html
[2025-09-10T14:52:35.019+08:00] callActionInActionSpace Scroll , { locate: undefined } , { locate: undefined }
[2025-09-10T14:52:35.019+08:00] actionPlan { type: 'Scroll', param: { locate: undefined }, thought: '' }
[2025-09-10T14:52:36.839+08:00] Using commonContextParser for action: locate
[2025-09-10T14:52:44.450+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_14-51-35-3vorpp4r.html
[2025-09-10T14:52:44.451+08:00] callActionInActionSpace Tap , {
  locate: {
    prompt: '踩icon',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  locate: {
    prompt: '踩icon',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T14:52:44.451+08:00] actionPlan {
  type: 'Tap',
  param: {
    locate: {
      prompt: '踩icon',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T14:52:44.453+08:00] Using commonContextParser for action: locate
[2025-09-10T14:52:55.773+08:00] Using commonContextParser for action: locate
[2025-09-10T14:53:00.688+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_14-51-35-3vorpp4r.html
[2025-09-10T14:53:02.694+08:00] Using commonContextParser for action: extract
[2025-09-10T14:53:05.384+08:00] Using commonContextParser for action: extract
[2025-09-10T14:53:11.500+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_14-51-35-3vorpp4r.html
[2025-09-10T14:53:13.343+08:00] Using commonContextParser for action: extract
[2025-09-10T14:53:15.679+08:00] Using commonContextParser for action: extract
[2025-09-10T14:53:21.059+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_14-51-35-3vorpp4r.html
[2025-09-10T14:53:22.642+08:00] Using commonContextParser for action: extract
[2025-09-10T14:53:25.134+08:00] Using commonContextParser for action: extract
[2025-09-10T14:53:31.093+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_14-51-35-3vorpp4r.html
[2025-09-10T14:53:33.045+08:00] Using commonContextParser for action: extract
[2025-09-10T14:53:35.534+08:00] Using commonContextParser for action: extract
[2025-09-10T14:53:41.992+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_14-51-35-3vorpp4r.html
[2025-09-10T14:53:41.999+08:00] callActionInActionSpace Tap , {
  locate: {
    prompt: 'other/suggestions',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
} , {
  locate: {
    prompt: 'other/suggestions',
    deepThink: false,
    cacheable: true,
    xpath: undefined
  }
}
[2025-09-10T14:53:41.999+08:00] actionPlan {
  type: 'Tap',
  param: {
    locate: {
      prompt: 'other/suggestions',
      deepThink: false,
      cacheable: true,
      xpath: undefined
    }
  },
  thought: ''
}
[2025-09-10T14:53:42.002+08:00] Using commonContextParser for action: locate
[2025-09-10T14:54:24.490+08:00] Using commonContextParser for action: locate
[2025-09-10T14:54:28.799+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_14-51-35-3vorpp4r.html
[2025-09-10T14:54:30.438+08:00] Using commonContextParser for action: extract
[2025-09-10T14:54:32.897+08:00] Using commonContextParser for action: extract
[2025-09-10T14:55:05.992+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_14-51-35-3vorpp4r.html
[2025-09-10T14:55:07.842+08:00] Using commonContextParser for action: extract
[2025-09-10T14:55:10.172+08:00] Using commonContextParser for action: extract
[2025-09-10T14:55:15.544+08:00] writeOutActionDumps D:\aigc\aigc_ui_tools\backend\testcase\midscene_run\report\android-2025-09-10_14-51-35-3vorpp4r.html
