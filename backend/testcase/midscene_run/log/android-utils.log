[2025-09-01T16:53:50.898+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-01T17:22:26.002+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-01T17:23:44.346+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-01T17:26:12.991+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-01T17:27:13.462+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-01T17:28:22.949+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-01T17:35:50.092+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-01T17:56:52.803+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-01T17:57:23.458+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-01T17:59:56.478+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-01T18:01:00.380+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-01T18:16:43.236+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-01T18:19:30.676+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-01T18:20:05.584+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-01T19:17:54.995+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-01T19:28:59.230+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-01T19:35:31.703+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-10T10:57:51.495+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-10T11:01:14.064+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-10T11:02:02.643+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-10T11:03:02.722+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-10T11:03:26.784+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-10T11:04:35.148+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-10T11:10:35.916+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-10T12:06:29.614+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-10T12:10:54.475+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-10T12:11:12.566+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-10T12:13:12.939+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-10T12:15:55.708+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-10T12:20:01.683+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-10T12:22:49.834+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
[2025-09-10T14:51:35.821+08:00] Found 1 connected devices:  [ { udid: 'ASALE3741B000022', state: 'device' } ]
