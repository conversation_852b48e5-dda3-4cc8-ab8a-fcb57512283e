# 多智能体自动化脚本生成系统完整指南

## 系统概述

本系统是基于Midscene.js和AutoGen框架的多智能体协作系统，能够自动将自然语言测试用例转换为可执行的Android自动化测试脚本。

### 核心特性

- **7个专业智能体协作**：页面提取、应用遍历、用例分析、脚本生成、脚本审查、批量处理、工具管理
- **多模态AI支持**：结合视觉和文本分析，提高UI元素识别准确性
- **完整的工作流程**：从应用分析到脚本生成的端到端自动化
- **批量处理能力**：支持Excel批量导入和处理
- **质量保证**：内置脚本审查和质量评估机制
- **灵活部署**：支持API服务、命令行工具、调试模式

## 系统架构

### 智能体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    主控制器 (MainController)                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  页面元素提取    │  │   应用遍历      │  │   用例分析      │ │
│  │     智能体      │  │     智能体      │  │     智能体      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  脚本生成       │  │   脚本审查      │  │   批量处理      │ │
│  │     智能体      │  │     智能体      │  │     智能体      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐                                          │
│  │  脚本生成工具    │                                          │
│  └─────────────────┘                                          │
└─────────────────────────────────────────────────────────────┘
```

### 数据流程

```
Excel文件 → 批量处理智能体 → 用例分析智能体 → 脚本生成智能体 → 脚本审查智能体 → 输出脚本
    ↓              ↓              ↓              ↓              ↓
应用分析 → 页面提取智能体 → 应用遍历智能体 → 元素映射 → 脚本优化 → 项目结构
```

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd new_backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置设置

```bash
# 复制配置文件
cp config.py.example config.py

# 编辑配置文件，设置：
# - 数据库连接信息
# - AI模型API密钥
# - Android设备配置
```

### 3. 启动系统

#### 命令行模式
```bash
python start_system.py --mode cli
```

#### API服务模式
```bash
python start_system.py --mode api --host 0.0.0.0 --port 8000
```

#### 调试模式
```bash
python start_system.py --mode debug --debug
```

## 使用指南

### 1. 应用分析

在生成测试脚本之前，需要先分析目标应用：

```python
# 通过API
POST /analyze-app
{
    "package_name": "com.example.app",
    "app_name": "示例应用"
}

# 通过命令行
# 选择选项1，输入应用包名
```

### 2. 单个用例处理

```python
# 通过API
POST /process-test-case
{
    "case_id": "TC001",
    "case_name": "登录功能测试",
    "package_name": "com.example.app",
    "test_steps": "1. 打开应用 2. 点击登录按钮 3. 输入用户名密码 4. 点击确认",
    "expected_results": "成功登录到主页面"
}
```

### 3. Excel批量处理

#### Excel文件格式

| case_id | case_name | package_name | test_steps | expected_results | priority |
|---------|-----------|--------------|------------|------------------|----------|
| TC001   | 登录测试   | com.app     | 测试步骤... | 预期结果...       | high     |

#### 批量处理

```python
# 通过API上传Excel
POST /upload-excel
# 上传Excel文件

# 或通过批量处理接口
POST /batch-process
{
    "test_cases": [
        {
            "case_id": "TC001",
            "case_name": "登录测试",
            "package_name": "com.example.app",
            "test_steps": "...",
            "expected_results": "..."
        }
    ]
}
```

### 4. 脚本导出和项目创建

```python
# 导出脚本文件
POST /export-scripts
{
    "script_ids": ["script1", "script2"],
    "output_dir": "/path/to/output"
}

# 创建完整项目
POST /create-project
{
    "project_name": "MyTestProject",
    "script_ids": ["script1", "script2"],
    "output_dir": "/path/to/project"
}
```

## 配置说明

### 数据库配置

```python
database:
  host: "localhost"
  port: 3306
  username: "root"
  password: "password"
  database: "automation_system"
```

### AI模型配置

```python
autogen:
  model_name: "gpt-4"
  api_key: "your-openai-api-key"
  base_url: "https://api.openai.com/v1"
  
anthropic:
  api_key: "your-anthropic-api-key"
  model_name: "claude-3-sonnet-20240229"
```

### Android设备配置

```python
uiautomator2:
  device_serial: "auto"  # 或指定设备序列号
  connect_timeout: 30
  operation_timeout: 10
```

## API接口文档

### 核心接口

#### 1. 健康检查
```
GET /health
```

#### 2. 应用分析
```
POST /analyze-app
{
    "package_name": "com.example.app",
    "app_name": "应用名称"
}
```

#### 3. 测试用例处理
```
POST /process-test-case
{
    "case_id": "TC001",
    "case_name": "测试用例名称",
    "package_name": "com.example.app",
    "test_steps": "测试步骤描述",
    "expected_results": "预期结果"
}
```

#### 4. 批量处理
```
POST /batch-process
{
    "test_cases": [...],
    "package_name": "com.example.app"
}
```

#### 5. Excel上传
```
POST /upload-excel
Content-Type: multipart/form-data
file: Excel文件
sheet_name: 工作表名称(可选)
```

#### 6. 脚本管理
```
GET /scripts                    # 获取脚本列表
GET /scripts/{script_id}        # 获取脚本详情
GET /download-script/{script_id} # 下载脚本文件
```

#### 7. 项目管理
```
POST /export-scripts            # 导出脚本
POST /create-project           # 创建项目
```

#### 8. 系统统计
```
GET /statistics                # 获取系统统计信息
```

## 生成的脚本结构

### 基础脚本模板

```typescript
import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';
import "dotenv/config";

async function main() {
  let device: AndroidDevice | null = null;
  let agent: AndroidAgent | null = null;

  try {
    // 设备连接
    const devices = await getConnectedDevices();
    device = new AndroidDevice(devices[0].udid);
    await device.connect();

    // 创建AI智能体
    agent = new AndroidAgent(device, {
      aiActionContext: 'If any popup appears, handle appropriately.',
    });

    // 启动应用
    await device.launch('com.example.app');

    // 测试步骤
    await agent.aiAction('点击登录按钮');
    await agent.aiAction('输入用户名', 'testuser');
    await agent.aiAction('输入密码', 'password123');
    await agent.aiAction('点击确认登录');

    // 断言验证
    await agent.aiAssert('页面显示用户主界面');

  } catch (error) {
    console.error('测试执行失败:', error);
  } finally {
    if (device) {
      await device.disconnect();
    }
  }
}

main().catch(console.error);
```

## 故障排除

### 常见问题

#### 1. 数据库连接失败
- 检查数据库服务是否启动
- 验证连接配置信息
- 确认网络连接

#### 2. Android设备连接失败
- 确认设备已连接并启用USB调试
- 检查ADB驱动是否正确安装
- 验证设备授权状态

#### 3. AI模型调用失败
- 检查API密钥是否正确
- 验证网络连接和代理设置
- 确认模型服务可用性

#### 4. 脚本生成质量问题
- 检查测试用例描述的清晰度
- 验证应用页面分析结果
- 调整AI模型参数

### 日志分析

系统日志位于 `logs/` 目录：
- `system.log`: 系统运行日志
- `agents.log`: 智能体执行日志
- `database.log`: 数据库操作日志

### 性能优化

1. **数据库优化**
   - 创建适当的索引
   - 定期清理历史数据
   - 使用连接池

2. **AI调用优化**
   - 实现请求缓存
   - 使用批量处理
   - 设置合理的超时时间

3. **设备操作优化**
   - 减少不必要的截图
   - 优化元素定位策略
   - 使用设备连接池

## 扩展开发

### 添加新智能体

1. 继承基础智能体类
2. 实现核心处理方法
3. 注册到主控制器
4. 添加配置选项

### 自定义脚本模板

1. 在 `utils/script_generation_tool.py` 中添加新模板
2. 实现模板变量替换逻辑
3. 更新脚本生成配置

### 集成新的AI模型

1. 在 `utils/autogen_client.py` 中添加新模型支持
2. 更新配置文件
3. 实现模型特定的处理逻辑

## 部署指南

### 开发环境部署

```bash
# 安装依赖
pip install -r requirements.txt

# 配置环境变量
export DATABASE_URL="mysql://user:pass@localhost/db"
export OPENAI_API_KEY="your-key"

# 启动服务
python start_system.py --mode api
```

### 生产环境部署

```bash
# 使用Docker部署
docker build -t automation-system .
docker run -d -p 8000:8000 automation-system

# 或使用docker-compose
docker-compose up -d
```

### 监控和维护

1. **健康检查**: 定期调用 `/health` 接口
2. **日志监控**: 监控错误日志和性能指标
3. **数据备份**: 定期备份数据库和生成的脚本
4. **版本更新**: 保持依赖包和AI模型的更新

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 支持和贡献

- 问题报告: 请在GitHub Issues中提交
- 功能请求: 欢迎提交Pull Request
- 文档改进: 帮助完善文档和示例

---

更多详细信息请参考各模块的具体文档和代码注释。
