"""
UI元素数据模型
基于Page Object模式设计的元素模型
"""
from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
import hashlib
import json


@dataclass
class ElementPosition:
    """元素位置信息"""
    left: int
    top: int
    right: int
    bottom: int
    
    @property
    def center_x(self) -> int:
        """中心点X坐标"""
        return (self.left + self.right) // 2
    
    @property
    def center_y(self) -> int:
        """中心点Y坐标"""
        return (self.top + self.bottom) // 2
    
    @property
    def width(self) -> int:
        """宽度"""
        return self.right - self.left
    
    @property
    def height(self) -> int:
        """高度"""
        return self.bottom - self.top
    
    @property
    def area(self) -> int:
        """面积"""
        return self.width * self.height
    
    def to_bounds_string(self) -> str:
        """转换为bounds字符串格式"""
        return f"[{self.left},{self.top}][{self.right},{self.bottom}]"
    
    @classmethod
    def from_bounds_string(cls, bounds_str: str) -> 'ElementPosition':
        """从bounds字符串创建位置对象"""
        try:
            # 解析 "[0,0][100,50]" 格式
            bounds_str = bounds_str.replace('[', '').replace(']', ',')
            coords = [int(x) for x in bounds_str.split(',') if x]
            if len(coords) >= 4:
                return cls(coords[0], coords[1], coords[2], coords[3])
        except Exception:
            pass
        return cls(0, 0, 0, 0)


@dataclass
class ElementAttributes:
    """元素属性信息"""
    clickable: bool = False
    long_clickable: bool = False
    checkable: bool = False
    checked: bool = False
    editable: bool = False
    enabled: bool = True
    focusable: bool = False
    focused: bool = False
    scrollable: bool = False
    selected: bool = False
    visible: bool = True
    
    def to_dict(self) -> Dict[str, bool]:
        """转换为字典"""
        return {
            'clickable': self.clickable,
            'long_clickable': self.long_clickable,
            'checkable': self.checkable,
            'checked': self.checked,
            'editable': self.editable,
            'enabled': self.enabled,
            'focusable': self.focusable,
            'focused': self.focused,
            'scrollable': self.scrollable,
            'selected': self.selected,
            'visible': self.visible
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ElementAttributes':
        """从字典创建属性对象"""
        def str_to_bool(value: Any) -> bool:
            if isinstance(value, bool):
                return value
            if isinstance(value, str):
                return value.lower() == 'true'
            return bool(value)
        
        return cls(
            clickable=str_to_bool(data.get('clickable', False)),
            long_clickable=str_to_bool(data.get('long_clickable', False)),
            checkable=str_to_bool(data.get('checkable', False)),
            checked=str_to_bool(data.get('checked', False)),
            editable=str_to_bool(data.get('editable', False)),
            enabled=str_to_bool(data.get('enabled', True)),
            focusable=str_to_bool(data.get('focusable', False)),
            focused=str_to_bool(data.get('focused', False)),
            scrollable=str_to_bool(data.get('scrollable', False)),
            selected=str_to_bool(data.get('selected', False)),
            visible=str_to_bool(data.get('visible', True))
        )


@dataclass
class UIElement:
    """UI元素模型"""
    element_id: str
    element_type: str  # 元素类型，如TextView、Button等
    text: str = ""
    resource_id: str = ""
    content_desc: str = ""
    position: Optional[ElementPosition] = None
    attributes: Optional[ElementAttributes] = None
    hierarchy_path: str = ""  # 层级路径
    xpath: str = ""  # XPath定位
    package_name: str = ""
    index: int = 0
    
    # AI增强字段
    semantic_description: str = ""  # 语义描述
    expected_action: str = ""  # 预期操作
    element_category: str = ""  # 元素类别
    confidence_score: float = 0.0  # 置信度分数
    
    # 元数据
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.element_id:
            self.element_id = self.generate_element_id()
        if self.attributes is None:
            self.attributes = ElementAttributes()
        if self.position is None:
            self.position = ElementPosition(0, 0, 0, 0)
    
    def generate_element_id(self) -> str:
        """生成元素唯一ID"""
        # 基于关键属性生成哈希ID
        key_data = f"{self.element_type}_{self.resource_id}_{self.text}_{self.content_desc}"
        if self.position:
            key_data += f"_{self.position.left}_{self.position.top}"
        return hashlib.md5(key_data.encode()).hexdigest()[:16]
    
    def is_actionable(self) -> bool:
        """判断元素是否可操作"""
        if not self.attributes:
            return False
        return (self.attributes.clickable or 
                self.attributes.long_clickable or 
                self.attributes.checkable or 
                self.attributes.editable or 
                self.attributes.scrollable)
    
    def is_visible_and_valid(self) -> bool:
        """判断元素是否可见且有效"""
        if not self.attributes or not self.attributes.visible:
            return False
        if not self.position or self.position.area <= 0:
            return False
        return True
    
    def get_locator_strategies(self) -> Dict[str, str]:
        """获取定位策略"""
        strategies = {}
        
        if self.resource_id:
            strategies['resource_id'] = self.resource_id
        if self.text:
            strategies['text'] = self.text
        if self.content_desc:
            strategies['content_desc'] = self.content_desc
        if self.xpath:
            strategies['xpath'] = self.xpath
        if self.position:
            strategies['coordinates'] = f"({self.position.center_x}, {self.position.center_y})"
        
        return strategies
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'element_id': self.element_id,
            'element_type': self.element_type,
            'text': self.text,
            'resource_id': self.resource_id,
            'content_desc': self.content_desc,
            'position': {
                'left': self.position.left,
                'top': self.position.top,
                'right': self.position.right,
                'bottom': self.position.bottom,
                'center_x': self.position.center_x,
                'center_y': self.position.center_y,
                'width': self.position.width,
                'height': self.position.height
            } if self.position else None,
            'attributes': self.attributes.to_dict() if self.attributes else None,
            'hierarchy_path': self.hierarchy_path,
            'xpath': self.xpath,
            'package_name': self.package_name,
            'index': self.index,
            'semantic_description': self.semantic_description,
            'expected_action': self.expected_action,
            'element_category': self.element_category,
            'confidence_score': self.confidence_score,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    @classmethod
    def from_xml_element(cls, xml_element, parent_path: str = "") -> 'UIElement':
        """从XML元素创建UIElement对象"""
        attrs = xml_element.attrib
        
        # 构建层级路径
        element_path = f"{parent_path}/{xml_element.tag}" if parent_path else xml_element.tag
        
        # 解析位置信息
        position = None
        if attrs.get('bounds'):
            position = ElementPosition.from_bounds_string(attrs.get('bounds'))
        
        # 解析属性信息
        attributes = ElementAttributes.from_dict(attrs)
        
        # 生成XPath
        xpath = cls._generate_xpath_from_attrs(attrs)
        
        return cls(
            element_id="",  # 将在__post_init__中生成
            element_type=attrs.get('class', ''),
            text=attrs.get('text', ''),
            resource_id=attrs.get('resource-id', ''),
            content_desc=attrs.get('content-desc', ''),
            position=position,
            attributes=attributes,
            hierarchy_path=element_path,
            xpath=xpath,
            package_name=attrs.get('package', ''),
            index=int(attrs.get('index', 0))
        )
    
    @staticmethod
    def _generate_xpath_from_attrs(attrs: Dict[str, str]) -> str:
        """从属性生成XPath"""
        xpath_parts = []
        
        if attrs.get('resource-id'):
            xpath_parts.append(f"@resource-id='{attrs['resource-id']}'")
        if attrs.get('text'):
            xpath_parts.append(f"@text='{attrs['text']}'")
        if attrs.get('content-desc'):
            xpath_parts.append(f"@content-desc='{attrs['content-desc']}'")
        
        if xpath_parts:
            return f"//*[{' and '.join(xpath_parts)}]"
        else:
            return f"//*[@class='{attrs.get('class', '')}']"
