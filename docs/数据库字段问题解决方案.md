# 数据库字段问题解决方案

## 问题描述

在运行 `case_info_agent` 时遇到以下错误：

```
(pymysql.err.OperationalError) (1054, "Unknown column 'test_cases.tcid' in 'field list'")
```

## 问题原因

数据库中的 `test_cases` 表结构与 SQLAlchemy 模型定义不匹配：

### 原始数据库表结构
- 字段名：`case_id`（varchar(50)）
- 缺少 `tcid` 字段
- 表结构与当前代码模型不一致

### SQLAlchemy 模型定义
- 字段名：`tcid`（VARCHAR(100)）
- 包含完整的测试用例字段定义

## 解决方案

### 1. 问题诊断

使用诊断脚本 `temp/diagnose_database_issue.py` 检查：
- 数据库连接状态
- 表结构对比
- SQLAlchemy 模型验证
- 数据库操作测试

### 2. 数据库修复

使用修复脚本 `temp/fix_database_schema.py` 执行：

1. **删除现有表**（按依赖关系顺序）：
   - script_versions
   - generated_scripts
   - agent_sessions
   - test_cases
   - package_components
   - business_modules

2. **重新创建表**：
   - 使用 SQLAlchemy 模型定义
   - 确保字段名称和类型正确
   - 建立正确的外键关系

3. **验证修复结果**：
   - 检查 `tcid` 字段是否存在
   - 测试基本数据库操作
   - 创建测试数据验证功能

## 修复结果

✅ **成功修复的内容**：
- `test_cases` 表现在包含正确的 `tcid` 字段
- 所有表结构与 SQLAlchemy 模型匹配
- 数据库操作正常工作
- 测试用例创建和查询功能正常

### 新的表结构

```sql
CREATE TABLE test_cases (
    id INTEGER NOT NULL AUTO_INCREMENT,
    tcid VARCHAR(100) NOT NULL COMMENT '测试用例ID (*TCID)',
    case_name VARCHAR(500) NOT NULL COMMENT '用例名称 (*CaseName)',
    group_name VARCHAR(100) COMMENT '分组 (*Group)',
    sub_group VARCHAR(100) COMMENT '子分组 (*SubGroup)',
    component VARCHAR(100) COMMENT '组件 (*Component)',
    case_type VARCHAR(50) COMMENT '用例类型 (*Type)',
    level VARCHAR(20) COMMENT '用例级别 (*Level)',
    steps TEXT NOT NULL COMMENT '测试步骤 (*Steps)',
    expect_result TEXT COMMENT '预期结果 (*ExpectResult)',
    automated VARCHAR(20) COMMENT '自动化标识 (*Automated)',
    owner VARCHAR(100) COMMENT '负责人 (*Owner)',
    execute_owner VARCHAR(100) COMMENT '执行人 (*ExecuteOwner)',
    -- ... 其他字段
    business_module_id INTEGER COMMENT '业务模块ID',
    status VARCHAR(20) COMMENT '处理状态',
    created_at DATETIME COMMENT '创建时间',
    updated_at DATETIME COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE (tcid),
    FOREIGN KEY(business_module_id) REFERENCES business_modules (id)
);
```

## 验证测试

修复后成功执行的测试：
1. 创建测试业务模块
2. 创建测试用例（tcid: TC_test001）
3. 通过 tcid 查询测试用例
4. 所有数据库操作正常

## 预防措施

为避免类似问题再次发生：

1. **数据库迁移管理**：
   - 使用 Alembic 进行数据库版本管理
   - 在模型变更时创建迁移脚本

2. **开发流程**：
   - 模型变更后及时更新数据库
   - 在测试环境验证表结构一致性

3. **监控检查**：
   - 定期运行表结构验证脚本
   - 在部署前检查数据库兼容性

## 相关文件

- 诊断脚本：`temp/diagnose_database_issue.py`
- 修复脚本：`temp/fix_database_schema.py`
- 模型定义：`backend/models/script_models.py`
- 数据库管理：`backend/script_database_manager.py`

## 总结

问题已完全解决，`case_info_agent` 现在可以正常工作。数据库表结构与代码模型完全匹配，所有相关功能都已验证正常。
