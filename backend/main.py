"""
移动端应用UI自动化测试分析系统主程序
"""
import sys
import asyncio
import argparse
import json
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime
from loguru import logger

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from .config import config
from .database import db_manager
from .app_controller import AndroidAppController
from .page_crawler import PageCrawler
from .models.app_model import AnalysisSession


class UIAnalysisSystem:
    """UI分析系统主类"""
    
    def __init__(self, device_id: Optional[str] = None):
        """
        初始化UI分析系统
        
        Args:
            device_id: 设备ID，为None时使用默认设备
        """
        self.device_id = device_id
        self.app_controller = AndroidAppController(device_id)
        self.page_crawler = PageCrawler(device_id)
        
        # 初始化日志
        self._setup_logging()
        
        # 初始化数据库
        self._initialize_database()
    
    def _setup_logging(self):
        """设置日志系统"""
        try:
            # 移除默认处理器
            logger.remove()
            
            # 控制台输出
            logger.add(
                sys.stdout,
                format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
                level="INFO"
            )
            
            # 文件输出
            log_file = config.paths.logs_dir / f"ui_analysis_{datetime.now().strftime('%Y%m%d')}.log"
            logger.add(
                str(log_file),
                format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
                level="DEBUG",
                rotation="1 day",
                retention="30 days",
                compression="zip"
            )
            
            logger.info("日志系统初始化完成")
            
        except Exception as e:
            print(f"日志系统初始化失败: {e}")
    
    def _initialize_database(self):
        """初始化数据库"""
        try:
            if not db_manager.initialize_database():
                logger.error("数据库初始化失败")
                sys.exit(1)
            logger.info("数据库初始化成功")
        except Exception as e:
            logger.error(f"数据库初始化异常: {e}")
            sys.exit(1)
    
    def check_system_requirements(self) -> bool:
        """
        检查系统要求
        
        Returns:
            bool: 系统要求是否满足
        """
        try:
            logger.info("检查系统要求...")
            
            # 检查设备连接
            if not self.app_controller.check_device_connection():
                logger.error("设备连接检查失败")
                return False
            
            # 获取设备信息
            device_info = self.app_controller.get_device_info()
            if not device_info.get('connected'):
                logger.error("设备未连接")
                return False
            
            logger.info(f"设备信息: {device_info['brand']} {device_info['model']} (Android {device_info['android_version']})")
            
            # 检查页面爬虫连接
            if not self.page_crawler.connect_device():
                logger.error("页面爬虫设备连接失败")
                return False
            
            logger.info("系统要求检查通过")
            return True
            
        except Exception as e:
            logger.error(f"系统要求检查失败: {e}")
            return False
    
    async def analyze_app(self, 
                         package_name: str,
                         activity_name: Optional[str] = None,
                         max_depth: Optional[int] = None,
                         max_pages: Optional[int] = None,
                         enable_ai: bool = True,
                         save_to_db: bool = True) -> AnalysisSession:
        """
        分析应用
        
        Args:
            package_name: 应用包名
            activity_name: 启动Activity名称
            max_depth: 最大遍历深度
            max_pages: 最大页面数量
            enable_ai: 是否启用AI增强
            save_to_db: 是否保存到数据库
            
        Returns:
            AnalysisSession: 分析会话结果
        """
        try:
            logger.info(f"开始分析应用: {package_name}")
            
            # 检查应用是否安装
            if not self.app_controller.is_app_installed(package_name):
                raise Exception(f"应用未安装: {package_name}")
            
            # 执行页面遍历分析
            session = await self.page_crawler.crawl_app_pages(
                package_name=package_name,
                activity_name=activity_name,
                max_depth=max_depth,
                max_pages=max_pages,
                enable_ai=enable_ai
            )
            
            # 保存到数据库
            if save_to_db and session.status.value == "completed":
                logger.info("保存分析结果到数据库...")
                if db_manager.save_complete_analysis_session(session):
                    logger.info("分析结果保存成功")
                else:
                    logger.warning("分析结果保存失败")
            
            # 生成分析报告
            self._generate_analysis_report(session)
            
            return session
            
        except Exception as e:
            logger.error(f"应用分析失败: {e}")
            raise
    
    def _generate_analysis_report(self, session: AnalysisSession):
        """
        生成分析报告
        
        Args:
            session: 分析会话
        """
        try:
            logger.info("生成分析报告...")
            
            # 生成摘要报告
            summary_report = session.to_summary_report()
            
            # 保存摘要报告
            report_file = config.paths.temp_dir / f"analysis_report_{session.session_id}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(summary_report, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"分析报告已保存: {report_file}")
            
            # 生成页面关系图谱
            page_graph = session.generate_page_graph()
            graph_file = config.paths.temp_dir / f"page_graph_{session.session_id}.json"
            with open(graph_file, 'w', encoding='utf-8') as f:
                json.dump(page_graph, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"页面关系图谱已保存: {graph_file}")
            
            # 打印摘要信息
            self._print_analysis_summary(summary_report)
            
        except Exception as e:
            logger.error(f"生成分析报告失败: {e}")
    
    def _print_analysis_summary(self, summary_report: Dict[str, Any]):
        """
        打印分析摘要
        
        Args:
            summary_report: 摘要报告
        """
        try:
            print("\n" + "="*60)
            print("📱 应用UI分析报告摘要")
            print("="*60)
            
            session_info = summary_report.get('session_info', {})
            coverage_stats = summary_report.get('coverage_statistics', {})
            element_stats = summary_report.get('element_statistics', {})
            nav_stats = summary_report.get('navigation_statistics', {})
            
            print(f"应用名称: {session_info.get('app_name', 'Unknown')}")
            print(f"包名: {session_info.get('package_name', 'Unknown')}")
            print(f"分析耗时: {session_info.get('analysis_duration', 0):.2f} 秒")
            print(f"分析状态: {session_info.get('status', 'Unknown')}")
            
            print(f"\n📊 覆盖率统计:")
            print(f"  发现页面: {coverage_stats.get('total_pages_discovered', 0)} 个")
            print(f"  成功分析: {coverage_stats.get('pages_successfully_analyzed', 0)} 个")
            print(f"  分析失败: {coverage_stats.get('pages_failed', 0)} 个")
            print(f"  成功率: {coverage_stats.get('success_rate', 0):.1f}%")
            
            print(f"\n🎯 元素统计:")
            print(f"  总元素数: {element_stats.get('total_elements', 0)} 个")
            print(f"  可操作元素: {element_stats.get('actionable_elements', 0)} 个")
            print(f"  AI增强元素: {element_stats.get('ai_enhanced_elements', 0)} 个")
            print(f"  AI增强率: {element_stats.get('ai_enhancement_rate', 0):.1f}%")
            
            print(f"\n🔗 导航统计:")
            print(f"  导航关系: {nav_stats.get('total_navigations', 0)} 个")
            print(f"  平均每页导航: {nav_stats.get('avg_navigations_per_page', 0):.1f} 个")
            
            error_summary = summary_report.get('error_summary', {})
            if error_summary.get('has_errors'):
                print(f"\n⚠️  错误信息:")
                print(f"  失败页面数: {error_summary.get('failed_pages_count', 0)}")
                if error_summary.get('error_message'):
                    print(f"  错误消息: {error_summary.get('error_message')}")
            
            print("="*60)
            
        except Exception as e:
            logger.error(f"打印分析摘要失败: {e}")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="移动端应用UI自动化测试分析系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 分析指定应用
  python -m backend.main --package com.example.app

  # 指定启动Activity和设备
  python -m backend.main --package com.example.app --activity MainActivity --device emulator-5554

  # 限制遍历深度和页面数量
  python -m backend.main --package com.example.app --max-depth 10 --max-pages 50

  # 禁用AI增强
  python -m backend.main --package com.example.app --no-ai

  # 不保存到数据库
  python -m backend.main --package com.example.app --no-save-db
        """
    )
    
    parser.add_argument(
        '--package', '-p',
        required=True,
        help='应用包名 (例如: com.example.app)'
    )
    
    parser.add_argument(
        '--activity', '-a',
        default=None,
        help='启动Activity名称 (默认: 使用主Activity)'
    )
    
    parser.add_argument(
        '--device', '-d',
        default=None,
        help='设备ID (默认: 使用第一个连接的设备)'
    )
    
    parser.add_argument(
        '--max-depth',
        type=int,
        default=None,
        help='最大遍历深度 (默认: 20)'
    )
    
    parser.add_argument(
        '--max-pages',
        type=int,
        default=None,
        help='最大页面数量 (默认: 100)'
    )
    
    parser.add_argument(
        '--no-ai',
        action='store_true',
        help='禁用AI语义增强'
    )
    
    parser.add_argument(
        '--no-save-db',
        action='store_true',
        help='不保存到数据库'
    )
    
    args = parser.parse_args()
    
    try:
        # 创建分析系统
        system = UIAnalysisSystem(device_id=args.device)
        
        # 检查系统要求
        if not system.check_system_requirements():
            logger.error("系统要求检查失败，退出程序")
            sys.exit(1)
        
        # 执行分析
        session = await system.analyze_app(
            package_name=args.package,
            activity_name=args.activity,
            max_depth=args.max_depth,
            max_pages=args.max_pages,
            enable_ai=not args.no_ai,
            save_to_db=not args.no_save_db
        )
        
        if session.status.value == "completed":
            logger.info("✅ 应用分析完成！")
            sys.exit(0)
        else:
            logger.error("❌ 应用分析失败！")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.warning("⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
