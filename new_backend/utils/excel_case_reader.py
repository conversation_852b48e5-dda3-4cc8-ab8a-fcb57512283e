"""
真实Excel用例文件读取器
基于实际Excel文件结构，读取和处理测试用例数据
"""
import os
import sys
import pandas as pd
from typing import Dict, Any, Optional, List
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加相对路径导入
sys.path.append(str(Path(__file__).parent.parent))

from config import config
from models.case_models import RealTestCase, EXCEL_FIELD_MAPPING, TestCase
from utils.database_manager import DatabaseManager
from utils.module_package_mapper import ModulePackageMapper


class ExcelCaseReader:
    """Excel用例文件读取器"""
    
    def __init__(self):
        """初始化Excel读取器"""
        self.config = config
        self.db_manager = DatabaseManager()
        self.package_mapper = ModulePackageMapper()
        
        # 数据文件路径
        self.data_dir = Path(__file__).parent.parent / "data"
        self.case_file = self.data_dir / "case" / "case.xlsx"
        
        logger.info("Excel用例读取器初始化完成")
    
    def _safe_str(self, value, default: str = "") -> str:
        """安全转换为字符串，处理NaN和None值"""
        if pd.isna(value) or value is None:
            return default
        str_value = str(value).strip()
        return str_value if str_value else default
    
    def _safe_float(self, value, default: Optional[float] = None) -> Optional[float]:
        """安全转换为浮点数，处理NaN和None值"""
        if pd.isna(value) or value is None:
            return default
        try:
            return float(value)
        except (ValueError, TypeError):
            return default
    
    def _safe_int(self, value, default: Optional[int] = None) -> Optional[int]:
        """安全转换为整数，处理NaN和None值"""
        if pd.isna(value) or value is None:
            return default
        try:
            return int(float(value))  # 先转float再转int，处理Excel中的数字格式
        except (ValueError, TypeError):
            return default
    
    def read_test_cases_from_excel(self, excel_file_path: str = None) -> List[RealTestCase]:
        """
        从Excel文件读取测试用例
        
        Args:
            excel_file_path: Excel文件路径，如果为None则使用默认路径
            
        Returns:
            List[RealTestCase]: 测试用例列表
        """
        try:
            if excel_file_path is None:
                excel_file_path = self.case_file
            else:
                excel_file_path = Path(excel_file_path)
            
            if not excel_file_path.exists():
                logger.error(f"测试用例文件不存在: {excel_file_path}")
                return []
            
            logger.info(f"读取测试用例文件: {excel_file_path}")
            
            # 读取Excel文件
            df = pd.read_excel(excel_file_path, engine='openpyxl')
            
            # 打印列名以便调试
            logger.info(f"Excel列名: {df.columns.tolist()}")
            logger.info(f"总行数: {len(df)}")
            
            test_cases = []
            for index, row in df.iterrows():
                try:
                    # 跳过空行
                    if pd.isna(row.get('*TCID')) and pd.isna(row.get('*CaseName')):
                        continue
                    
                    # 创建RealTestCase对象
                    test_case = RealTestCase(
                        # 核心必填字段
                        group=self._safe_str(row.get('*Group')),
                        tcid=self._safe_str(row.get('*TCID')),
                        casename=self._safe_str(row.get('*CaseName')),
                        subgroup=self._safe_str(row.get('*SubGroup')),
                        component=self._safe_str(row.get('*Component')),
                        type=self._safe_str(row.get('*Type')),
                        level=self._safe_str(row.get('*Level')),
                        steps=self._safe_str(row.get('*Steps')),
                        expectresult=self._safe_str(row.get('*ExpectResult')),
                        
                        # 可选字段
                        sort=self._safe_float(row.get('Sort')),
                        subcomponent=self._safe_str(row.get('SubComponent')),
                        subfunction=self._safe_str(row.get('SubFunction')),
                        secondfunction=self._safe_str(row.get('SecondFunction')),
                        casesettype=self._safe_float(row.get('CaseSetType')),
                        purpose=self._safe_str(row.get('Purpose')),
                        precondition=self._safe_str(row.get('PreCondition')),
                        automated=self._safe_float(row.get('*Automated')),
                        owner=self._safe_float(row.get('*Owner')),
                        executeowner=self._safe_float(row.get('*ExecuteOwner')),
                        keywords=self._safe_str(row.get('Keywords')),
                        phase=self._safe_int(row.get('Phase')),
                        testarea=self._safe_str(row.get('TestArea')),
                        excutetime=self._safe_float(row.get('ExcuteTime')),
                        standard=self._safe_float(row.get('Standard')),
                        simples=self._safe_float(row.get('Simples')),
                        environment=self._safe_float(row.get('Environment')),
                        os=self._safe_float(row.get('OS')),
                        android=self._safe_float(row.get('Android')),
                        cpu=self._safe_float(row.get('CPU')),
                        interactioncomponent=self._safe_str(row.get('InteractionComponent')),
                        brand=self._safe_str(row.get('Brand')),
                        sim=self._safe_str(row.get('SIM')),
                        source=self._safe_str(row.get('Source')),
                        matchproject=self._safe_float(row.get('MatchProject')),
                        testcodepath=self._safe_float(row.get('TestCodePath')),
                        appversion=self._safe_str(row.get('Appversion')),
                        specialability=self._safe_float(row.get('SpecialAbility')),
                        specialtype=self._safe_float(row.get('SpecialType')),
                        update_time=self._safe_str(row.get('更新时间')),
                        auditresult=self._safe_float(row.get('AuditResult')),
                        auditremark=self._safe_float(row.get('AuditRemark'))
                    )
                    
                    # 验证必要字段
                    if test_case.tcid and test_case.casename and test_case.steps:
                        test_cases.append(test_case)
                    else:
                        logger.warning(f"第{index+1}行数据不完整，跳过: TCID={test_case.tcid}, CaseName={test_case.casename}")
                    
                except Exception as e:
                    logger.warning(f"解析第{index+1}行用例数据失败: {e}")
                    continue
            
            logger.info(f"成功读取 {len(test_cases)} 个测试用例")
            return test_cases
            
        except Exception as e:
            logger.error(f"读取测试用例文件失败: {e}")
            return []
    
    def convert_to_standard_format(self, real_cases: List[RealTestCase]) -> List[TestCase]:
        """
        将真实Excel用例转换为标准格式
        
        Args:
            real_cases: 真实Excel用例列表
            
        Returns:
            List[TestCase]: 标准格式用例列表
        """
        try:
            standard_cases = []
            
            for real_case in real_cases:
                try:
                    # 从组件映射中获取包名
                    package_name = self._get_package_name_by_component(real_case.component, real_case.group)
                    
                    # 转换为标准格式
                    standard_case = TestCase(
                        case_id=real_case.tcid,
                        case_name=real_case.casename,
                        test_purpose=real_case.purpose,
                        package_name=package_name,
                        module_name=real_case.group,
                        preconditions=real_case.precondition,
                        test_steps=real_case.steps,
                        expected_results=real_case.expectresult,
                        priority=real_case.level,
                        test_type=real_case.type
                    )
                    
                    standard_cases.append(standard_case)
                    
                except Exception as e:
                    logger.warning(f"转换用例失败: {real_case.tcid}: {e}")
                    continue
            
            logger.info(f"成功转换 {len(standard_cases)} 个标准格式用例")
            return standard_cases
            
        except Exception as e:
            logger.error(f"转换标准格式失败: {e}")
            return []
    
    def _get_package_name_by_component(self, component: str, group: str) -> str:
        """
        根据组件和业务模块获取包名
        
        Args:
            component: 组件名称
            group: 业务模块
            
        Returns:
            str: 包名
        """
        try:
            # 首先尝试根据业务模块查找
            mappings = self.package_mapper.get_package_by_module(group)
            
            if mappings:
                # 如果有多个包，尝试根据组件名匹配
                for mapping in mappings:
                    if component.lower() in mapping['component_name'].lower():
                        return mapping['package_name']
                
                # 如果没有精确匹配，返回第一个
                return mappings[0]['package_name']
            
            # 如果没有找到映射，尝试根据组件名查找
            all_mappings = self.package_mapper.get_all_mappings()
            for mapping in all_mappings:
                if component.lower() in mapping['component_name'].lower():
                    return mapping['package_name']
            
            # 如果都没找到，返回默认值
            logger.warning(f"未找到组件 {component} 的包名映射")
            return "com.unknown.app"
            
        except Exception as e:
            logger.warning(f"获取包名失败: {e}")
            return "com.unknown.app"
    
    def save_cases_to_database(self, test_cases: List[TestCase]) -> int:
        """
        保存测试用例到数据库
        
        Args:
            test_cases: 测试用例列表
            
        Returns:
            int: 成功保存的用例数
        """
        try:
            saved_count = 0
            
            for test_case in test_cases:
                try:
                    saved_case = self.db_manager.save_test_case(test_case)
                    if saved_case:
                        saved_count += 1
                except Exception as e:
                    logger.warning(f"保存用例失败: {test_case.case_id}: {e}")
                    continue
            
            logger.info(f"成功保存 {saved_count} 个测试用例到数据库")
            return saved_count
            
        except Exception as e:
            logger.error(f"保存测试用例到数据库失败: {e}")
            return 0
    
    def process_excel_file(self, excel_file_path: str = None) -> Dict[str, Any]:
        """
        处理Excel文件的完整流程
        
        Args:
            excel_file_path: Excel文件路径
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            logger.info("开始处理Excel用例文件...")
            
            # 1. 读取Excel文件
            real_cases = self.read_test_cases_from_excel(excel_file_path)
            
            if not real_cases:
                return {
                    'success': False,
                    'error': '没有读取到有效的测试用例数据'
                }
            
            # 2. 转换为标准格式
            standard_cases = self.convert_to_standard_format(real_cases)
            
            # 3. 保存到数据库
            saved_count = self.save_cases_to_database(standard_cases)
            
            return {
                'success': True,
                'total_read': len(real_cases),
                'converted_count': len(standard_cases),
                'saved_count': saved_count,
                'message': f'成功处理 {saved_count} 个测试用例'
            }
            
        except Exception as e:
            logger.error(f"处理Excel文件失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_case_statistics(self) -> Dict[str, Any]:
        """
        获取用例统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 从数据库获取统计信息
            stats = self.db_manager.get_test_case_statistics()
            return stats
            
        except Exception as e:
            logger.error(f"获取用例统计信息失败: {e}")
            return {}
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.db_manager:
                self.db_manager.cleanup()
            
            if self.package_mapper:
                self.package_mapper.cleanup()
            
            logger.info("Excel用例读取器资源清理完成")
            
        except Exception as e:
            logger.warning(f"清理Excel用例读取器资源失败: {e}")


def main():
    """测试函数"""
    try:
        # 创建读取器
        reader = ExcelCaseReader()
        
        # 测试读取Excel文件
        print("=== 测试读取Excel用例文件 ===")
        real_cases = reader.read_test_cases_from_excel()
        print(f"读取到 {len(real_cases)} 个真实用例")
        
        # 显示第一个用例
        if real_cases:
            case = real_cases[0]
            print(f"\n第一个用例:")
            print(f"ID: {case.tcid}")
            print(f"名称: {case.casename}")
            print(f"模块: {case.group}")
            print(f"组件: {case.component}")
            print(f"步骤: {case.steps[:100]}...")
            print(f"预期: {case.expectresult[:100]}...")
        
        # 测试转换为标准格式
        print("\n=== 测试转换为标准格式 ===")
        standard_cases = reader.convert_to_standard_format(real_cases)
        print(f"转换为 {len(standard_cases)} 个标准格式用例")
        
        # 测试完整处理流程
        print("\n=== 测试完整处理流程 ===")
        result = reader.process_excel_file()
        print(f"处理结果: {result}")
        
        # 清理资源
        reader.cleanup()
        
    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == "__main__":
    main()
