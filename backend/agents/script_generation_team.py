"""
脚本生成智能体团队
基于AutoGen Teams实现多智能体协作生成midsence.js Android自动化脚本
"""
import logging
from typing import Dict, Any, List, Optional
import json
import asyncio

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination
from autogen_agentchat.messages import TextMessage
from autogen_core import CancellationToken

from backend.agents.case_info_agent import CaseElementInfo
from example.llms import deepseek_model_client

logger = logging.getLogger(__name__)


class ScriptGenerationTeam:
    """脚本生成智能体团队"""
    
    def __init__(self):
        """初始化智能体团队"""
        self.script_generator = self._create_script_generator()
        self.code_reviewer = self._create_code_reviewer()
        self.team = self._create_team()
        
    def _create_script_generator(self) -> AssistantAgent:
        """创建脚本生成智能体"""
        system_message = """
你是一个专业的Android自动化测试脚本生成专家，专门使用@midscene/android框架生成TypeScript自动化脚本。

你的任务：
1. 根据测试用例和UI元素信息生成完整的自动化脚本
2. 使用@midscene/android框架的Android自动化API
3. 确保脚本覆盖所有测试步骤和验证点
4. 生成可直接运行的TypeScript代码

@midscene/android框架API参考（基于实际示例）：
```typescript
import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';
import "dotenv/config";

const sleep = (ms: number) => new Promise((r) => setTimeout(r, ms));

// 设备连接和初始化
const devices = await getConnectedDevices();
const device = new AndroidDevice(devices[0].udid);

// 创建AI智能体（核心功能）
const agent = new AndroidAgent(device, {
  aiActionContext: 'If any location, permission, user agreement, etc. popup, click agree. If login page pops up, close it.',
});

await device.connect();
await device.launch('应用包名或URL');

// AI驱动的操作（推荐使用）
await agent.aiAction('在搜索框输入 "关键词" 并点击搜索');
await agent.aiAction('点击登录按钮');
await agent.aiAction('输入用户名和密码');
await agent.aiAction('滑动到页面底部');

// AI驱动的等待
await agent.aiWaitFor("页面加载完成，显示搜索结果");
await agent.aiWaitFor("登录成功，显示用户主页");

// AI驱动的查询（获取页面信息）
const items = await agent.aiQuery("{title: string, price: number}[], 获取商品列表信息");
const userInfo = await agent.aiQuery("string, 获取当前用户名");

// AI驱动的断言（验证结果）
await agent.aiAssert("页面显示搜索结果");
await agent.aiAssert("用户已成功登录");
await agent.aiAssert("左侧有分类筛选器");
```

重要特性：
1. 使用AI驱动的操作，用自然语言描述操作意图
2. aiAction: 执行操作（点击、输入、滑动等）
3. aiWaitFor: 等待条件满足
4. aiQuery: 查询页面信息
5. aiAssert: 验证页面状态
6. aiActionContext: 设置全局操作上下文（处理弹窗等）

请生成完整的测试脚本，包含：
- 正确的导入语句
- 设备连接和初始化
- AndroidAgent创建和配置
- 使用AI驱动的操作方法
- 完整的测试步骤实现
- 适当的等待和验证
- 错误处理

当你完成脚本生成后，请说"SCRIPT_GENERATED"。
"""
        
        return AssistantAgent(
            name="script_generator",
            model_client=deepseek_model_client(),
            system_message=system_message
        )
    
    def _create_code_reviewer(self) -> AssistantAgent:
        """创建代码审查智能体"""
        system_message = """
你是一个资深的Android自动化测试开发专家，专门审查@midscene/android框架的TypeScript测试脚本。

你的职责：
1. 检查脚本的完整性和正确性
2. 验证@midscene/android API使用是否正确
3. 检查AI驱动操作的描述是否清晰准确
4. 确保脚本覆盖所有测试步骤和验证点
5. 验证脚本可以直接运行

重点审查项目：
- 导入语句：必须使用 @midscene/android
- 设备连接：正确使用 getConnectedDevices 和 AndroidDevice
- AI智能体：正确创建和配置 AndroidAgent
- AI操作：aiAction 描述是否清晰、具体、可执行
- AI等待：aiWaitFor 条件是否明确
- AI查询：aiQuery 格式和用途是否正确
- AI断言：aiAssert 验证点是否完整
- 错误处理：是否包含适当的异常处理
- 代码结构：是否清晰易读，注释完整

@midscene/android最佳实践：
1. 使用自然语言描述操作意图，而不是技术细节
2. aiAction应该描述用户行为："点击登录按钮"而不是"click('#login')"
3. 设置合适的aiActionContext处理通用弹窗
4. 使用aiWaitFor确保页面状态稳定
5. 用aiAssert验证关键业务逻辑
6. aiQuery获取页面数据时指定清晰的数据结构

如果脚本质量良好且符合@midscene/android最佳实践，请回复"REVIEW_APPROVED"。
如果需要修改，请提供具体的修改建议，重点关注AI操作描述的准确性。
"""
        
        return AssistantAgent(
            name="code_reviewer",
            model_client=deepseek_model_client(),
            system_message=system_message
        )
    
    def _create_team(self) -> RoundRobinGroupChat:
        """创建智能体团队"""
        # 定义终止条件
        termination_condition = TextMentionTermination("REVIEW_APPROVED") | MaxMessageTermination(10)
        
        # 创建团队
        team = RoundRobinGroupChat(
            [self.script_generator, self.code_reviewer],
            termination_condition=termination_condition
        )
        
        return team
    
    async def generate_script(self, case_info: CaseElementInfo) -> Dict[str, Any]:
        """
        生成自动化测试脚本
        
        Args:
            case_info: 用例和元素信息
            
        Returns:
            Dict[str, Any]: 生成结果，包含脚本内容和元数据
        """
        try:
            logger.info(f"开始生成脚本，用例ID: {case_info.test_case.tcid}")
            
            # 构建生成任务
            task = self._build_generation_task(case_info)
            
            # 运行团队协作
            result = await self.team.run(task=task)
            
            # 解析结果
            script_content = self._extract_script_from_result(result)
            
            # 构建返回结果
            generation_result = {
                "success": True,  # 添加成功标志
                "script_content": script_content,
                "script_name": f"{case_info.test_case.tcid}_test.ts",
                "framework": "midsence.js",
                "language": "typescript",
                "metadata": {
                    "test_case_id": case_info.test_case.tcid,
                    "business_module": case_info.business_context.get("module_name"),
                    "element_count": len(case_info.related_elements),
                    "generation_messages": len(result.messages) if result.messages else 0
                }
            }
            
            logger.info(f"脚本生成完成: {generation_result['script_name']}")
            return generation_result
            
        except Exception as e:
            logger.error(f"生成脚本失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "script_content": None,
                "metadata": {
                    "test_case_id": case_info.test_case.tcid if case_info and case_info.test_case else "unknown"
                }
            }
    
    def _build_generation_task(self, case_info: CaseElementInfo) -> str:
        """构建脚本生成任务"""
        # 准备UI元素信息
        elements_info = []
        for element in case_info.related_elements[:10]:  # 限制元素数量
            elements_info.append({
                "type": element.element_type,
                "text": element.text or "",
                "resource_id": element.resource_id or "",
                "class_name": element.class_name or "",
                "bounds": element.bounds or "",
                "description": element.ai_description or element.description or ""
            })
        
        # 准备包组件信息
        components_info = []
        for comp in case_info.package_components:
            components_info.append({
                "package_name": comp.package_name,
                "component_name": comp.component_name,
                "description": comp.description
            })
        
        task = f"""
请根据以下信息生成完整的@midscene/android自动化测试脚本：

## 测试用例信息
- 用例ID: {case_info.test_case.tcid}
- 业务模块: {case_info.business_context.get("module_name", "未知")}
- 测试步骤: {case_info.test_case.steps}
- 预期结果: {case_info.test_case.expect_result}
- 前置条件: {case_info.test_case.pre_condition}

## 相关UI元素信息
{json.dumps(elements_info, ensure_ascii=False, indent=2)}

## 包组件信息
{json.dumps(components_info, ensure_ascii=False, indent=2)}

## 业务上下文
{json.dumps(case_info.business_context.get("analysis_result", {}), ensure_ascii=False, indent=2)}

请生成一个完整的TypeScript测试脚本，要求：

1. **使用@midscene/android框架**：
   - 正确导入 AndroidAgent, AndroidDevice, getConnectedDevices
   - 设备连接和初始化流程
   - 创建AI智能体并配置aiActionContext

2. **AI驱动的操作方式**：
   - 使用 agent.aiAction() 执行操作，用自然语言描述用户行为
   - 使用 agent.aiWaitFor() 等待页面状态
   - 使用 agent.aiQuery() 获取页面信息（如需要）
   - 使用 agent.aiAssert() 验证测试结果

3. **脚本结构**：
   - 包含完整的设备连接和应用启动
   - 覆盖所有测试步骤，每个步骤用清晰的AI操作描述
   - 包含所有验证点，确保预期结果得到验证
   - 适当的错误处理和资源清理

4. **AI操作描述原则**：
   - 描述用户意图而非技术实现
   - 使用具体、明确的自然语言
   - 包含必要的上下文信息
   - 考虑可能的弹窗和异常情况

请生成可直接运行的完整脚本，从设备连接到测试完成的全流程。
"""
        return task
    
    def _extract_script_from_result(self, result) -> str:
        """从团队协作结果中提取脚本内容"""
        try:
            if not result or not result.messages:
                logger.warning("团队协作结果为空")
                return self._generate_fallback_script()
            
            # 查找包含TypeScript代码的消息
            script_content = ""
            for message in result.messages:
                content = message.content
                
                # 查找代码块
                if "```typescript" in content or "```ts" in content:
                    # 提取代码块
                    lines = content.split('\n')
                    in_code_block = False
                    code_lines = []
                    
                    for line in lines:
                        if line.strip().startswith('```typescript') or line.strip().startswith('```ts'):
                            in_code_block = True
                            continue
                        elif line.strip() == '```' and in_code_block:
                            in_code_block = False
                            break
                        elif in_code_block:
                            code_lines.append(line)
                    
                    if code_lines:
                        script_content = '\n'.join(code_lines)
                        break
            
            if not script_content:
                # 如果没找到代码块，尝试提取最后一条消息的内容
                last_message = result.messages[-1].content
                if "import" in last_message and "async" in last_message:
                    script_content = last_message
                else:
                    script_content = self._generate_fallback_script()
            
            return script_content
            
        except Exception as e:
            logger.error(f"提取脚本内容失败: {e}")
            return self._generate_fallback_script()
    
    def _generate_fallback_script(self) -> str:
        """生成降级脚本"""
        return '''
import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';
import "dotenv/config";

const sleep = (ms: number) => new Promise((r) => setTimeout(r, ms));

async function runTest() {
  let device: AndroidDevice | null = null;
  let agent: AndroidAgent | null = null;

  try {
    console.log('开始执行自动化测试...');

    // 获取连接的设备
    const devices = await getConnectedDevices();
    if (devices.length === 0) {
      throw new Error('未找到连接的Android设备');
    }

    // 创建设备实例
    device = new AndroidDevice(devices[0].udid);
    console.log(`连接到设备: ${devices[0].udid}`);

    // 连接设备
    await device.connect();

    // 创建AI智能体
    agent = new AndroidAgent(device, {
      aiActionContext: 'If any location, permission, user agreement, etc. popup, click agree. If login page pops up, close it.',
    });

    // TODO: 根据具体测试用例实现以下步骤
    // 示例：启动应用
    // await device.launch('com.example.app');

    // 示例：AI驱动的操作
    // await agent.aiAction('点击登录按钮');
    // await agent.aiWaitFor('登录页面加载完成');
    // await agent.aiAction('输入用户名和密码');
    // await agent.aiAction('点击确认登录');
    // await agent.aiAssert('登录成功，显示用户主页');

    console.log('测试执行完成');

  } catch (error) {
    console.error('测试执行失败:', error);
    throw error;
  } finally {
    // 清理资源
    if (device) {
      await device.disconnect();
      console.log('设备连接已断开');
    }
  }
}

// 运行测试
runTest().catch(console.error);
'''


async def main():
    """测试函数"""
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    # 这里需要模拟CaseElementInfo数据进行测试
    print("脚本生成团队创建成功")


if __name__ == "__main__":
    asyncio.run(main())
