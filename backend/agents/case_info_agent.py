"""
用例信息获取智能体
接收自然语言用例信息，从数据库中检索相关页面元素，组装用例和元素信息
"""
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
import json

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage
from autogen_core import CancellationToken

from backend.script_database_manager import ScriptDatabaseManager
from backend.database import DatabaseManager
from backend.models.script_models import TestCase, PackageComponent
from backend.models.element_model import UIElement
from example.llms import deepseek_model_client

logger = logging.getLogger(__name__)


@dataclass
class CaseElementInfo:
    """用例和元素信息组合"""
    test_case: TestCase
    related_elements: List[UIElement]
    package_components: List[PackageComponent]
    business_context: Dict[str, Any]


class CaseInfoAgent:
    """用例信息获取智能体"""
    
    def __init__(self):
        """初始化智能体"""
        self.script_db = ScriptDatabaseManager()
        self.ui_db = DatabaseManager()

        # 初始化UI数据库
        if not self.ui_db.initialize_database():
            logger.warning("UI数据库初始化失败，将使用空的元素列表")
            self.ui_db = None

        self.agent = self._create_agent()
        
    def _create_agent(self) -> AssistantAgent:
        """创建AutoGen智能体"""
        system_message = """
你是一个专业的测试用例分析专家，负责分析自然语言测试用例并提取关键信息。

你的主要任务：
1. 分析用例的业务模块归属
2. 识别用例中涉及的UI操作和元素
3. 提取关键的操作步骤和验证点
4. 为后续的脚本生成提供结构化信息

分析时请关注：
- 业务模块识别（如：登录、注册、设置等）
- UI元素类型（按钮、输入框、文本等）
- 操作类型（点击、输入、滑动、验证等）
- 数据流和状态变化

请以JSON格式返回分析结果。
"""
        
        return AssistantAgent(
            name="case_info_agent",
            model_client=deepseek_model_client(),
            system_message=system_message
        )
    
    async def analyze_case_info(self, case_description: str, business_module: str = None) -> CaseElementInfo:
        """
        分析用例信息并获取相关元素
        
        Args:
            case_description: 自然语言用例描述
            business_module: 指定的业务模块（可选）
            
        Returns:
            CaseElementInfo: 组装好的用例和元素信息
        """
        try:
            logger.info(f"开始分析用例信息: {case_description[:100]}...")
            
            # 1. 使用智能体分析用例
            analysis_result = await self._analyze_with_agent(case_description, business_module)
            
            # 2. 根据分析结果查找相关数据
            case_info = await self._retrieve_related_data(analysis_result, case_description)
            
            logger.info(f"用例信息分析完成，找到 {len(case_info.related_elements)} 个相关元素")
            return case_info
            
        except Exception as e:
            logger.error(f"分析用例信息失败: {e}")
            raise

    async def analyze_case(self, case_description: str, business_module: str = None) -> CaseElementInfo:
        """
        分析用例信息的别名方法

        Args:
            case_description: 自然语言用例描述
            business_module: 指定的业务模块（可选）

        Returns:
            CaseElementInfo: 组装好的用例和元素信息
        """
        return await self.analyze_case_info(case_description, business_module)

    async def _analyze_with_agent(self, case_description: str, business_module: str = None) -> Dict[str, Any]:
        """使用智能体分析用例"""
        try:
            # 构建分析提示
            prompt = self._build_analysis_prompt(case_description, business_module)
            
            # 运行智能体分析
            result = await self.agent.run(task=prompt)
            
            if result and result.messages:
                content = result.messages[-1].content
                # 尝试解析JSON结果
                try:
                    return json.loads(content)
                except json.JSONDecodeError:
                    # 如果不是JSON，提取关键信息
                    return self._extract_key_info_from_text(content, case_description)
            else:
                logger.warning("智能体返回空结果")
                return self._create_fallback_analysis(case_description, business_module)
                
        except Exception as e:
            logger.error(f"智能体分析失败: {e}")
            return self._create_fallback_analysis(case_description, business_module)
    
    def _build_analysis_prompt(self, case_description: str, business_module: str = None) -> str:
        """构建分析提示"""
        prompt = f"""
请分析以下测试用例，提取关键信息：

用例描述：
{case_description}

{f"指定业务模块：{business_module}" if business_module else ""}

请分析并返回JSON格式结果，包含：
{{
    "business_module": "推断的业务模块名称",
    "ui_elements": [
        {{
            "element_type": "元素类型（button/input/text等）",
            "action": "操作类型（click/input/verify等）",
            "description": "元素描述",
            "text_content": "可能的文本内容",
            "resource_id": "可能的资源ID"
        }}
    ],
    "test_steps": [
        {{
            "step_number": 1,
            "action": "操作描述",
            "target": "目标元素",
            "data": "输入数据（如有）"
        }}
    ],
    "verification_points": [
        "验证点1",
        "验证点2"
    ]
}}
"""
        return prompt
    
    def _extract_key_info_from_text(self, content: str, case_description: str) -> Dict[str, Any]:
        """从文本中提取关键信息"""
        # 简单的关键词提取逻辑
        ui_elements = []
        
        # 常见UI元素关键词
        element_keywords = {
            "按钮": "button",
            "输入框": "input", 
            "文本": "text",
            "图片": "image",
            "列表": "list",
            "菜单": "menu"
        }
        
        for keyword, element_type in element_keywords.items():
            if keyword in case_description:
                ui_elements.append({
                    "element_type": element_type,
                    "action": "click" if element_type == "button" else "verify",
                    "description": f"包含'{keyword}'的元素",
                    "text_content": "",
                    "resource_id": ""
                })
        
        return {
            "business_module": "未知模块",
            "ui_elements": ui_elements,
            "test_steps": [{"step_number": 1, "action": case_description, "target": "未指定", "data": ""}],
            "verification_points": ["验证操作完成"]
        }
    
    def _create_fallback_analysis(self, case_description: str, business_module: str = None) -> Dict[str, Any]:
        """创建降级分析结果"""
        return {
            "business_module": business_module or "未知模块",
            "ui_elements": [],
            "test_steps": [{"step_number": 1, "action": case_description, "target": "未指定", "data": ""}],
            "verification_points": ["验证操作完成"]
        }
    
    async def _retrieve_related_data(self, analysis_result: Dict[str, Any], case_description: str) -> CaseElementInfo:
        """根据分析结果检索相关数据"""
        try:
            business_module = analysis_result.get("business_module", "未知模块")
            
            # 1. 查找或创建测试用例记录
            test_case = await self._find_or_create_test_case(case_description, business_module, analysis_result)
            
            # 2. 获取包组件映射
            package_components = self.script_db.get_package_components_by_module(business_module)
            
            # 3. 根据业务模块和UI元素描述查找相关页面元素
            related_elements = await self._find_related_ui_elements(business_module, analysis_result.get("ui_elements", []))
            
            # 4. 构建业务上下文
            business_context = {
                "analysis_result": analysis_result,
                "module_name": business_module,
                "element_count": len(related_elements),
                "component_count": len(package_components)
            }
            
            return CaseElementInfo(
                test_case=test_case,
                related_elements=related_elements,
                package_components=package_components,
                business_context=business_context
            )
            
        except Exception as e:
            logger.error(f"检索相关数据失败: {e}")
            raise
    
    async def _find_or_create_test_case(self, case_description: str, business_module: str, 
                                      analysis_result: Dict[str, Any]) -> TestCase:
        """查找或创建测试用例记录"""
        try:
            # 生成用例ID
            import hashlib
            case_id = f"TC_{hashlib.md5(case_description.encode()).hexdigest()[:8]}"
            
            # 查找现有用例
            existing_case = self.script_db.get_test_case_by_id(case_id)
            if existing_case:
                return existing_case

            # 创建业务模块（如果不存在）
            module = self.script_db.create_business_module(business_module, f"自动创建的模块: {business_module}")

            # 创建新的测试用例
            with self.script_db.get_session() as session:
                test_case = TestCase(
                    tcid=case_id,
                    case_name=f"自动生成用例_{case_id}",
                    group_name=business_module,
                    sub_group="自动化测试",
                    component="自动生成",
                    case_type="功能测试",
                    level="中",
                    steps=case_description,
                    expect_result="操作成功完成",
                    automated="是",
                    owner="系统",
                    execute_owner="系统",
                    business_module_id=module.id
                )
                session.add(test_case)
                session.commit()
                session.refresh(test_case)
                
                logger.info(f"创建新测试用例: {case_id}")
                return test_case
                
        except Exception as e:
            logger.error(f"查找或创建测试用例失败: {e}")
            raise
    
    async def _find_related_ui_elements(self, business_module: str, ui_elements_info: List[Dict]) -> List[UIElement]:
        """查找相关的UI元素"""
        try:
            related_elements = []

            # 检查UI数据库是否可用
            if self.ui_db is None or not hasattr(self.ui_db, 'SessionLocal') or self.ui_db.SessionLocal is None:
                logger.warning("UI数据库未初始化，返回空的元素列表")
                return []

            # 从现有数据库中查找相关元素
            with self.ui_db.get_session() as session:
                from backend.database import ElementTable

                # 根据业务模块相关的包名查找元素
                package_components = self.script_db.get_package_components_by_module(business_module)

                for component in package_components:
                    # 查找该包下的UI元素
                    element_records = session.query(ElementTable).filter(
                        ElementTable.package_name.like(f"%{component.package_name}%")
                    ).limit(10).all()  # 限制数量避免过多

                    # 转换为UIElement对象
                    for record in element_records:
                        ui_element = UIElement(
                            element_id=str(record.id),
                            element_type=record.element_type or "",
                            text=record.text or "",
                            resource_id=record.resource_id or "",
                            content_desc=record.content_desc or "",
                            package_name=record.package_name or "",
                            semantic_description=record.ai_description or "",
                            expected_action=record.expected_action or "",
                            element_category=record.element_category or "",
                            confidence_score=record.confidence_score or 0.0
                        )
                        related_elements.append(ui_element)

                # 如果没找到足够的元素，尝试通过元素类型匹配
                if len(related_elements) < 5:
                    for ui_info in ui_elements_info:
                        element_type = ui_info.get("element_type", "")
                        if element_type:
                            element_records = session.query(ElementTable).filter(
                                ElementTable.element_type == element_type
                            ).limit(3).all()

                            for record in element_records:
                                ui_element = UIElement(
                                    element_id=str(record.id),
                                    element_type=record.element_type or "",
                                    text=record.text or "",
                                    resource_id=record.resource_id or "",
                                    content_desc=record.content_desc or "",
                                    package_name=record.package_name or "",
                                    semantic_description=record.ai_description or "",
                                    expected_action=record.expected_action or "",
                                    element_category=record.element_category or "",
                                    confidence_score=record.confidence_score or 0.0
                                )
                                related_elements.append(ui_element)

            # 去重
            seen_ids = set()
            unique_elements = []
            for element in related_elements:
                if element.element_id not in seen_ids:
                    seen_ids.add(element.element_id)
                    unique_elements.append(element)

            logger.info(f"找到 {len(unique_elements)} 个相关UI元素")
            return unique_elements[:20]  # 最多返回20个元素

        except Exception as e:
            logger.error(f"查找相关UI元素失败: {e}")
            return []


async def main():
    """测试函数"""
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建智能体
    agent = CaseInfoAgent()
    
    # 测试用例
    case_description = """
    1. 打开应用
    2. 点击登录按钮
    3. 输入用户名"<EMAIL>"
    4. 输入密码"123456"
    5. 点击确认登录
    6. 验证登录成功，显示用户主页
    """
    
    # 分析用例信息
    result = await agent.analyze_case_info(case_description, "登录模块")
    
    print(f"测试用例ID: {result.test_case.tcid}")
    print(f"业务模块: {result.test_case.business_module_id}")
    print(f"相关元素数量: {len(result.related_elements)}")
    print(f"包组件数量: {len(result.package_components)}")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
