"""
单脚本生成智能体
基于Midscene.js API生成可执行的TypeScript自动化脚本
"""
import os
import sys
import json
import asyncio
from typing import Dict, Any, Optional, List
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加相对路径导入
sys.path.append(str(Path(__file__).parent.parent))

from config import config
from utils.autogen_client import AutoGenClientManager
from utils.database_manager import DatabaseManager
from new_backend.models.case_models import TestCase, CaseAnalysisResult
from new_backend.models.script_models import GeneratedScript


class ScriptGenerationAgent:
    """单脚本生成智能体"""
    
    def __init__(self):
        """初始化脚本生成智能体"""
        self.config = config
        self.agent_name = "script_generation_agent"
        
        # 核心组件
        self.autogen_client = AutoGenClientManager()
        self.db_manager = DatabaseManager()
        
        # 智能体
        self.generation_agent = None
        
        # 初始化智能体
        self._initialize_agent()
    
    def _initialize_agent(self):
        """初始化脚本生成智能体"""
        try:
            logger.info("初始化脚本生成智能体...")
            
            # 创建脚本生成智能体
            self.generation_agent = self.autogen_client.create_text_agent(
                name="script_generation_agent",
                system_message=self._get_script_generation_prompt()
            )
            
            if self.generation_agent:
                logger.info("脚本生成智能体初始化成功")
            else:
                logger.warning("脚本生成智能体初始化失败，将使用降级模式")
            
        except Exception as e:
            logger.error(f"初始化脚本生成智能体失败: {e}")
            self.generation_agent = None
    
    def _get_script_generation_prompt(self) -> str:
        """获取脚本生成提示词"""
        return """你是一个专业的Android自动化测试脚本生成专家，专门使用Midscene.js框架。

任务：根据测试用例分析结果和元素库信息，生成完整的TypeScript自动化测试脚本。

Midscene.js框架API参考（优先使用自然语言描述）：
```typescript
import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';
import "dotenv/config";

const sleep = (ms: number) => new Promise((r) => setTimeout(r, ms));

// 设备连接和初始化
const devices = await getConnectedDevices();
const device = new AndroidDevice(devices[0].udid, {
    imeStrategy: "yadb-for-non-ascii"  // 支持非ASCII字符输入
});

// 创建AI智能体（核心功能）
const agent = new AndroidAgent(device, {
  aiActionContext: '如果出现位置、权限、用户协议等弹窗，点击同意。如果出现登录页面，关闭它'
});

await device.connect();
await device.launch('应用包名');

// AI驱动的操作（优先使用，支持自然语言描述位置）
await agent.aiAction('点击页面底部的发送按钮');
await agent.aiAction('在顶部搜索框输入 "关键词"');
await agent.aiAction('向下滑动到页面底部');
await agent.aiAction('长按右上角的设置图标');

// 更精确的操作方法（当需要指定位置时）
await agent.aiTap('发送按钮');           // 点击指定元素
await agent.aiInput('搜索内容', '搜索框'); // 在指定输入框输入
await agent.aiScroll();                  // 滚动页面
await agent.aiSwipe('up');              // 向上滑动

// AI驱动的等待（使用自然语言描述状态）
await agent.aiWaitFor("页面完全加载，显示主要内容");
await agent.aiWaitFor("弹窗消失，返回主页面");
await agent.aiWaitFor("搜索结果列表显示");

// AI驱动的查询（获取页面信息）
const items = await agent.aiQuery("{title: string, price: number}[], 获取商品列表信息");
const userInfo = await agent.aiQuery("string, 获取当前用户名");
const feedbackOptions = await agent.aiQuery("{options: string[]}, 获取反馈选项列表");

// AI驱动的断言（验证页面状态）
await agent.aiAssert("页面显示搜索结果列表");
await agent.aiAssert("用户已成功登录，显示用户头像");
await agent.aiAssert("底部导航栏包含首页、分类、购物车、我的四个选项");
```

核心原则：
1. **优先使用自然语言描述位置**：如"页面底部的发送按钮"、"顶部搜索框"、"右上角设置图标"
2. **利用元素库信息**：当分析结果提供了元素映射信息时，使用元素的文本内容和描述来构建自然语言位置描述
3. **避免硬编码坐标**：完全依赖AI理解自然语言描述来定位元素
4. **使用语义化描述**：描述元素的功能和位置特征，而不是技术属性

元素库信息使用策略：
- 当元素映射提供了element_text时，优先使用文本内容作为定位描述
- 当元素映射提供了target_description时，将其转换为自然语言位置描述
- 结合元素类型(element_type)来构建更准确的操作描述
- 利用匹配置信度(match_confidence)来选择最佳的元素描述

脚本生成要求：
1. 生成完整可执行的TypeScript代码，包含完整的函数结构
2. 包含完整的错误处理和资源清理逻辑
3. 优先使用aiAction、aiTap、aiInput等支持自然语言的方法
4. 每个操作都要有清晰的中文注释说明测试步骤
5. 使用元素库信息构建精确的自然语言位置描述
6. 包含适当的等待时间和状态验证
7. 添加console.log输出关键测试节点信息

脚本结构模板：
```typescript
/**
 * 自动生成的测试脚本
 * 测试用例ID: {case_id}
 * 业务模块: {module_name}
 * 生成时间: {generation_time}
 */

import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';
import "dotenv/config";

const sleep = (ms: number) => new Promise((r) => setTimeout(r, ms));

async function run{CaseName}Test() {
    let device: AndroidDevice | null = null;

    try {
        // 设备连接和初始化
        const devices = await getConnectedDevices();
        if (devices.length === 0) {
            throw new Error('No connected devices found');
        }

        device = new AndroidDevice(devices[0].udid, {
            imeStrategy: "yadb-for-non-ascii"
        });

        // 创建AI智能体，配置全局操作上下文
        const agent = new AndroidAgent(device, {
            aiActionContext: '如果出现位置、权限、用户协议等弹窗，点击同意。如果出现登录页面，关闭它'
        });

        await device.connect();
        await device.launch('{package_name}');
        await sleep(2000);

        // 测试步骤实现...

        console.log('测试成功完成！所有验证点已通过');

    } catch (error) {
        console.error('测试执行失败:', error);
        throw error;
    } finally {
        // 清理资源
        if (device) {
            await device.destroy();
        }
    }
}

// 执行测试
run{CaseName}Test().catch(console.error);
```

返回格式：
{
    "script_content": "完整的TypeScript脚本内容",
    "script_metadata": {
        "framework": "midscene.js",
        "language": "typescript",
        "estimated_execution_time": 60,
        "complexity_level": "medium",
        "uses_natural_language": true,
        "element_mapping_utilized": true
    },
    "generation_notes": "脚本生成说明，包括如何利用元素库信息",
    "optimization_suggestions": ["基于元素库信息的优化建议"]
}

请确保生成的脚本充分利用元素库信息，使用自然语言描述位置，符合Midscene.js最佳实践。"""
    
    async def generate_script(self, test_case: TestCase, 
                            analysis_result: CaseAnalysisResult) -> Dict[str, Any]:
        """
        生成自动化测试脚本
        
        Args:
            test_case: 测试用例对象
            analysis_result: 用例分析结果
            
        Returns:
            Dict[str, Any]: 生成结果
        """
        try:
            logger.info(f"开始生成脚本: {test_case.case_name}")
            
            # 准备生成输入
            generation_input = self._prepare_generation_input(test_case, analysis_result)
            
            # 使用AI智能体生成脚本
            if self.generation_agent and self.autogen_client.is_available():
                generation_result = await self._generate_with_ai(generation_input)
            else:
                # 降级处理
                logger.warning("使用降级模式生成脚本")
                generation_result = self._generate_with_template(test_case, analysis_result)
            
            # 验证和优化脚本
            validated_result = self._validate_script_result(generation_result, test_case)
            
            # 保存生成的脚本
            saved_script = self._save_generated_script(test_case, validated_result)
            
            logger.info(f"脚本生成完成: {test_case.case_name}")
            return {
                'success': True,
                'script_result': validated_result,
                'saved_script_id': saved_script.id if saved_script else None
            }
            
        except Exception as e:
            logger.error(f"生成脚本失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _prepare_generation_input(self, test_case: TestCase,
                                analysis_result: CaseAnalysisResult) -> str:
        """准备脚本生成输入"""
        try:
            input_parts = [
                "请根据以下测试用例和分析结果生成Midscene.js自动化测试脚本：",
                "",
                "=== 测试用例信息 ===",
                f"用例ID: {test_case.case_id}",
                f"用例名称: {test_case.case_name}",
                f"测试目的: {test_case.test_purpose or '未指定'}",
                f"应用包名: {test_case.package_name}",
                f"模块名称: {test_case.module_name or '未指定'}",
                "",
                "前置条件:",
                test_case.preconditions or "无特殊前置条件",
                "",
                "测试步骤:",
                test_case.test_steps,
                "",
                "预期结果:",
                test_case.expected_results or "无明确预期结果",
                "",
                "=== 智能分析结果 ===",
            ]

            # 添加提取的操作步骤（增强版，包含自然语言描述建议）
            if analysis_result.extracted_actions:
                input_parts.extend([
                    "",
                    "提取的操作步骤（含自然语言位置描述建议）:",
                ])

                for i, action in enumerate(analysis_result.extracted_actions, 1):
                    action_type = action.get('action_type', 'unknown')
                    action_desc = action.get('action_description', '')
                    target_element = action.get('target_element', '')
                    input_data = action.get('input_data')
                    ai_action_text = action.get('ai_action_text', '')

                    # 构建增强的操作描述
                    enhanced_desc = f"{i}. 操作类型: {action_type}"
                    enhanced_desc += f"\n   描述: {action_desc}"

                    if target_element:
                        enhanced_desc += f"\n   目标元素: {target_element}"
                        # 生成自然语言位置描述建议
                        natural_desc = self._generate_natural_language_description(target_element, action_type)
                        if natural_desc:
                            enhanced_desc += f"\n   建议的自然语言描述: {natural_desc}"

                    if input_data:
                        enhanced_desc += f"\n   输入数据: {input_data}"

                    if ai_action_text:
                        enhanced_desc += f"\n   AI操作文本: {ai_action_text}"

                    input_parts.append(enhanced_desc)

            # 添加提取的断言（增强版）
            if analysis_result.extracted_assertions:
                input_parts.extend([
                    "",
                    "提取的断言验证点（含自然语言描述建议）:",
                ])

                for i, assertion in enumerate(analysis_result.extracted_assertions, 1):
                    assertion_type = assertion.get('assertion_type', 'unknown')
                    assertion_desc = assertion.get('assertion_description', '')
                    expected_value = assertion.get('expected_value', '')
                    ai_assertion_text = assertion.get('ai_assertion_text', '')

                    enhanced_desc = f"{i}. 断言类型: {assertion_type}"
                    enhanced_desc += f"\n   描述: {assertion_desc}"

                    if expected_value:
                        enhanced_desc += f"\n   期望值: {expected_value}"

                    if ai_assertion_text:
                        enhanced_desc += f"\n   AI断言文本: {ai_assertion_text}"
                    else:
                        # 生成AI断言文本建议
                        ai_suggestion = self._generate_ai_assertion_text(assertion_desc, expected_value)
                        if ai_suggestion:
                            enhanced_desc += f"\n   建议的AI断言文本: {ai_suggestion}"

                    input_parts.append(enhanced_desc)

            # 添加元素库映射信息（增强版，提供自然语言描述）
            if analysis_result.matched_elements:
                input_parts.extend([
                    "",
                    "=== 元素库映射信息（用于构建自然语言位置描述） ===",
                ])

                for key, mapping in analysis_result.matched_elements.items():
                    target_desc = mapping.get('target_description', '')
                    element_type = mapping.get('element_type', '')
                    element_text = mapping.get('element_text', '')
                    element_id = mapping.get('matched_element_id', '')
                    match_confidence = mapping.get('match_confidence', 0)

                    mapping_desc = f"- {key}:"
                    mapping_desc += f"\n  目标描述: {target_desc}"
                    mapping_desc += f"\n  元素类型: {element_type}"
                    mapping_desc += f"\n  元素文本: {element_text or 'N/A'}"
                    mapping_desc += f"\n  匹配置信度: {match_confidence:.2f}"

                    # 生成自然语言位置描述建议
                    natural_location = self._generate_element_natural_description(mapping)
                    if natural_location:
                        mapping_desc += f"\n  建议的自然语言位置: {natural_location}"

                    input_parts.append(mapping_desc)

            input_parts.extend([
                "",
                "=== 脚本生成要求 ===",
                "请生成完整的TypeScript脚本，特别注意：",
                "1. 优先使用aiAction、aiTap、aiInput等支持自然语言描述的方法",
                "2. 利用上述元素库信息构建精确的自然语言位置描述",
                "3. 避免使用硬编码坐标，完全依赖AI理解自然语言",
                "4. 每个操作都要有清晰的中文注释说明测试步骤",
                "5. 包含完整的错误处理和资源清理逻辑",
                "6. 使用适当的等待时间和状态验证",
                "7. 添加console.log输出关键测试节点信息",
                "",
                "自然语言描述示例：",
                "- await agent.aiAction('点击页面底部的发送按钮');",
                "- await agent.aiTap('登录按钮');",
                "- await agent.aiInput('用户名', '顶部的用户名输入框');",
                "- await agent.aiAssert('页面显示用户登录成功的提示信息');",
                "",
                "请确保生成的脚本可以直接运行，并充分利用元素库信息优化定位精度。"
            ])

            return "\n".join(input_parts)

        except Exception as e:
            logger.error(f"准备生成输入失败: {e}")
            return f"生成脚本: {test_case.case_name}"

    def _generate_natural_language_description(self, target_element: str, action_type: str) -> str:
        """根据目标元素和操作类型生成自然语言描述建议"""
        try:
            if not target_element:
                return ""

            # 常见元素类型映射
            element_mappings = {
                '按钮': '按钮',
                'button': '按钮',
                '输入框': '输入框',
                'input': '输入框',
                'edittext': '输入框',
                '文本': '文本',
                'text': '文本',
                'textview': '文本',
                '图标': '图标',
                'icon': '图标',
                '菜单': '菜单',
                'menu': '菜单',
                '列表': '列表',
                'list': '列表',
                '卡片': '卡片',
                'card': '卡片'
            }

            # 位置关键词
            position_keywords = {
                '顶部': '顶部',
                '底部': '底部',
                '左侧': '左侧',
                '右侧': '右侧',
                '中间': '中间',
                '上方': '上方',
                '下方': '下方',
                '左上': '左上角',
                '右上': '右上角',
                '左下': '左下角',
                '右下': '右下角'
            }

            target_lower = target_element.lower()

            # 检测元素类型
            element_type = ""
            for key, value in element_mappings.items():
                if key in target_lower:
                    element_type = value
                    break

            # 检测位置信息
            position = ""
            for key, value in position_keywords.items():
                if key in target_element:
                    position = value
                    break

            # 构建自然语言描述
            if action_type == 'click' or action_type == 'tap':
                if position and element_type:
                    return f"点击{position}的{target_element}"
                elif element_type:
                    return f"点击{target_element}"
                else:
                    return f"点击{target_element}"
            elif action_type == 'input':
                if position:
                    return f"在{position}的{target_element}输入内容"
                else:
                    return f"在{target_element}输入内容"
            elif action_type == 'swipe' or action_type == 'scroll':
                return f"在{target_element}区域滑动"
            else:
                return f"对{target_element}执行{action_type}操作"

        except Exception as e:
            logger.warning(f"生成自然语言描述失败: {e}")
            return ""

    def _generate_ai_assertion_text(self, assertion_desc: str, expected_value: str) -> str:
        """生成AI断言文本建议"""
        try:
            if not assertion_desc:
                return ""

            # 常见断言类型映射
            if "页面" in assertion_desc and "显示" in assertion_desc:
                if expected_value:
                    return f"页面显示{expected_value}"
                else:
                    return "页面内容正确显示"
            elif "登录" in assertion_desc:
                return "用户成功登录，显示用户信息"
            elif "搜索" in assertion_desc:
                return "搜索结果正确显示"
            elif "弹窗" in assertion_desc or "对话框" in assertion_desc:
                if expected_value:
                    return f"弹窗显示{expected_value}"
                else:
                    return "弹窗正确显示"
            elif "按钮" in assertion_desc:
                if "状态" in assertion_desc:
                    return f"按钮状态正确变化"
                else:
                    return "按钮正确显示"
            else:
                if expected_value:
                    return f"验证{expected_value}正确显示"
                else:
                    return f"验证{assertion_desc}成功"

        except Exception as e:
            logger.warning(f"生成AI断言文本失败: {e}")
            return ""

    def _generate_element_natural_description(self, mapping: Dict[str, Any]) -> str:
        """根据元素映射信息生成自然语言位置描述"""
        try:
            target_desc = mapping.get('target_description', '')
            element_type = mapping.get('element_type', '')
            element_text = mapping.get('element_text', '')

            if not target_desc:
                return ""

            # 如果有元素文本，优先使用
            if element_text and element_text != 'N/A':
                # 根据元素类型构建描述
                if element_type in ['button', 'Button']:
                    return f"'{element_text}'按钮"
                elif element_type in ['input', 'EditText']:
                    return f"'{element_text}'输入框"
                elif element_type in ['text', 'TextView']:
                    return f"显示'{element_text}'的文本"
                else:
                    return f"'{element_text}'"

            # 如果没有文本，使用目标描述
            if "按钮" in target_desc or "button" in target_desc.lower():
                return f"{target_desc}"
            elif "输入" in target_desc or "input" in target_desc.lower():
                return f"{target_desc}"
            elif "图标" in target_desc or "icon" in target_desc.lower():
                return f"{target_desc}"
            else:
                return target_desc

        except Exception as e:
            logger.warning(f"生成元素自然语言描述失败: {e}")
            return ""

    async def _generate_with_ai(self, generation_input: str) -> Dict[str, Any]:
        """使用AI智能体生成脚本"""
        try:
            # 调用AI智能体
            result = await self.autogen_client.run_text_task(
                agent=self.generation_agent,
                content=generation_input
            )
            
            if result.get('success'):
                # 解析AI返回的结果
                content = result.get('content', '')
                script_data = self._parse_ai_generation_result(content)
                
                if script_data:
                    return script_data
                else:
                    logger.warning("AI脚本生成结果解析失败")
                    return self._create_empty_script_result()
            else:
                logger.warning(f"AI脚本生成失败: {result.get('error')}")
                return self._create_empty_script_result()
                
        except Exception as e:
            logger.error(f"AI脚本生成异常: {e}")
            return self._create_empty_script_result()
    
    def _parse_ai_generation_result(self, content: str) -> Optional[Dict[str, Any]]:
        """解析AI生成结果"""
        try:
            # 尝试提取JSON部分
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            
            if start_idx >= 0 and end_idx > start_idx:
                json_str = content[start_idx:end_idx]
                result = json.loads(json_str)
                
                # 验证必要字段
                if 'script_content' in result:
                    return result
            
            # 如果没有JSON格式，尝试提取代码块
            script_content = self._extract_code_from_content(content)
            if script_content:
                return {
                    'script_content': script_content,
                    'script_metadata': {
                        'framework': 'midscene.js',
                        'language': 'typescript',
                        'estimated_execution_time': 60,
                        'complexity_level': 'medium'
                    },
                    'generation_notes': 'AI生成的脚本',
                    'optimization_suggestions': []
                }
            
            return None
            
        except json.JSONDecodeError as e:
            logger.warning(f"解析AI生成结果JSON失败: {e}")
            # 尝试提取代码内容
            script_content = self._extract_code_from_content(content)
            if script_content:
                return {
                    'script_content': script_content,
                    'script_metadata': {
                        'framework': 'midscene.js',
                        'language': 'typescript'
                    },
                    'generation_notes': 'AI生成的脚本（JSON解析失败）'
                }
            return None
        except Exception as e:
            logger.warning(f"解析AI生成结果异常: {e}")
            return None
    
    def _extract_code_from_content(self, content: str) -> Optional[str]:
        """从内容中提取代码"""
        try:
            # 查找代码块
            import re
            
            # 匹配```typescript或```ts代码块
            ts_pattern = r'```(?:typescript|ts)\n(.*?)\n```'
            ts_match = re.search(ts_pattern, content, re.DOTALL)
            if ts_match:
                return ts_match.group(1).strip()
            
            # 匹配普通代码块
            code_pattern = r'```\n(.*?)\n```'
            code_match = re.search(code_pattern, content, re.DOTALL)
            if code_match:
                code_content = code_match.group(1).strip()
                # 检查是否包含TypeScript/JavaScript关键词
                if any(keyword in code_content for keyword in ['import', 'await', 'async', 'const', 'function']):
                    return code_content
            
            return None

        except Exception as e:
            logger.warning(f"提取代码失败: {e}")
            return None

    def _generate_with_template(self, test_case: TestCase,
                              analysis_result: CaseAnalysisResult) -> Dict[str, Any]:
        """使用模板生成脚本（降级模式）"""
        try:
            logger.info("使用模板生成脚本")

            # 生成脚本内容
            script_content = self._create_script_template(test_case, analysis_result)

            return {
                'script_content': script_content,
                'script_metadata': {
                    'framework': 'midscene.js',
                    'language': 'typescript',
                    'estimated_execution_time': 60,
                    'complexity_level': 'medium',
                    'generation_method': 'template'
                },
                'generation_notes': '基于模板的降级生成',
                'optimization_suggestions': ['建议使用AI模式重新生成以获得更好的脚本质量']
            }

        except Exception as e:
            logger.error(f"模板生成脚本失败: {e}")
            return self._create_empty_script_result()

    def _create_script_template(self, test_case: TestCase,
                              analysis_result: CaseAnalysisResult) -> str:
        """创建脚本模板"""
        try:
            # 脚本模板
            template_parts = [
                "/**",
                f" * 自动化测试脚本: {test_case.case_name}",
                f" * 用例ID: {test_case.case_id}",
                f" * 应用包名: {test_case.package_name}",
                f" * 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                " * 框架: Midscene.js",
                " */",
                "",
                "import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';",
                "import \"dotenv/config\";",
                "",
                "const sleep = (ms: number) => new Promise((r) => setTimeout(r, ms));",
                "",
                "async function main() {",
                "  let device: AndroidDevice | null = null;",
                "  let agent: AndroidAgent | null = null;",
                "",
                "  try {",
                "    console.log('开始执行自动化测试...');",
                "",
                "    // 获取连接的设备",
                "    const devices = await getConnectedDevices();",
                "    if (devices.length === 0) {",
                "      throw new Error('未找到连接的Android设备');",
                "    }",
                "",
                "    // 创建设备实例",
                "    device = new AndroidDevice(devices[0].udid);",
                "    console.log(`连接到设备: ${devices[0].udid}`);",
                "",
                "    // 连接设备",
                "    await device.connect();",
                "",
                "    // 创建AI智能体",
                "    agent = new AndroidAgent(device, {",
                "      aiActionContext: 'If any location, permission, user agreement, etc. popup, click agree. If login page pops up, close it.',",
                "    });",
                "",
                f"    // 启动应用: {test_case.package_name}",
                f"    await device.launch('{test_case.package_name}');",
                "    await sleep(3000); // 等待应用启动",
                "",
                "    // === 前置条件 ===",
                f"    // {test_case.preconditions or '无特殊前置条件'}",
                "",
                "    // === 测试步骤 ===",
            ]

            # 添加操作步骤
            if analysis_result.extracted_actions:
                for i, action in enumerate(analysis_result.extracted_actions, 1):
                    action_type = action.get('action_type', 'click')
                    action_desc = action.get('action_description', '')
                    ai_action_text = action.get('ai_action_text', action_desc)

                    template_parts.extend([
                        f"    // 步骤 {i}: {action_desc}",
                        f"    await agent.aiAction('{ai_action_text}');",
                        "    await sleep(1000); // 等待操作完成",
                        ""
                    ])
            else:
                # 如果没有分析结果，添加基本的操作模板
                template_parts.extend([
                    "    // TODO: 根据测试步骤添加具体操作",
                    "    // 示例操作：",
                    "    // await agent.aiAction('点击登录按钮');",
                    "    // await agent.aiAction('输入用户名和密码');",
                    ""
                ])

            # 添加断言步骤
            template_parts.extend([
                "    // === 验证结果 ===",
            ])

            if analysis_result.extracted_assertions:
                for i, assertion in enumerate(analysis_result.extracted_assertions, 1):
                    assertion_desc = assertion.get('assertion_description', '')
                    ai_assertion_text = assertion.get('ai_assertion_text', assertion_desc)

                    template_parts.extend([
                        f"    // 验证 {i}: {assertion_desc}",
                        f"    await agent.aiAssert('{ai_assertion_text}');",
                        ""
                    ])
            else:
                # 如果没有断言，添加基本的验证模板
                template_parts.extend([
                    "    // TODO: 根据预期结果添加具体验证",
                    "    // 示例验证：",
                    "    // await agent.aiAssert('页面显示登录成功');",
                    ""
                ])

            # 添加结尾部分
            template_parts.extend([
                "    console.log('测试执行完成');",
                "",
                "  } catch (error) {",
                "    console.error('测试执行失败:', error);",
                "    throw error;",
                "  } finally {",
                "    // 清理资源",
                "    if (device) {",
                "      try {",
                "        await device.disconnect();",
                "      } catch (e) {",
                "        console.warn('设备断开连接失败:', e);",
                "      }",
                "    }",
                "  }",
                "}",
                "",
                "// 运行测试",
                "main().catch(console.error);"
            ])

            return "\n".join(template_parts)

        except Exception as e:
            logger.error(f"创建脚本模板失败: {e}")
            return self._get_minimal_script_template(test_case)

    def _get_minimal_script_template(self, test_case: TestCase) -> str:
        """获取最小脚本模板"""
        return f"""/**
 * 自动化测试脚本: {test_case.case_name}
 * 用例ID: {test_case.case_id}
 * 应用包名: {test_case.package_name}
 */

import {{ AndroidAgent, AndroidDevice, getConnectedDevices }} from '@midscene/android';
import "dotenv/config";

async function main() {{
  try {{
    const devices = await getConnectedDevices();
    const device = new AndroidDevice(devices[0].udid);
    await device.connect();

    const agent = new AndroidAgent(device);
    await device.launch('{test_case.package_name}');

    // TODO: 添加测试步骤
    console.log('请根据测试用例添加具体的操作和验证');

  }} catch (error) {{
    console.error('测试失败:', error);
  }}
}}

main().catch(console.error);"""

    def _create_empty_script_result(self) -> Dict[str, Any]:
        """创建空的脚本结果"""
        return {
            'script_content': '// 脚本生成失败\nconsole.log("脚本生成失败，请手动编写");',
            'script_metadata': {
                'framework': 'midscene.js',
                'language': 'typescript',
                'estimated_execution_time': 0,
                'complexity_level': 'unknown'
            },
            'generation_notes': '脚本生成失败',
            'optimization_suggestions': ['请检查输入数据和生成配置']
        }

    def _validate_script_result(self, script_result: Dict[str, Any],
                               test_case: TestCase) -> Dict[str, Any]:
        """验证和优化脚本结果"""
        try:
            # 确保必要字段存在
            if 'script_content' not in script_result:
                script_result['script_content'] = self._get_minimal_script_template(test_case)

            if 'script_metadata' not in script_result:
                script_result['script_metadata'] = {
                    'framework': 'midscene.js',
                    'language': 'typescript'
                }

            # 验证脚本内容
            script_content = script_result['script_content']

            # 检查必要的导入
            if 'import' not in script_content or 'AndroidAgent' not in script_content:
                logger.warning("脚本缺少必要的导入，添加基本导入")
                script_content = self._add_missing_imports(script_content)
                script_result['script_content'] = script_content

            # 检查基本结构
            if 'async function' not in script_content and 'await' in script_content:
                logger.warning("脚本缺少async函数结构")

            # 添加脚本元数据
            metadata = script_result.get('script_metadata', {})
            metadata.update({
                'case_id': test_case.case_id,
                'case_name': test_case.case_name,
                'package_name': test_case.package_name,
                'generation_time': datetime.now().isoformat(),
                'script_length': len(script_content),
                'lines_of_code': len(script_content.split('\n'))
            })
            script_result['script_metadata'] = metadata

            # 计算复杂度
            complexity_score = self._calculate_script_complexity(script_content)
            metadata['complexity_score'] = complexity_score

            return script_result

        except Exception as e:
            logger.error(f"验证脚本结果失败: {e}")
            return script_result

    def _add_missing_imports(self, script_content: str) -> str:
        """添加缺失的导入"""
        try:
            basic_imports = [
                "import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';",
                "import \"dotenv/config\";",
                ""
            ]

            # 如果没有导入，添加到开头
            if 'import' not in script_content:
                return "\n".join(basic_imports) + script_content

            return script_content

        except Exception as e:
            logger.warning(f"添加导入失败: {e}")
            return script_content

    def _calculate_script_complexity(self, script_content: str) -> float:
        """计算脚本复杂度"""
        try:
            # 简单的复杂度计算
            complexity_indicators = {
                'aiAction': 1.0,
                'aiAssert': 1.0,
                'aiWaitFor': 0.5,
                'aiQuery': 1.5,
                'if': 0.5,
                'for': 1.0,
                'while': 1.0,
                'try': 0.5,
                'catch': 0.5
            }

            total_complexity = 0.0
            for indicator, weight in complexity_indicators.items():
                count = script_content.count(indicator)
                total_complexity += count * weight

            return min(total_complexity, 10.0)  # 限制最大复杂度

        except Exception as e:
            logger.warning(f"计算脚本复杂度失败: {e}")
            return 1.0

    def _save_generated_script(self, test_case: TestCase,
                             script_result: Dict[str, Any]) -> Optional[GeneratedScript]:
        """保存生成的脚本到数据库"""
        try:
            # 创建脚本对象
            script = GeneratedScript(
                script_id=f"script_{test_case.case_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                case_id=test_case.id,
                script_name=f"{test_case.module_name or 'unknown'}_{test_case.case_id}_test",
                script_description=f"自动生成的测试脚本: {test_case.case_name}",
                module_name=test_case.module_name,
                package_name=test_case.package_name,
                script_content=script_result.get('script_content', ''),
                script_template_version=self.config.midscene.script_template_version,
                midscene_version="latest",
                status='generated',
                generation_method='ai_generated' if self.generation_agent else 'template_based',
                script_metadata=script_result.get('script_metadata', {}),
                generation_config={
                    'agent_name': self.agent_name,
                    'generation_time': datetime.now().isoformat(),
                    'autogen_available': self.autogen_client.is_available()
                },
                generator_agent=self.agent_name,
                generation_model=self.config.autogen.model_name
            )

            # 保存到数据库
            saved_script = self.db_manager.save_generated_script(script)

            if saved_script:
                logger.info(f"脚本保存成功: {script.script_name}")

            return saved_script

        except Exception as e:
            logger.error(f"保存生成脚本失败: {e}")
            return None

    def get_script_summary(self, script_result: Dict[str, Any]) -> Dict[str, Any]:
        """获取脚本摘要"""
        try:
            script_content = script_result.get('script_content', '')
            metadata = script_result.get('script_metadata', {})

            # 统计脚本特征
            features = {
                'has_ai_actions': 'aiAction' in script_content,
                'has_ai_assertions': 'aiAssert' in script_content,
                'has_ai_waits': 'aiWaitFor' in script_content,
                'has_ai_queries': 'aiQuery' in script_content,
                'has_error_handling': 'try' in script_content and 'catch' in script_content,
                'has_device_management': 'device.connect' in script_content,
                'has_app_launch': 'device.launch' in script_content
            }

            # 计算质量评分
            quality_score = sum(features.values()) / len(features)

            summary = {
                'script_length': len(script_content),
                'lines_of_code': len(script_content.split('\n')),
                'estimated_execution_time': metadata.get('estimated_execution_time', 60),
                'complexity_level': metadata.get('complexity_level', 'medium'),
                'complexity_score': metadata.get('complexity_score', 1.0),
                'quality_score': quality_score,
                'features': features,
                'framework': metadata.get('framework', 'midscene.js'),
                'language': metadata.get('language', 'typescript'),
                'generation_method': metadata.get('generation_method', 'unknown')
            }

            return summary

        except Exception as e:
            logger.error(f"生成脚本摘要失败: {e}")
            return {}

    def cleanup(self):
        """清理资源"""
        try:
            if self.autogen_client:
                self.autogen_client.cleanup()

            logger.info("脚本生成智能体资源清理完成")

        except Exception as e:
            logger.warning(f"清理脚本生成智能体资源失败: {e}")
