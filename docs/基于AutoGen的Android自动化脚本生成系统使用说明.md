# 基于AutoGen的Android自动化脚本生成系统使用说明

## 系统概述

本系统基于AutoGen 0.5.7框架和Deepseek大模型，实现了从自然语言测试用例到midsence.js Android自动化脚本的完整生成流程。系统采用多智能体协作方式，确保生成的脚本质量高、覆盖完整。

## 系统架构

### 核心组件

1. **Excel数据读取工具** (`backend/excel_reader.py`)
   - 读取测试用例文件 (`backend/data/case/case.xlsx`)
   - 读取模块映射文件 (`backend/data/business_knowledge/package_component.xlsx`)

2. **数据库管理器** (`backend/script_database_manager.py`)
   - 管理测试用例、脚本和模块映射数据
   - 基于MySQL存储，支持完整的CRUD操作

3. **智能体团队**
   - **用例信息获取智能体** (`backend/agents/case_info_agent.py`)
   - **脚本生成团队** (`backend/agents/script_generation_team.py`)
   - **脚本审查智能体** (`backend/agents/script_review_agent.py`)
   - **文件生成智能体** (`backend/agents/file_generation_agent.py`)

4. **主控制器** (`backend/script_generation_controller.py`)
   - 协调各个智能体的工作流程
   - 提供统一的API接口

## 安装和配置

### 1. 环境要求

```bash
# Python 3.8+
pip install -r requirements.txt

# 主要依赖
pip install autogen-agentchat==0.5.7
pip install pandas openpyxl
pip install sqlalchemy pymysql
pip install asyncio
```

### 2. 数据库配置

修改 `backend/config.py` 中的数据库配置：

```python
@dataclass
class DatabaseConfig:
    host: str = "localhost"
    port: int = 3306
    username: str = "your_username"
    password: str = "your_password"
    database: str = "ui_analysis"
    charset: str = "utf8mb4"
```

### 3. 大模型配置

在 `example/llms.py` 中配置Deepseek模型：

```python
def deepseek_model_client() -> OpenAIChatCompletionClient:
    model_client = OpenAIChatCompletionClient(
        model="deepseek-v3",
        api_key="your_api_key",
        base_url="your_base_url"
    )
    return model_client
```

## 数据准备

### 1. 测试用例文件格式

在 `backend/data/case/case.xlsx` 中准备测试用例，包含以下列：

| 列名 | 说明 | 示例 |
|------|------|------|
| 用例ID | 唯一标识 | TC_001 |
| 业务模块 | 功能模块 | 登录模块 |
| 前置条件 | 测试前提 | 应用已安装 |
| 操作步骤 | 详细步骤 | 1.打开应用 2.点击登录... |
| 预期结果 | 期望结果 | 登录成功，显示主页 |
| 优先级 | 重要程度 | 高/中/低 |

### 2. 模块映射文件格式

在 `backend/data/business_knowledge/package_component.xlsx` 中配置模块映射：

| 列名 | 说明 | 示例 |
|------|------|------|
| 业务模块 | 模块名称 | 登录模块 |
| 包名 | Android包名 | com.example.login |
| 组件名称 | 组件标识 | LoginActivity |
| 描述 | 详细说明 | 用户登录页面 |

## 使用方法

### 1. 系统初始化

```python
from backend.script_generation_controller import ScriptGenerationController

# 创建控制器
controller = ScriptGenerationController()

# 初始化系统（加载Excel数据到数据库）
success = await controller.initialize_system()
```

### 2. 单个脚本生成

```python
# 自然语言用例描述
case_description = """
测试用例：用户登录功能

测试步骤：
1. 启动应用
2. 点击登录按钮
3. 输入用户名"<EMAIL>"
4. 输入密码"123456"
5. 点击确认登录
6. 验证登录成功

预期结果：
显示用户主页，登录状态正确
"""

# 生成脚本
result = await controller.generate_script_from_case_description(
    case_description, 
    business_module="登录模块"
)

if result["success"]:
    print(f"脚本生成成功: {result['script_file']}")
    print(f"审查评分: {result['review_score']}")
```

### 3. 从数据库用例生成

```python
# 使用数据库中的用例ID
result = await controller.generate_script_from_case_id("TC_001")
```

### 4. 批量生成

```python
# 批量生成指定用例
case_ids = ["TC_001", "TC_002", "TC_003"]
result = await controller.batch_generate_scripts(case_ids)

# 或按业务模块批量生成
result = await controller.batch_generate_scripts(business_module="登录模块")
```

## 生成的脚本示例

系统会在 `testcase/` 目录下生成TypeScript测试脚本：

```typescript
/**
 * 自动生成的测试脚本
 * 
 * 测试用例ID: TC_001
 * 业务模块: 登录模块
 * 生成时间: 2024-08-29 15:30:00
 */

import { Page } from '@midscene/web';

describe('登录模块测试', () => {
  let page: Page;

  beforeAll(async () => {
    page = new Page();
  });

  afterAll(async () => {
    if (page) {
      await page.close();
    }
  });

  test('用户登录功能', async () => {
    try {
      // 启动应用
      await page.waitFor(2000);
      
      // 点击登录按钮
      await page.click('登录');
      
      // 输入用户名
      await page.input('用户名', '<EMAIL>');
      
      // 输入密码
      await page.input('密码', '123456');
      
      // 点击确认登录
      await page.click('确认登录');
      
      // 验证登录成功
      await page.waitFor('用户主页');
      await page.assertExist('用户信息');
      
      console.log('登录测试完成');
      
    } catch (error) {
      console.error('登录测试失败:', error);
      throw error;
    }
  });
});
```

## 智能体协作流程

### 1. 用例信息获取智能体
- 分析自然语言用例描述
- 识别业务模块和UI操作
- 从数据库检索相关页面元素
- 组装完整的用例信息

### 2. 脚本生成团队（AutoGen Teams）
- **脚本生成智能体**: 根据用例和元素信息生成初始脚本
- **代码审查智能体**: 审查脚本质量和完整性
- 通过RoundRobinGroupChat实现协作对话
- 直到审查通过或达到最大轮次

### 3. 脚本审查智能体
- 全面审查脚本的语法和逻辑
- 检查测试覆盖率和API使用
- 提供详细的评分和改进建议
- 确保脚本可直接运行

### 4. 文件生成智能体
- 添加规范的文件头注释
- 生成合适的文件名和目录结构
- 保存到MySQL数据库
- 生成testcase目录下的.ts文件

## 系统测试

运行完整的系统测试：

```bash
python temp/test_script_generation_system.py
```

测试内容包括：
- Excel文件读取
- 数据库操作
- 系统初始化
- 单个脚本生成
- 批量脚本生成

## 监控和统计

```python
# 获取生成统计信息
stats = controller.get_generation_statistics()
print(f"总用例数: {stats['total_test_cases']}")
print(f"生成脚本数: {stats['total_generated_scripts']}")
print(f"成功率: {stats['success_rate']:.1f}%")
```

## 注意事项

1. **数据库连接**: 确保MySQL服务正常运行，数据库配置正确
2. **API密钥**: 配置有效的Deepseek模型API密钥
3. **文件权限**: 确保testcase目录有写入权限
4. **网络连接**: 智能体需要网络连接访问大模型API
5. **资源限制**: 批量生成时注意API调用频率限制

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查MySQL服务状态
   systemctl status mysql
   # 测试连接
   mysql -u username -p -h localhost
   ```

2. **Excel文件读取失败**
   - 检查文件路径是否正确
   - 确认文件格式为.xlsx
   - 验证列名是否匹配

3. **脚本生成质量低**
   - 检查用例描述是否详细
   - 确认相关UI元素数据是否充足
   - 调整智能体的system_message

4. **API调用失败**
   - 验证API密钥有效性
   - 检查网络连接
   - 确认API调用频率未超限

## 扩展开发

### 添加新的智能体

1. 继承AssistantAgent基类
2. 实现特定的system_message
3. 在主控制器中集成新智能体
4. 更新工作流程

### 支持新的测试框架

1. 修改脚本生成智能体的模板
2. 更新API调用方式
3. 调整文件生成格式
4. 测试兼容性

## 技术支持

如有问题，请检查：
1. 系统日志输出
2. 数据库连接状态
3. API调用响应
4. 文件生成结果

系统采用详细的日志记录，可通过日志快速定位问题。
