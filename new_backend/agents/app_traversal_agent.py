"""
APP元素自动遍历智能体
实现APP启动、页面遍历、元素分析调度算法，完成页面关联关系分析
"""
import os
import sys
import json
import asyncio
import time
from typing import Dict, Any, Optional, List, Set, Tuple
from pathlib import Path
from datetime import datetime
from collections import deque
from loguru import logger

# 添加相对路径导入
sys.path.append(str(Path(__file__).parent.parent))

from config import config
from agents.page_element_extraction_agent import PageElementExtractionAgent
from utils.database_manager import DatabaseManager
from models.page_models import UIPage, UIElement, AppInfo, AnalysisSession, PageNavigation


class AppTraversalAgent:
    """APP元素自动遍历智能体"""
    
    def __init__(self):
        """初始化APP遍历智能体"""
        self.config = config
        self.agent_name = "app_traversal_agent"
        
        # 核心组件
        self.page_extractor = PageElementExtractionAgent()
        self.db_manager = DatabaseManager()
        
        # 遍历状态
        self.current_session = None
        self.visited_pages = set()  # 已访问页面的哈希值
        self.page_queue = deque()   # 待访问页面队列
        self.page_graph = {}        # 页面关系图
        self.navigation_history = []  # 导航历史
        
        # 遍历配置
        self.max_depth = config.agents.max_traversal_depth
        self.max_pages = config.agents.max_pages_per_app
        self.similarity_threshold = config.agents.page_similarity_threshold
        
        logger.info("APP遍历智能体初始化完成")
    
    def start_app_analysis(self, package_name: str, device_id: Optional[str] = None,
                          max_depth: Optional[int] = None, max_pages: Optional[int] = None,
                          enable_ai_enhancement: bool = True) -> Dict[str, Any]:
        """
        开始APP分析
        
        Args:
            package_name: 应用包名
            device_id: 设备ID
            max_depth: 最大遍历深度
            max_pages: 最大页面数
            enable_ai_enhancement: 是否启用AI增强
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        try:
            logger.info(f"开始APP分析: {package_name}")
            
            # 更新配置
            if max_depth:
                self.max_depth = max_depth
            if max_pages:
                self.max_pages = max_pages
            
            # 连接设备
            if not self.page_extractor.connect_device(device_id):
                return {'success': False, 'error': '设备连接失败'}
            
            # 启动应用
            if not self._launch_app(package_name):
                return {'success': False, 'error': '应用启动失败'}
            
            # 创建或获取应用信息
            app_info = self._get_or_create_app_info(package_name)
            if not app_info:
                return {'success': False, 'error': '创建应用信息失败'}
            
            # 创建分析会话
            session = self._create_analysis_session(app_info, enable_ai_enhancement)
            if not session:
                return {'success': False, 'error': '创建分析会话失败'}
            
            self.current_session = session
            
            # 开始遍历
            result = self._start_traversal(app_info, enable_ai_enhancement)
            
            # 更新会话状态
            self._complete_analysis_session(session, result)
            
            return result
            
        except Exception as e:
            logger.error(f"APP分析失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _launch_app(self, package_name: str) -> bool:
        """启动应用"""
        try:
            logger.info(f"启动应用: {package_name}")
            
            # 使用页面提取器的UI助手启动应用
            success = self.page_extractor.ui_helper.launch_app(package_name)
            
            if success:
                # 等待应用完全启动
                time.sleep(3)
                
                # 验证应用是否正确启动
                current_app = self.page_extractor.ui_helper.get_current_app()
                if current_app and current_app.get('package') == package_name:
                    logger.info(f"应用启动成功: {package_name}")
                    return True
                else:
                    logger.warning(f"应用启动验证失败: 期望 {package_name}, 实际 {current_app}")
                    return False
            else:
                logger.error(f"应用启动失败: {package_name}")
                return False
                
        except Exception as e:
            logger.error(f"启动应用异常: {e}")
            return False
    
    def _get_or_create_app_info(self, package_name: str) -> Optional[AppInfo]:
        """获取或创建应用信息"""
        try:
            # 从数据库查找现有应用信息
            app_info = self.db_manager.get_app_by_package_name(package_name)
            
            if not app_info:
                # 创建新的应用信息
                current_app = self.page_extractor.ui_helper.get_current_app()
                
                app_info = AppInfo(
                    package_name=package_name,
                    app_name=self._extract_app_name(package_name),
                    main_activity=current_app.get('activity', '') if current_app else '',
                    analysis_status='analyzing'
                )
                
                # 保存到数据库
                app_info = self.db_manager.save_app_info(app_info)
                logger.info(f"创建新应用信息: {package_name}")
            else:
                # 更新分析状态
                app_info.analysis_status = 'analyzing'
                app_info.updated_at = datetime.now()
                self.db_manager.update_app_info(app_info)
                logger.info(f"使用现有应用信息: {package_name}")
            
            return app_info
            
        except Exception as e:
            logger.error(f"获取或创建应用信息失败: {e}")
            return None
    
    def _extract_app_name(self, package_name: str) -> str:
        """从包名提取应用名称"""
        try:
            # 简单的包名解析
            parts = package_name.split('.')
            if len(parts) >= 2:
                return parts[-1].replace('_', ' ').title()
            else:
                return package_name
                
        except Exception as e:
            logger.warning(f"提取应用名称失败: {e}")
            return package_name
    
    def _create_analysis_session(self, app_info: AppInfo, enable_ai_enhancement: bool) -> Optional[AnalysisSession]:
        """创建分析会话"""
        try:
            session_id = f"session_{app_info.package_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            session = AnalysisSession(
                session_id=session_id,
                app_id=app_info.id,
                max_depth=self.max_depth,
                max_pages=self.max_pages,
                enable_ai_enhancement=enable_ai_enhancement,
                status='running'
            )
            
            # 保存到数据库
            session = self.db_manager.save_analysis_session(session)
            logger.info(f"创建分析会话: {session_id}")
            
            return session
            
        except Exception as e:
            logger.error(f"创建分析会话失败: {e}")
            return None
    
    def _start_traversal(self, app_info: AppInfo, enable_ai_enhancement: bool) -> Dict[str, Any]:
        """开始页面遍历"""
        try:
            logger.info("开始页面遍历")
            
            # 重置遍历状态
            self.visited_pages.clear()
            self.page_queue.clear()
            self.page_graph.clear()
            self.navigation_history.clear()
            
            # 分析起始页面
            start_page, start_elements = self.page_extractor.extract_page_elements(
                app_info.package_name, enable_ai_enhancement
            )
            
            if not start_page:
                return {'success': False, 'error': '无法分析起始页面'}
            
            # 设置页面关联
            start_page.app_id = app_info.id
            start_page.session_id = self.current_session.id
            start_page.depth_level = 0
            
            # 保存起始页面
            saved_page = self.db_manager.save_page_with_elements(start_page, start_elements)
            if not saved_page:
                return {'success': False, 'error': '保存起始页面失败'}
            
            # 添加到已访问页面
            self.visited_pages.add(start_page.page_hash)
            self.page_graph[start_page.page_id] = {
                'page': start_page,
                'elements': start_elements,
                'children': [],
                'depth': 0
            }
            
            # 将可点击元素加入遍历队列
            self._add_actionable_elements_to_queue(start_page, start_elements, 0)
            
            # 开始BFS遍历
            traversal_result = self._bfs_traversal(app_info, enable_ai_enhancement)
            
            # 生成遍历报告
            report = self._generate_traversal_report(app_info)
            
            return {
                'success': True,
                'app_info': {
                    'package_name': app_info.package_name,
                    'app_name': app_info.app_name,
                    'total_pages': len(self.visited_pages),
                    'total_elements': sum(len(node['elements']) for node in self.page_graph.values())
                },
                'traversal_result': traversal_result,
                'report': report
            }
            
        except Exception as e:
            logger.error(f"页面遍历失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _add_actionable_elements_to_queue(self, page: UIPage, elements: List[UIElement], depth: int):
        """将可操作元素添加到遍历队列"""
        try:
            if depth >= self.max_depth:
                return
            
            actionable_elements = [elem for elem in elements if elem.is_actionable and elem.attributes]
            
            for element in actionable_elements:
                # 过滤掉一些不适合点击的元素
                if self._should_skip_element(element):
                    continue
                
                # 添加到队列
                self.page_queue.append({
                    'page_id': page.page_id,
                    'element': element,
                    'depth': depth + 1,
                    'action': 'click'
                })
            
            logger.debug(f"添加 {len(actionable_elements)} 个可操作元素到队列")
            
        except Exception as e:
            logger.warning(f"添加元素到队列失败: {e}")
    
    def _should_skip_element(self, element: UIElement) -> bool:
        """判断是否应该跳过某个元素"""
        try:
            # 跳过系统导航元素
            if element.resource_id:
                skip_ids = ['android:id/home', 'android:id/back', 'android:id/recent']
                if any(skip_id in element.resource_id for skip_id in skip_ids):
                    return True
            
            # 跳过太小的元素
            if element.position:
                pos = element.position
                width = pos.get('right', 0) - pos.get('left', 0)
                height = pos.get('bottom', 0) - pos.get('top', 0)
                if width < 20 or height < 20:
                    return True
            
            # 跳过某些类型的元素
            skip_types = ['android.widget.ScrollView', 'android.view.ViewGroup']
            if element.element_type in skip_types:
                return True
            
            return False

        except Exception as e:
            logger.warning(f"判断跳过元素失败: {e}")
            return True

    def _bfs_traversal(self, app_info: AppInfo, enable_ai_enhancement: bool) -> Dict[str, Any]:
        """BFS页面遍历"""
        try:
            pages_discovered = 1  # 起始页面
            elements_extracted = 0
            navigation_count = 0

            logger.info(f"开始BFS遍历，队列中有 {len(self.page_queue)} 个元素")

            while self.page_queue and pages_discovered < self.max_pages:
                # 从队列取出下一个要处理的元素
                queue_item = self.page_queue.popleft()

                page_id = queue_item['page_id']
                element = queue_item['element']
                depth = queue_item['depth']
                action = queue_item['action']

                if depth > self.max_depth:
                    continue

                logger.debug(f"处理元素: {element.element_type} - {element.text or element.resource_id}")

                # 执行操作
                navigation_success = self._perform_navigation_action(element, action)

                if navigation_success:
                    # 等待页面加载
                    time.sleep(2)

                    # 分析新页面
                    new_page, new_elements = self.page_extractor.extract_page_elements(
                        app_info.package_name, enable_ai_enhancement
                    )

                    if new_page and self._is_new_page(new_page):
                        # 设置页面信息
                        new_page.app_id = app_info.id
                        new_page.session_id = self.current_session.id
                        new_page.depth_level = depth
                        new_page.parent_page_id = page_id

                        # 保存新页面
                        saved_page = self.db_manager.save_page_with_elements(new_page, new_elements)

                        if saved_page:
                            # 记录导航关系
                            self._record_navigation(page_id, new_page.page_id, element, action)

                            # 更新遍历状态
                            self.visited_pages.add(new_page.page_hash)
                            self.page_graph[new_page.page_id] = {
                                'page': new_page,
                                'elements': new_elements,
                                'children': [],
                                'depth': depth
                            }

                            # 更新父页面的子页面列表
                            if page_id in self.page_graph:
                                self.page_graph[page_id]['children'].append(new_page.page_id)

                            # 添加新的可操作元素到队列
                            self._add_actionable_elements_to_queue(new_page, new_elements, depth)

                            pages_discovered += 1
                            elements_extracted += len(new_elements)
                            navigation_count += 1

                            logger.info(f"发现新页面: {new_page.page_name} (深度: {depth})")

                        # 返回上一页面（如果可能）
                        self._navigate_back()
                    else:
                        logger.debug("页面未变化或已访问过")
                        # 返回上一页面
                        self._navigate_back()
                else:
                    logger.debug("导航操作失败")

                # 更新会话进度
                if self.current_session:
                    self.current_session.current_depth = depth
                    self.current_session.pages_discovered = pages_discovered
                    self.current_session.elements_extracted = elements_extracted
                    self.db_manager.update_analysis_session(self.current_session)

            logger.info(f"BFS遍历完成: 发现 {pages_discovered} 个页面, {elements_extracted} 个元素")

            return {
                'pages_discovered': pages_discovered,
                'elements_extracted': elements_extracted,
                'navigation_count': navigation_count,
                'max_depth_reached': max(node['depth'] for node in self.page_graph.values()) if self.page_graph else 0
            }

        except Exception as e:
            logger.error(f"BFS遍历失败: {e}")
            return {'pages_discovered': 0, 'elements_extracted': 0, 'navigation_count': 0}

    def _perform_navigation_action(self, element: UIElement, action: str) -> bool:
        """执行导航操作"""
        try:
            if not element.position:
                return False

            pos = element.position
            center_x = (pos.get('left', 0) + pos.get('right', 0)) // 2
            center_y = (pos.get('top', 0) + pos.get('bottom', 0)) // 2

            ui_helper = self.page_extractor.ui_helper

            if action == 'click':
                # 点击元素
                ui_helper.device.click(center_x, center_y)
                logger.debug(f"点击元素: ({center_x}, {center_y})")
                return True
            elif action == 'long_click':
                # 长按元素
                ui_helper.device.long_click(center_x, center_y)
                logger.debug(f"长按元素: ({center_x}, {center_y})")
                return True
            else:
                logger.warning(f"不支持的操作类型: {action}")
                return False

        except Exception as e:
            logger.warning(f"执行导航操作失败: {e}")
            return False

    def _is_new_page(self, page: UIPage) -> bool:
        """判断是否为新页面"""
        try:
            # 检查页面哈希是否已存在
            if page.page_hash in self.visited_pages:
                return False

            # 检查关键元素哈希的相似度
            for visited_hash in self.visited_pages:
                similarity = self._calculate_page_similarity(page.key_elements_hash, visited_hash)
                if similarity > self.similarity_threshold:
                    logger.debug(f"页面相似度过高: {similarity}")
                    return False

            return True

        except Exception as e:
            logger.warning(f"判断新页面失败: {e}")
            return False

    def _calculate_page_similarity(self, hash1: str, hash2: str) -> float:
        """计算页面相似度"""
        try:
            if not hash1 or not hash2:
                return 0.0

            # 简单的字符串相似度计算
            if hash1 == hash2:
                return 1.0

            # 计算编辑距离
            def levenshtein_distance(s1, s2):
                if len(s1) < len(s2):
                    return levenshtein_distance(s2, s1)

                if len(s2) == 0:
                    return len(s1)

                previous_row = list(range(len(s2) + 1))
                for i, c1 in enumerate(s1):
                    current_row = [i + 1]
                    for j, c2 in enumerate(s2):
                        insertions = previous_row[j + 1] + 1
                        deletions = current_row[j] + 1
                        substitutions = previous_row[j] + (c1 != c2)
                        current_row.append(min(insertions, deletions, substitutions))
                    previous_row = current_row

                return previous_row[-1]

            distance = levenshtein_distance(hash1, hash2)
            max_len = max(len(hash1), len(hash2))
            similarity = 1.0 - (distance / max_len) if max_len > 0 else 0.0

            return similarity

        except Exception as e:
            logger.warning(f"计算页面相似度失败: {e}")
            return 0.0

    def _record_navigation(self, from_page_id: str, to_page_id: str,
                          trigger_element: UIElement, action: str):
        """记录页面导航关系"""
        try:
            navigation = PageNavigation(
                from_page_id=self.page_graph[from_page_id]['page'].id,
                to_page_id=self.page_graph[to_page_id]['page'].id,
                trigger_element_id=trigger_element.element_id,
                navigation_action=action,
                navigation_description=f"通过{action} {trigger_element.element_type}导航",
                navigation_metadata={
                    'trigger_element': {
                        'type': trigger_element.element_type,
                        'text': trigger_element.text,
                        'resource_id': trigger_element.resource_id
                    },
                    'timestamp': datetime.now().isoformat()
                }
            )

            # 保存导航关系
            self.db_manager.save_page_navigation(navigation)

            # 记录到导航历史
            self.navigation_history.append({
                'from_page': from_page_id,
                'to_page': to_page_id,
                'action': action,
                'element': trigger_element.element_id,
                'timestamp': datetime.now()
            })

            logger.debug(f"记录导航: {from_page_id} -> {to_page_id}")

        except Exception as e:
            logger.warning(f"记录导航关系失败: {e}")

    def _navigate_back(self) -> bool:
        """返回上一页面"""
        try:
            ui_helper = self.page_extractor.ui_helper
            ui_helper.device.press("back")
            time.sleep(1)
            return True

        except Exception as e:
            logger.warning(f"返回上一页面失败: {e}")
            return False

    def _generate_traversal_report(self, app_info: AppInfo) -> Dict[str, Any]:
        """生成遍历报告"""
        try:
            total_pages = len(self.page_graph)
            total_elements = sum(len(node['elements']) for node in self.page_graph.values())
            total_actionable = sum(
                len([e for e in node['elements'] if e.is_actionable])
                for node in self.page_graph.values()
            )

            # 统计深度分布
            depth_distribution = {}
            for node in self.page_graph.values():
                depth = node['depth']
                depth_distribution[depth] = depth_distribution.get(depth, 0) + 1

            # 统计元素类型
            element_types = {}
            for node in self.page_graph.values():
                for element in node['elements']:
                    elem_type = element.element_type
                    element_types[elem_type] = element_types.get(elem_type, 0) + 1

            report = {
                'app_info': {
                    'package_name': app_info.package_name,
                    'app_name': app_info.app_name
                },
                'traversal_statistics': {
                    'total_pages': total_pages,
                    'total_elements': total_elements,
                    'total_actionable_elements': total_actionable,
                    'total_navigations': len(self.navigation_history),
                    'max_depth': max(node['depth'] for node in self.page_graph.values()) if self.page_graph else 0
                },
                'depth_distribution': depth_distribution,
                'element_types': element_types,
                'page_graph_summary': {
                    page_id: {
                        'page_name': node['page'].page_name,
                        'depth': node['depth'],
                        'element_count': len(node['elements']),
                        'children_count': len(node['children'])
                    }
                    for page_id, node in self.page_graph.items()
                },
                'navigation_summary': [
                    {
                        'from_page': nav['from_page'],
                        'to_page': nav['to_page'],
                        'action': nav['action'],
                        'timestamp': nav['timestamp'].isoformat()
                    }
                    for nav in self.navigation_history
                ]
            }

            return report

        except Exception as e:
            logger.error(f"生成遍历报告失败: {e}")
            return {}

    def _complete_analysis_session(self, session: AnalysisSession, result: Dict[str, Any]):
        """完成分析会话"""
        try:
            session.status = 'completed' if result.get('success') else 'failed'
            session.completed_at = datetime.now()

            if result.get('success'):
                traversal_result = result.get('traversal_result', {})
                session.pages_discovered = traversal_result.get('pages_discovered', 0)
                session.elements_extracted = traversal_result.get('elements_extracted', 0)
            else:
                session.error_message = result.get('error', '未知错误')

            self.db_manager.update_analysis_session(session)
            logger.info(f"分析会话完成: {session.session_id}")

        except Exception as e:
            logger.error(f"完成分析会话失败: {e}")

    def get_traversal_status(self) -> Dict[str, Any]:
        """获取遍历状态"""
        return {
            'current_session': self.current_session.session_id if self.current_session else None,
            'visited_pages_count': len(self.visited_pages),
            'queue_size': len(self.page_queue),
            'navigation_count': len(self.navigation_history),
            'page_graph_size': len(self.page_graph)
        }

    def cleanup(self):
        """清理资源"""
        try:
            self.visited_pages.clear()
            self.page_queue.clear()
            self.page_graph.clear()
            self.navigation_history.clear()
            self.current_session = None

            if self.page_extractor:
                self.page_extractor.cleanup()

            logger.info("APP遍历智能体资源清理完成")

        except Exception as e:
            logger.warning(f"清理APP遍历智能体资源失败: {e}")
