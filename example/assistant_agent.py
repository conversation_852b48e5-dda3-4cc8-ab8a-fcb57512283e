import asyncio

from autogen_agentchat.agents import Assistant<PERSON>gent
from autogen_agentchat.messages import StructuredMessage
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient

# Define a tool that searches the web for information.
# For simplicity, we will use a mock function here that returns a static string.
from llms import deepseek_model_client,qwenvl_model_client
async def web_search(query: str) -> str:
    """Find information on the web"""
    return "AutoGen is a programming framework for building multi-agent applications."

agent = AssistantAgent(
    name="assistant",
    model_client=qwenvl_model_client(),
    tools=[web_search],
    system_message="Use tools to solve tasks.",
)

# Use asyncio.run(agent.run(...)) when running in a script.
async def main():
    result = await agent.run(task="Find information on AutoGen")
    print(result.messages)

if __name__ == '__main__':
    asyncio.run(main())
