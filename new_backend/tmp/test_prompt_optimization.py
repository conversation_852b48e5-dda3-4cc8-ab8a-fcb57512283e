#!/usr/bin/env python3
"""
测试脚本生成智能体的prompt优化
直接测试核心方法，避免复杂导入
"""
import os
import sys
from pathlib import Path

# 添加相对路径导入
sys.path.append(str(Path(__file__).parent.parent))


def test_prompt_optimization():
    """测试prompt优化"""
    try:
        print("="*80)
        print("测试脚本生成智能体Prompt优化")
        print("="*80)
        
        # 直接导入并测试ScriptGenerationAgent的方法
        from agents.script_generation_agent import ScriptGenerationAgent
        
        # 创建智能体实例
        agent = ScriptGenerationAgent()
        print("✓ 成功创建脚本生成智能体")
        
        # 测试1: 检查优化后的prompt
        print("\n1. 检查优化后的prompt...")
        prompt = agent._get_script_generation_prompt()
        
        # 检查关键优化点
        optimization_checks = [
            ("自然语言描述位置", "自然语言描述位置" in prompt),
            ("aiAction方法", "aiAction" in prompt),
            ("aiTap方法", "aiTap" in prompt),
            ("aiInput方法", "aiInput" in prompt),
            ("元素库信息", "元素库信息" in prompt),
            ("避免硬编码坐标", "避免硬编码坐标" in prompt),
            ("imeStrategy配置", "imeStrategy" in prompt),
            ("自然语言位置描述", "自然语言位置描述" in prompt)
        ]
        
        print("Prompt优化检查:")
        for check_name, result in optimization_checks:
            status = "✓" if result else "✗"
            print(f"  {status} {check_name}: {'通过' if result else '未找到'}")
        
        # 显示prompt的关键部分
        print(f"\nPrompt长度: {len(prompt)} 字符")
        print("\nPrompt关键内容预览:")
        print("-" * 60)
        lines = prompt.split('\n')
        for i, line in enumerate(lines[:25]):
            if line.strip():
                print(f"{i+1:2d}: {line}")
        print("-" * 60)
        
        # 测试2: 自然语言描述生成方法
        print("\n2. 测试自然语言描述生成方法...")
        
        test_cases = [
            ("登录按钮", "click"),
            ("顶部搜索框", "input"),
            ("底部发送按钮", "click"),
            ("右上角设置图标", "click"),
            ("页面内容区域", "swipe"),
            ("用户名输入框", "input"),
            ("确认对话框", "click")
        ]
        
        print("自然语言描述生成测试:")
        for element, action in test_cases:
            try:
                result = agent._generate_natural_language_description(element, action)
                print(f"  - {element} + {action} -> {result}")
            except Exception as e:
                print(f"  - {element} + {action} -> 错误: {e}")
        
        # 测试3: AI断言文本生成方法
        print("\n3. 测试AI断言文本生成方法...")
        
        assertion_cases = [
            ("页面显示登录成功", "用户信息"),
            ("弹窗显示确认信息", "确认对话框"),
            ("搜索结果显示", "商品列表"),
            ("按钮状态变化", "高亮状态"),
            ("用户登录验证", "登录成功页面")
        ]
        
        print("AI断言文本生成测试:")
        for desc, expected in assertion_cases:
            try:
                result = agent._generate_ai_assertion_text(desc, expected)
                print(f"  - {desc} + {expected} -> {result}")
            except Exception as e:
                print(f"  - {desc} + {expected} -> 错误: {e}")
        
        # 测试4: 元素自然语言描述生成
        print("\n4. 测试元素自然语言描述生成...")
        
        element_mappings = [
            {
                "target_description": "登录按钮",
                "element_type": "Button",
                "element_text": "登录",
                "matched_element_id": "login_btn"
            },
            {
                "target_description": "搜索输入框",
                "element_type": "EditText",
                "element_text": "请输入搜索内容",
                "matched_element_id": "search_input"
            },
            {
                "target_description": "设置图标",
                "element_type": "ImageView",
                "element_text": "",
                "matched_element_id": "settings_icon"
            },
            {
                "target_description": "确认对话框",
                "element_type": "TextView",
                "element_text": "确认删除此项目？",
                "matched_element_id": "confirm_dialog"
            }
        ]
        
        print("元素自然语言描述生成测试:")
        for mapping in element_mappings:
            try:
                result = agent._generate_element_natural_description(mapping)
                target = mapping.get('target_description', '')
                text = mapping.get('element_text', '')
                print(f"  - {target} (文本: {text}) -> {result}")
            except Exception as e:
                print(f"  - {mapping.get('target_description', '')} -> 错误: {e}")
        
        # 测试5: 检查输入准备方法的改进
        print("\n5. 测试输入准备方法...")
        
        # 创建模拟数据
        class MockTestCase:
            def __init__(self):
                self.case_id = "TEST_001"
                self.case_name = "测试用例"
                self.test_purpose = "测试目的"
                self.package_name = "com.test.app"
                self.module_name = "测试模块"
                self.preconditions = "前置条件"
                self.test_steps = "测试步骤"
                self.expected_results = "预期结果"
        
        class MockAnalysisResult:
            def __init__(self):
                self.extracted_actions = [
                    {
                        "step_index": 1,
                        "action_type": "click",
                        "target_element": "登录按钮",
                        "action_description": "点击登录按钮",
                        "ai_action_text": "点击页面底部的登录按钮"
                    }
                ]
                self.extracted_assertions = [
                    {
                        "step_index": 1,
                        "assertion_type": "page_navigation",
                        "assertion_description": "验证登录成功",
                        "expected_value": "用户主页",
                        "ai_assertion_text": "页面显示用户登录成功"
                    }
                ]
                self.matched_elements = {
                    "action_1": {
                        "target_description": "登录按钮",
                        "element_type": "Button",
                        "element_text": "登录",
                        "match_confidence": 0.95
                    }
                }
        
        test_case = MockTestCase()
        analysis_result = MockAnalysisResult()
        
        try:
            generation_input = agent._prepare_generation_input(test_case, analysis_result)
            print(f"✓ 输入准备成功，长度: {len(generation_input)} 字符")
            
            # 检查关键改进点
            improvements = [
                ("自然语言位置描述建议", "自然语言位置描述建议" in generation_input),
                ("元素库映射信息", "元素库映射信息" in generation_input),
                ("建议的自然语言描述", "建议的自然语言描述" in generation_input),
                ("AI操作文本", "AI操作文本" in generation_input),
                ("匹配置信度", "匹配置信度" in generation_input)
            ]
            
            print("输入准备改进检查:")
            for improvement_name, result in improvements:
                status = "✓" if result else "✗"
                print(f"  {status} {improvement_name}: {'包含' if result else '未包含'}")
                
        except Exception as e:
            print(f"✗ 输入准备测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    try:
        print("开始测试脚本生成智能体Prompt优化...")
        
        # 执行测试
        success = test_prompt_optimization()
        
        # 总结
        print("\n" + "="*80)
        print("测试总结")
        print("="*80)
        
        if success:
            print("🎉 脚本生成智能体Prompt优化测试通过！")
            print("\n主要优化点:")
            print("✓ 强调使用自然语言描述位置的API方法")
            print("✓ 增加了元素库信息的利用策略")
            print("✓ 提供了自然语言描述生成的辅助方法")
            print("✓ 改进了输入准备，包含更多上下文信息")
            print("✓ 优化了prompt结构，更符合Midscene.js最佳实践")
            print("\n参考优化后的脚本示例:")
            print("- await agent.aiAction('点击页面底部的发送按钮');")
            print("- await agent.aiTap('登录按钮');")
            print("- await agent.aiInput('用户名', '顶部的用户名输入框');")
            print("- await agent.aiAssert('页面显示用户登录成功的提示信息');")
        else:
            print("❌ 测试失败，请检查错误信息。")
        
    except Exception as e:
        print(f"❌ 测试程序执行失败: {e}")


if __name__ == "__main__":
    main()
