#!/usr/bin/env python3
"""
多智能体自动化脚本生成系统使用示例
演示如何使用系统的各种功能
"""
import os
import sys
import asyncio
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from main_controller import MainController
from loguru import logger


async def example_analyze_app():
    """示例：分析应用"""
    logger.info("=" * 50)
    logger.info("示例1: 分析应用")
    logger.info("=" * 50)
    
    controller = MainController()
    
    try:
        # 分析系统设置应用
        result = await controller.analyze_app(
            package_name="com.android.settings",
            app_name="系统设置"
        )
        
        if result.get('success'):
            logger.success("应用分析成功!")
            logger.info(f"工作流ID: {result.get('workflow_id')}")
            logger.info(f"遍历结果: {result.get('traversal_result', {}).get('summary', {})}")
        else:
            logger.error(f"应用分析失败: {result.get('error')}")
        
        return result
        
    except Exception as e:
        logger.error(f"应用分析异常: {e}")
        return {"success": False, "error": str(e)}
    finally:
        controller.cleanup()


async def example_process_single_case():
    """示例：处理单个测试用例"""
    logger.info("=" * 50)
    logger.info("示例2: 处理单个测试用例")
    logger.info("=" * 50)
    
    controller = MainController()
    
    try:
        # 定义测试用例
        test_case_data = {
            "case_id": "TC_001",
            "case_name": "登录功能测试",
            "test_purpose": "验证用户登录功能的正确性",
            "package_name": "com.example.app",
            "module_name": "login",
            "preconditions": "应用已安装，网络连接正常",
            "test_steps": """
1. 启动应用
2. 点击"登录"按钮
3. 输入用户名"testuser"
4. 输入密码"password123"
5. 点击"确认登录"按钮
6. 等待登录完成
            """,
            "expected_results": """
1. 应用成功启动并显示主界面
2. 登录页面正常显示
3. 用户名输入成功
4. 密码输入成功
5. 登录请求发送成功
6. 登录成功，跳转到用户主页
            """,
            "priority": "high",
            "test_type": "functional"
        }
        
        # 处理测试用例
        result = await controller.process_test_case(test_case_data)
        
        if result.get('success'):
            logger.success("测试用例处理成功!")
            logger.info(f"工作流ID: {result.get('workflow_id')}")
            
            # 显示分析结果摘要
            analysis_result = result.get('analysis_result', {}).get('analysis_result', {})
            if analysis_result:
                actions_count = len(analysis_result.get('extracted_actions', []))
                assertions_count = len(analysis_result.get('extracted_assertions', []))
                confidence = analysis_result.get('analysis_confidence', 0)
                
                logger.info(f"分析结果: {actions_count}个操作步骤, {assertions_count}个断言, 置信度: {confidence:.2f}")
            
            # 显示脚本生成结果
            script_result = result.get('script_result', {})
            if script_result.get('script_content'):
                script_length = len(script_result['script_content'])
                logger.info(f"生成脚本长度: {script_length} 字符")
                
                # 保存生成的脚本到文件
                script_file = project_root / "tmp" / f"example_script_{datetime.now().strftime('%Y%m%d_%H%M%S')}.ts"
                with open(script_file, 'w', encoding='utf-8') as f:
                    f.write(script_result['script_content'])
                logger.info(f"脚本已保存到: {script_file}")
        else:
            logger.error(f"测试用例处理失败: {result.get('error')}")
        
        return result
        
    except Exception as e:
        logger.error(f"测试用例处理异常: {e}")
        return {"success": False, "error": str(e)}
    finally:
        controller.cleanup()


async def example_batch_process():
    """示例：批量处理测试用例"""
    logger.info("=" * 50)
    logger.info("示例3: 批量处理测试用例")
    logger.info("=" * 50)
    
    controller = MainController()
    
    try:
        # 定义多个测试用例
        test_cases = [
            {
                "case_id": "TC_001",
                "case_name": "用户注册功能测试",
                "test_purpose": "验证新用户注册流程",
                "package_name": "com.example.app",
                "module_name": "register",
                "preconditions": "应用已安装",
                "test_steps": "1. 打开注册页面\n2. 填写用户信息\n3. 提交注册",
                "expected_results": "注册成功，收到确认信息",
                "priority": "high",
                "test_type": "functional"
            },
            {
                "case_id": "TC_002", 
                "case_name": "密码重置功能测试",
                "test_purpose": "验证密码重置功能",
                "package_name": "com.example.app",
                "module_name": "password",
                "preconditions": "用户已注册",
                "test_steps": "1. 点击忘记密码\n2. 输入邮箱\n3. 接收重置链接",
                "expected_results": "密码重置成功",
                "priority": "medium",
                "test_type": "functional"
            },
            {
                "case_id": "TC_003",
                "case_name": "搜索功能测试", 
                "test_purpose": "验证应用内搜索功能",
                "package_name": "com.example.app",
                "module_name": "search",
                "preconditions": "用户已登录",
                "test_steps": "1. 点击搜索框\n2. 输入关键词\n3. 查看搜索结果",
                "expected_results": "显示相关搜索结果",
                "priority": "medium",
                "test_type": "functional"
            }
        ]
        
        # 批量处理
        result = await controller.batch_process_cases(
            cases_data=test_cases,
            package_name="com.example.app"
        )
        
        if result.get('success'):
            logger.success("批量处理成功!")
            logger.info(f"工作流ID: {result.get('workflow_id')}")
            logger.info(f"总用例数: {result.get('total_cases')}")
            logger.info(f"成功数: {result.get('success_count')}")
            logger.info(f"失败数: {result.get('failed_count')}")
            
            # 显示每个用例的处理结果
            for case_result in result.get('results', []):
                case_name = case_result.get('case_name')
                success = case_result.get('result', {}).get('success', False)
                status = "✓" if success else "✗"
                logger.info(f"  {status} {case_name}")
        else:
            logger.error(f"批量处理失败: {result.get('error')}")
        
        return result
        
    except Exception as e:
        logger.error(f"批量处理异常: {e}")
        return {"success": False, "error": str(e)}
    finally:
        controller.cleanup()


async def example_system_status():
    """示例：获取系统状态"""
    logger.info("=" * 50)
    logger.info("示例4: 获取系统状态")
    logger.info("=" * 50)
    
    controller = MainController()
    
    try:
        status = await controller.get_system_status()
        
        logger.info(f"系统健康状态: {status.get('health', 'unknown')}")
        logger.info(f"检查时间: {status.get('system_time')}")
        
        # 显示各组件状态
        components = status.get('components', {})
        for component_name, component_status in components.items():
            status_icon = {
                'healthy': '✓',
                'warning': '⚠',
                'error': '✗'
            }.get(component_status.get('status'), '?')
            
            logger.info(f"  {status_icon} {component_name}: {component_status.get('message', 'N/A')}")
        
        return status
        
    except Exception as e:
        logger.error(f"获取系统状态异常: {e}")
        return {"health": "error", "error": str(e)}
    finally:
        controller.cleanup()


async def run_all_examples():
    """运行所有示例"""
    logger.info("开始运行所有示例...")
    
    examples = [
        ("系统状态检查", example_system_status),
        ("应用分析", example_analyze_app),
        ("单用例处理", example_process_single_case),
        ("批量处理", example_batch_process)
    ]
    
    results = {}
    
    for example_name, example_func in examples:
        try:
            logger.info(f"\n开始运行示例: {example_name}")
            result = await example_func()
            results[example_name] = result
            
            if result.get('success', True):  # 系统状态检查没有success字段
                logger.success(f"示例 '{example_name}' 运行成功")
            else:
                logger.warning(f"示例 '{example_name}' 运行失败")
                
        except Exception as e:
            logger.error(f"示例 '{example_name}' 运行异常: {e}")
            results[example_name] = {"success": False, "error": str(e)}
    
    # 生成示例运行报告
    report_file = project_root / "tmp" / f"example_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2, default=str)
    
    logger.info(f"示例运行报告保存到: {report_file}")
    
    # 统计结果
    success_count = sum(1 for result in results.values() if result.get('success', True))
    total_count = len(results)
    
    logger.info("=" * 60)
    logger.info(f"所有示例运行完成: {success_count}/{total_count} 成功")
    logger.info("=" * 60)


def setup_logging():
    """设置日志"""
    log_file = project_root / "tmp" / f"example_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    
    # 确保日志目录存在
    log_file.parent.mkdir(exist_ok=True)
    
    # 配置日志
    logger.remove()  # 移除默认处理器
    logger.add(
        sys.stdout,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
        level="INFO"
    )
    logger.add(
        log_file,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {message}",
        level="DEBUG"
    )
    
    logger.info(f"示例日志保存到: {log_file}")


async def main():
    """主函数"""
    setup_logging()
    
    logger.info("多智能体自动化脚本生成系统 - 使用示例")
    logger.info("=" * 60)
    
    try:
        # 运行所有示例
        await run_all_examples()
        
    except KeyboardInterrupt:
        logger.info("示例运行被用户中断")
    except Exception as e:
        logger.error(f"示例运行异常: {e}")


if __name__ == "__main__":
    asyncio.run(main())
