# 多智能体自动化脚本生成系统架构文档

## 系统概述

本系统是一个基于Midscene.js和AutoGen框架的多智能体协作系统，用于将自然语言测试用例自动转换为可执行的Android自动化测试脚本。

### 核心特性

- **多智能体协作**: 7个专业化智能体分工协作
- **AI驱动**: 基于大语言模型的智能分析和生成
- **自动化流程**: 从用例分析到脚本生成的全自动化流程
- **Page Object模式**: 采用业界最佳实践的页面对象设计模式
- **降级处理**: 完善的降级机制确保系统稳定性

## 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    用户接口层                                │
├─────────────────────────────────────────────────────────────┤
│                    智能体协调层                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │页面元素提取 │ │APP自动遍历  │ │用例分析     │           │
│  │智能体       │ │智能体       │ │智能体       │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │脚本生成     │ │脚本审查     │ │批量处理     │           │
│  │智能体       │ │智能体       │ │智能体       │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    核心服务层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │AutoGen      │ │UIAutomator2 │ │数据库       │           │
│  │客户端       │ │助手         │ │管理器       │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    数据存储层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │MySQL        │ │文件系统     │ │缓存系统     │           │
│  │数据库       │ │存储         │ │(可选)       │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

## 智能体详细说明

### 1. 页面元素提取智能体 (PageElementExtractionAgent)

**功能**: 提取Android应用页面的UI元素并进行语义增强

**核心流程**:
1. 使用UIAutomator2获取页面DOM结构
2. 解析XML获取元素属性（坐标、类型、文本等）
3. 截取页面截图
4. 调用多模态AI模型进行语义增强
5. 存储增强后的元素信息到数据库

**输入**: 应用包名、页面信息
**输出**: 语义增强的UI元素列表

### 2. APP自动遍历智能体 (AppTraversalAgent)

**功能**: 自动遍历应用页面，建立页面关系图

**核心算法**: 广度优先搜索(BFS)
**遍历策略**:
- 启动应用到首页
- 识别可操作元素
- 逐个点击并记录页面跳转
- 构建页面导航关系图
- 避免重复遍历和死循环

**输出**: 完整的应用页面结构和导航关系

### 3. 用例分析智能体 (CaseAnalysisAgent)

**功能**: 解析自然语言测试用例，提取操作步骤和断言

**分析维度**:
- 操作步骤识别（点击、输入、滑动等）
- 断言提取（页面验证、文本检查等）
- 元素匹配（将描述映射到具体UI元素）
- 风险识别和优化建议

**支持的操作类型**:
- click: 点击操作
- input: 文本输入
- swipe: 滑动操作
- scroll: 滚动操作
- wait: 等待操作
- back: 返回操作
- launch: 启动应用
- close: 关闭应用

### 4. 脚本生成智能体 (ScriptGenerationAgent)

**功能**: 基于Midscene.js API生成可执行的TypeScript脚本

**生成特性**:
- AI驱动的操作描述
- 完整的错误处理
- 详细的注释说明
- 符合Midscene.js最佳实践
- 可直接运行的完整脚本

**Midscene.js API使用**:
```typescript
// AI驱动的操作
await agent.aiAction('点击登录按钮');
await agent.aiWaitFor('页面加载完成');
await agent.aiAssert('用户已成功登录');
await agent.aiQuery('获取用户信息');
```

### 5. 脚本审查智能体 (ScriptReviewAgent)

**功能**: 对生成的脚本进行质量检查和优化

**审查维度**:
- 语法正确性
- 逻辑完整性
- 性能优化
- 最佳实践符合性
- 可维护性评估

### 6. 脚本生成工具 (ScriptGenerationTool)

**功能**: 文件生成和目录管理

**管理功能**:
- 脚本文件生成
- 版本控制
- 目录结构管理
- 依赖管理

### 7. 批量处理智能体 (BatchProcessingAgent)

**功能**: Excel批量处理和工作流编排

**处理能力**:
- Excel文件解析
- 批量用例处理
- 工作流编排
- 进度监控
- 结果汇总

## 数据模型设计

### 页面对象模型 (Page Object Model)

```python
# 应用信息
class AppInfo:
    - package_name: 应用包名
    - app_name: 应用名称
    - version: 版本号
    - analysis_status: 分析状态

# UI页面
class UIPage:
    - page_id: 页面ID
    - page_name: 页面名称
    - activity_name: Activity名称
    - screenshot_path: 截图路径

# UI元素
class UIElement:
    - element_id: 元素ID
    - element_type: 元素类型
    - text: 文本内容
    - resource_id: 资源ID
    - bounds: 边界坐标
    - semantic_description: 语义描述
```

### 测试用例模型

```python
# 测试用例
class TestCase:
    - case_id: 用例ID
    - case_name: 用例名称
    - test_purpose: 测试目的
    - test_steps: 测试步骤
    - expected_results: 预期结果

# 用例分析结果
class CaseAnalysisResult:
    - extracted_actions: 提取的操作步骤
    - extracted_assertions: 提取的断言
    - matched_elements: 匹配的元素
    - analysis_confidence: 分析置信度
```

## 技术栈

### 核心框架
- **AutoGen 0.5.7**: 多智能体协作框架
- **Midscene.js**: Android自动化测试框架
- **UIAutomator2**: Android UI自动化工具

### 数据存储
- **MySQL**: 主数据库
- **SQLAlchemy**: ORM框架
- **Redis**: 缓存系统（可选）

### AI模型
- **GPT-4**: 文本分析和生成
- **Claude**: 备用文本模型
- **GPT-4V**: 多模态分析

### 开发工具
- **Python 3.8+**: 主要开发语言
- **TypeScript**: 脚本生成目标语言
- **Loguru**: 日志记录
- **Pytest**: 测试框架

## 配置管理

### 环境配置

```python
# 数据库配置
DATABASE_URL = "mysql+pymysql://user:password@localhost:3306/dbname"

# AutoGen配置
AUTOGEN_MODEL_NAME = "gpt-4"
AUTOGEN_API_KEY = "your-api-key"

# UIAutomator2配置
DEVICE_SERIAL = "auto"  # 自动检测设备

# Midscene.js配置
SCRIPT_TEMPLATE_VERSION = "1.0"
```

### 智能体配置

每个智能体都有独立的配置参数：
- 模型选择
- 提示词模板
- 超时设置
- 重试策略
- 降级处理

## 部署和运行

### 环境要求

1. **Python环境**: Python 3.8+
2. **Android设备**: 启用USB调试的Android设备
3. **数据库**: MySQL 5.7+
4. **依赖包**: 见requirements.txt

### 安装步骤

```bash
# 1. 克隆项目
git clone <repository-url>
cd new_backend

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置环境变量
cp .env.example .env
# 编辑.env文件设置配置

# 4. 初始化数据库
python -c "from utils.database_manager import DatabaseManager; DatabaseManager()"

# 5. 运行调试脚本
python tmp/debug_system.py
```

### 使用示例

```python
# 基本使用流程
from agents.case_analysis_agent import CaseAnalysisAgent
from agents.script_generation_agent import ScriptGenerationAgent

# 1. 分析测试用例
case_agent = CaseAnalysisAgent()
analysis_result = await case_agent.analyze_test_case(test_case)

# 2. 生成测试脚本
script_agent = ScriptGenerationAgent()
script_result = await script_agent.generate_script(test_case, analysis_result)
```

## 监控和日志

### 日志系统

- **结构化日志**: 使用Loguru进行结构化日志记录
- **多级别日志**: DEBUG、INFO、WARNING、ERROR
- **文件轮转**: 自动日志文件轮转和压缩
- **实时监控**: 支持实时日志查看

### 性能监控

- **执行时间**: 记录每个智能体的执行时间
- **成功率**: 统计操作成功率
- **资源使用**: 监控内存和CPU使用情况
- **错误追踪**: 详细的错误堆栈追踪

## 扩展和定制

### 添加新智能体

1. 继承基础智能体类
2. 实现核心业务逻辑
3. 配置智能体参数
4. 注册到系统中

### 自定义提示词

每个智能体的提示词都可以自定义：
- 修改系统消息
- 调整输出格式
- 添加特定领域知识

### 集成新的AI模型

系统支持集成新的AI模型：
- 实现模型适配器
- 配置模型参数
- 测试模型性能

## 故障排除

### 常见问题

1. **设备连接失败**: 检查USB调试是否启用
2. **AI模型调用失败**: 检查API密钥和网络连接
3. **数据库连接失败**: 检查数据库配置和权限
4. **脚本生成质量差**: 调整提示词和模型参数

### 调试工具

- **调试脚本**: `tmp/debug_system.py`
- **日志分析**: 查看详细的执行日志
- **性能分析**: 使用内置的性能监控工具

## 最佳实践

### 开发建议

1. **模块化设计**: 保持代码模块化和可测试性
2. **错误处理**: 完善的异常处理和降级机制
3. **文档维护**: 及时更新文档和注释
4. **测试覆盖**: 编写充分的单元测试和集成测试

### 使用建议

1. **渐进式部署**: 先在测试环境验证再部署到生产环境
2. **监控告警**: 设置适当的监控和告警机制
3. **定期维护**: 定期更新依赖包和模型版本
4. **备份策略**: 制定数据备份和恢复策略

## 版本历史

- **v1.0.0**: 初始版本，包含7个核心智能体
- **v1.1.0**: 优化AI模型调用和错误处理
- **v1.2.0**: 添加批量处理和性能监控功能

## 贡献指南

欢迎贡献代码和建议：

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 创建Pull Request

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
