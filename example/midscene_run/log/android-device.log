[2025-08-29T17:06:36.847+08:00] Initializing ADB with device ID: ASALE3741B000022
[2025-08-29T17:06:36.848+08:00] adb shell wm,size
[2025-08-29T17:06:36.967+08:00] adb shell wm,size end
[2025-08-29T17:06:36.967+08:00] Using Physical size: 1080x2436
[2025-08-29T17:06:36.967+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:06:37.068+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: <PERSON>rror executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:06:37.068+08:00] Failed to get orientation from input, try display
[2025-08-29T17:06:37.068+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:06:37.184+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:06:37.185+08:00] Screen orientation (fallback): 0
[2025-08-29T17:06:37.185+08:00] ADB initialized successfully 
DeviceId: ASALE3741B000022
ScreenSize:
  physical size: 1080x2436

[2025-08-29T17:06:37.185+08:00] Launching app: https://www.ebay.com
[2025-08-29T17:06:37.185+08:00] adb startUri https://www.ebay.com
[2025-08-29T17:06:38.025+08:00] adb startUri https://www.ebay.com end
[2025-08-29T17:06:38.025+08:00] Successfully launched: https://www.ebay.com
[2025-08-29T17:06:43.108+08:00] screenshotBase64 begin
[2025-08-29T17:06:43.108+08:00] adb shell wm,size
[2025-08-29T17:06:43.196+08:00] adb shell wm,size end
[2025-08-29T17:06:43.196+08:00] Using Physical size: 1080x2436
[2025-08-29T17:06:43.196+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:06:43.297+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:06:43.297+08:00] Failed to get orientation from input, try display
[2025-08-29T17:06:43.297+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:06:43.407+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:06:43.407+08:00] Screen orientation (fallback): 0
[2025-08-29T17:06:43.408+08:00] adb getScreenDensity 
[2025-08-29T17:06:43.508+08:00] adb getScreenDensity  end
[2025-08-29T17:06:43.511+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:06:43.511+08:00] adb takeScreenshot 
[2025-08-29T17:06:43.717+08:00] adb takeScreenshot  end
[2025-08-29T17:06:43.717+08:00] adb.takeScreenshot completed
[2025-08-29T17:06:43.717+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:06:43.717+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:06:43.719+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:06:43.719+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_4a974768-1c4f-4285-a592-4f07bf8de952.png
[2025-08-29T17:06:44.210+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_4a974768-1c4f-4285-a592-4f07bf8de952.png end
[2025-08-29T17:06:44.210+08:00] adb.shell screencap completed
[2025-08-29T17:06:44.210+08:00] Pulling screenshot file from device
[2025-08-29T17:06:44.210+08:00] adb pull /data/local/tmp/midscene_screenshot_4a974768-1c4f-4285-a592-4f07bf8de952.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ojjo1po5pa.png
[2025-08-29T17:06:44.261+08:00] adb pull /data/local/tmp/midscene_screenshot_4a974768-1c4f-4285-a592-4f07bf8de952.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ojjo1po5pa.png end
[2025-08-29T17:06:44.261+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ojjo1po5pa.png
[2025-08-29T17:06:44.262+08:00] adb shell rm /data/local/tmp/midscene_screenshot_4a974768-1c4f-4285-a592-4f07bf8de952.png
[2025-08-29T17:06:44.333+08:00] adb shell rm /data/local/tmp/midscene_screenshot_4a974768-1c4f-4285-a592-4f07bf8de952.png end
[2025-08-29T17:06:44.333+08:00] Resizing screenshot image
[2025-08-29T17:06:44.472+08:00] Image resize completed
[2025-08-29T17:06:44.472+08:00] Converting to base64
[2025-08-29T17:06:44.472+08:00] screenshotBase64 end
[2025-08-29T17:06:44.472+08:00] adb shell wm,size
[2025-08-29T17:06:44.567+08:00] adb shell wm,size end
[2025-08-29T17:06:44.567+08:00] Using Physical size: 1080x2436
[2025-08-29T17:06:44.567+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:06:44.676+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:06:44.676+08:00] Failed to get orientation from input, try display
[2025-08-29T17:06:44.676+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:06:44.763+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:06:44.763+08:00] Screen orientation (fallback): 0
[2025-08-29T17:06:44.763+08:00] adb getScreenDensity 
[2025-08-29T17:06:44.843+08:00] adb getScreenDensity  end
[2025-08-29T17:07:27.173+08:00] Initializing ADB with device ID: ASALE3741B000022
[2025-08-29T17:07:27.175+08:00] adb shell wm,size
[2025-08-29T17:07:27.279+08:00] adb shell wm,size end
[2025-08-29T17:07:27.279+08:00] Using Physical size: 1080x2436
[2025-08-29T17:07:27.280+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:07:27.397+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:07:27.397+08:00] Failed to get orientation from input, try display
[2025-08-29T17:07:27.397+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:07:27.512+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:07:27.513+08:00] Screen orientation (fallback): 1
[2025-08-29T17:07:27.513+08:00] ADB initialized successfully 
DeviceId: ASALE3741B000022
ScreenSize:
  physical size: 1080x2436
  orientation size: 1

[2025-08-29T17:07:27.513+08:00] Launching app: https://www.ebay.com
[2025-08-29T17:07:27.513+08:00] adb startUri https://www.ebay.com
[2025-08-29T17:07:27.634+08:00] adb startUri https://www.ebay.com end
[2025-08-29T17:07:27.634+08:00] Successfully launched: https://www.ebay.com
[2025-08-29T17:07:32.721+08:00] screenshotBase64 begin
[2025-08-29T17:07:32.721+08:00] adb shell wm,size
[2025-08-29T17:07:32.793+08:00] adb shell wm,size end
[2025-08-29T17:07:32.794+08:00] Using Physical size: 1080x2436
[2025-08-29T17:07:32.794+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:07:32.878+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:07:32.879+08:00] Failed to get orientation from input, try display
[2025-08-29T17:07:32.879+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:07:33.000+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:07:33.000+08:00] Screen orientation (fallback): 0
[2025-08-29T17:07:33.000+08:00] adb getScreenDensity 
[2025-08-29T17:07:33.083+08:00] adb getScreenDensity  end
[2025-08-29T17:07:33.084+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:07:33.084+08:00] adb takeScreenshot 
[2025-08-29T17:07:33.256+08:00] adb takeScreenshot  end
[2025-08-29T17:07:33.256+08:00] adb.takeScreenshot completed
[2025-08-29T17:07:33.256+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:07:33.256+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:07:33.256+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:07:33.256+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_d4349a13-9708-4066-8f68-69b41f47de87.png
[2025-08-29T17:07:33.779+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_d4349a13-9708-4066-8f68-69b41f47de87.png end
[2025-08-29T17:07:33.779+08:00] adb.shell screencap completed
[2025-08-29T17:07:33.779+08:00] Pulling screenshot file from device
[2025-08-29T17:07:33.779+08:00] adb pull /data/local/tmp/midscene_screenshot_d4349a13-9708-4066-8f68-69b41f47de87.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\87fnjxtkg9c.png
[2025-08-29T17:07:33.823+08:00] adb pull /data/local/tmp/midscene_screenshot_d4349a13-9708-4066-8f68-69b41f47de87.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\87fnjxtkg9c.png end
[2025-08-29T17:07:33.823+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\87fnjxtkg9c.png
[2025-08-29T17:07:33.823+08:00] adb shell rm /data/local/tmp/midscene_screenshot_d4349a13-9708-4066-8f68-69b41f47de87.png
[2025-08-29T17:07:33.882+08:00] adb shell rm /data/local/tmp/midscene_screenshot_d4349a13-9708-4066-8f68-69b41f47de87.png end
[2025-08-29T17:07:33.882+08:00] Resizing screenshot image
[2025-08-29T17:07:34.003+08:00] Image resize completed
[2025-08-29T17:07:34.003+08:00] Converting to base64
[2025-08-29T17:07:34.003+08:00] screenshotBase64 end
[2025-08-29T17:07:34.003+08:00] adb shell wm,size
[2025-08-29T17:07:34.093+08:00] adb shell wm,size end
[2025-08-29T17:07:34.093+08:00] Using Physical size: 1080x2436
[2025-08-29T17:07:34.094+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:07:34.181+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:07:34.181+08:00] Failed to get orientation from input, try display
[2025-08-29T17:07:34.181+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:07:34.293+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:07:34.294+08:00] Screen orientation (fallback): 0
[2025-08-29T17:07:34.294+08:00] adb getScreenDensity 
[2025-08-29T17:07:34.396+08:00] adb getScreenDensity  end
[2025-08-29T17:07:39.406+08:00] screenshotBase64 begin
[2025-08-29T17:07:39.406+08:00] adb shell wm,size
[2025-08-29T17:07:39.500+08:00] adb shell wm,size end
[2025-08-29T17:07:39.500+08:00] Using Physical size: 1080x2436
[2025-08-29T17:07:39.500+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:07:39.581+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:07:39.581+08:00] Failed to get orientation from input, try display
[2025-08-29T17:07:39.581+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:07:39.688+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:07:39.688+08:00] Screen orientation (fallback): 0
[2025-08-29T17:07:39.688+08:00] adb getScreenDensity 
[2025-08-29T17:07:39.778+08:00] adb getScreenDensity  end
[2025-08-29T17:07:39.778+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:07:39.778+08:00] adb takeScreenshot 
[2025-08-29T17:07:39.977+08:00] adb takeScreenshot  end
[2025-08-29T17:07:39.977+08:00] adb.takeScreenshot completed
[2025-08-29T17:07:39.977+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:07:39.977+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:07:39.977+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:07:39.977+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_7fc3ca1c-e247-4e17-a886-38bfa6e41def.png
[2025-08-29T17:07:40.486+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_7fc3ca1c-e247-4e17-a886-38bfa6e41def.png end
[2025-08-29T17:07:40.486+08:00] adb.shell screencap completed
[2025-08-29T17:07:40.486+08:00] Pulling screenshot file from device
[2025-08-29T17:07:40.486+08:00] adb pull /data/local/tmp/midscene_screenshot_7fc3ca1c-e247-4e17-a886-38bfa6e41def.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\3xoo57tr7gq.png
[2025-08-29T17:07:40.533+08:00] adb pull /data/local/tmp/midscene_screenshot_7fc3ca1c-e247-4e17-a886-38bfa6e41def.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\3xoo57tr7gq.png end
[2025-08-29T17:07:40.533+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\3xoo57tr7gq.png
[2025-08-29T17:07:40.533+08:00] adb shell rm /data/local/tmp/midscene_screenshot_7fc3ca1c-e247-4e17-a886-38bfa6e41def.png
[2025-08-29T17:07:40.595+08:00] adb shell rm /data/local/tmp/midscene_screenshot_7fc3ca1c-e247-4e17-a886-38bfa6e41def.png end
[2025-08-29T17:07:40.595+08:00] Resizing screenshot image
[2025-08-29T17:07:40.638+08:00] Image resize completed
[2025-08-29T17:07:40.638+08:00] Converting to base64
[2025-08-29T17:07:40.638+08:00] screenshotBase64 end
[2025-08-29T17:07:40.638+08:00] adb shell wm,size
[2025-08-29T17:07:40.714+08:00] adb shell wm,size end
[2025-08-29T17:07:40.715+08:00] Using Physical size: 1080x2436
[2025-08-29T17:07:40.715+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:07:40.795+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:07:40.795+08:00] Failed to get orientation from input, try display
[2025-08-29T17:07:40.795+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:07:40.898+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:07:40.898+08:00] Screen orientation (fallback): 0
[2025-08-29T17:07:40.898+08:00] adb getScreenDensity 
[2025-08-29T17:07:40.992+08:00] adb getScreenDensity  end
[2025-08-29T17:07:40.994+08:00] screenshotBase64 begin
[2025-08-29T17:07:40.994+08:00] adb shell wm,size
[2025-08-29T17:07:41.082+08:00] adb shell wm,size end
[2025-08-29T17:07:41.082+08:00] Using Physical size: 1080x2436
[2025-08-29T17:07:41.082+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:07:41.177+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:07:41.177+08:00] Failed to get orientation from input, try display
[2025-08-29T17:07:41.177+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:07:41.297+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:07:41.297+08:00] Screen orientation (fallback): 0
[2025-08-29T17:07:41.297+08:00] adb getScreenDensity 
[2025-08-29T17:07:41.382+08:00] adb getScreenDensity  end
[2025-08-29T17:07:41.382+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:07:41.382+08:00] adb takeScreenshot 
[2025-08-29T17:07:41.570+08:00] adb takeScreenshot  end
[2025-08-29T17:07:41.570+08:00] adb.takeScreenshot completed
[2025-08-29T17:07:41.570+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:07:41.570+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:07:41.570+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:07:41.570+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_1fc5ba2d-be66-4c57-bd08-18bbcee79e6e.png
[2025-08-29T17:07:42.087+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_1fc5ba2d-be66-4c57-bd08-18bbcee79e6e.png end
[2025-08-29T17:07:42.087+08:00] adb.shell screencap completed
[2025-08-29T17:07:42.087+08:00] Pulling screenshot file from device
[2025-08-29T17:07:42.087+08:00] adb pull /data/local/tmp/midscene_screenshot_1fc5ba2d-be66-4c57-bd08-18bbcee79e6e.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ezpvzc2qmx.png
[2025-08-29T17:07:42.132+08:00] adb pull /data/local/tmp/midscene_screenshot_1fc5ba2d-be66-4c57-bd08-18bbcee79e6e.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ezpvzc2qmx.png end
[2025-08-29T17:07:42.132+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ezpvzc2qmx.png
[2025-08-29T17:07:42.133+08:00] adb shell rm /data/local/tmp/midscene_screenshot_1fc5ba2d-be66-4c57-bd08-18bbcee79e6e.png
[2025-08-29T17:07:42.198+08:00] adb shell rm /data/local/tmp/midscene_screenshot_1fc5ba2d-be66-4c57-bd08-18bbcee79e6e.png end
[2025-08-29T17:07:42.198+08:00] Resizing screenshot image
[2025-08-29T17:07:42.240+08:00] Image resize completed
[2025-08-29T17:07:42.240+08:00] Converting to base64
[2025-08-29T17:07:42.240+08:00] screenshotBase64 end
[2025-08-29T17:07:42.293+08:00] screenshotBase64 begin
[2025-08-29T17:07:42.293+08:00] adb shell wm,size
[2025-08-29T17:07:42.392+08:00] adb shell wm,size end
[2025-08-29T17:07:42.392+08:00] Using Physical size: 1080x2436
[2025-08-29T17:07:42.392+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:07:42.484+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:07:42.484+08:00] Failed to get orientation from input, try display
[2025-08-29T17:07:42.484+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:07:42.598+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:07:42.598+08:00] Screen orientation (fallback): 0
[2025-08-29T17:07:42.598+08:00] adb getScreenDensity 
[2025-08-29T17:07:42.683+08:00] adb getScreenDensity  end
[2025-08-29T17:07:42.683+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:07:42.683+08:00] adb takeScreenshot 
[2025-08-29T17:07:42.897+08:00] adb takeScreenshot  end
[2025-08-29T17:07:42.897+08:00] adb.takeScreenshot completed
[2025-08-29T17:07:42.897+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:07:42.897+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:07:42.897+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:07:42.897+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_b49f403e-6640-4589-9caf-e6242c87cbe5.png
[2025-08-29T17:07:43.416+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_b49f403e-6640-4589-9caf-e6242c87cbe5.png end
[2025-08-29T17:07:43.416+08:00] adb.shell screencap completed
[2025-08-29T17:07:43.416+08:00] Pulling screenshot file from device
[2025-08-29T17:07:43.416+08:00] adb pull /data/local/tmp/midscene_screenshot_b49f403e-6640-4589-9caf-e6242c87cbe5.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\yvyuzpn41zb.png
[2025-08-29T17:07:43.462+08:00] adb pull /data/local/tmp/midscene_screenshot_b49f403e-6640-4589-9caf-e6242c87cbe5.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\yvyuzpn41zb.png end
[2025-08-29T17:07:43.462+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\yvyuzpn41zb.png
[2025-08-29T17:07:43.463+08:00] adb shell rm /data/local/tmp/midscene_screenshot_b49f403e-6640-4589-9caf-e6242c87cbe5.png
[2025-08-29T17:07:43.533+08:00] adb shell rm /data/local/tmp/midscene_screenshot_b49f403e-6640-4589-9caf-e6242c87cbe5.png end
[2025-08-29T17:07:43.533+08:00] Resizing screenshot image
[2025-08-29T17:07:43.577+08:00] Image resize completed
[2025-08-29T17:07:43.577+08:00] Converting to base64
[2025-08-29T17:07:43.577+08:00] screenshotBase64 end
[2025-08-29T17:07:43.577+08:00] adb shell wm,size
[2025-08-29T17:07:43.673+08:00] adb shell wm,size end
[2025-08-29T17:07:43.673+08:00] Using Physical size: 1080x2436
[2025-08-29T17:07:43.673+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:07:43.766+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:07:43.766+08:00] Failed to get orientation from input, try display
[2025-08-29T17:07:43.766+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:07:43.878+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:07:43.878+08:00] Screen orientation (fallback): 0
[2025-08-29T17:07:43.878+08:00] adb getScreenDensity 
[2025-08-29T17:07:43.971+08:00] adb getScreenDensity  end
[2025-08-29T17:07:43.973+08:00] adb push D:\aigc\aigc_ui_tools\node_modules\@midscene\android\bin\yadb /data/local/tmp
[2025-08-29T17:07:44.085+08:00] adb push D:\aigc\aigc_ui_tools\node_modules\@midscene\android\bin\yadb /data/local/tmp end
[2025-08-29T17:07:44.086+08:00] adb shell input swipe 825 344 825 344 150
[2025-08-29T17:07:44.333+08:00] adb shell input swipe 825 344 825 344 150 end
[2025-08-29T17:07:44.333+08:00] adb shell app_process -Djava.class.path=/data/local/tmp/yadb /data/local/tmp com.ysbing.yadb.Main -keyboard "~CLEAR~"
[2025-08-29T17:07:45.602+08:00] adb shell app_process -Djava.class.path=/data/local/tmp/yadb /data/local/tmp com.ysbing.yadb.Main -keyboard "~CLEAR~" end
[2025-08-29T17:07:45.602+08:00] adb isSoftKeyboardPresent 
[2025-08-29T17:07:45.805+08:00] adb isSoftKeyboardPresent  end
[2025-08-29T17:07:45.805+08:00] adb shell app_process -Djava.class.path=/data/local/tmp/yadb /data/local/tmp com.ysbing.yadb.Main -keyboard "Headphones"
[2025-08-29T17:07:47.075+08:00] adb shell app_process -Djava.class.path=/data/local/tmp/yadb /data/local/tmp com.ysbing.yadb.Main -keyboard "Headphones" end
[2025-08-29T17:07:47.075+08:00] adb isSoftKeyboardPresent 
[2025-08-29T17:07:47.266+08:00] adb isSoftKeyboardPresent  end
[2025-08-29T17:07:47.266+08:00] Keyboard has no UI; no closing necessary
[2025-08-29T17:07:47.471+08:00] screenshotBase64 begin
[2025-08-29T17:07:47.471+08:00] adb shell wm,size
[2025-08-29T17:07:47.562+08:00] adb shell wm,size end
[2025-08-29T17:07:47.562+08:00] Using Physical size: 1080x2436
[2025-08-29T17:07:47.562+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:07:47.667+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:07:47.667+08:00] Failed to get orientation from input, try display
[2025-08-29T17:07:47.667+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:07:47.794+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:07:47.794+08:00] Screen orientation (fallback): 0
[2025-08-29T17:07:47.795+08:00] adb getScreenDensity 
[2025-08-29T17:07:47.893+08:00] adb getScreenDensity  end
[2025-08-29T17:07:47.893+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:07:47.893+08:00] adb takeScreenshot 
[2025-08-29T17:07:48.067+08:00] adb takeScreenshot  end
[2025-08-29T17:07:48.067+08:00] adb.takeScreenshot completed
[2025-08-29T17:07:48.067+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:07:48.067+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:07:48.068+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:07:48.068+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_e931b9ab-1a2f-4fb8-a9cc-39426c4badd5.png
[2025-08-29T17:07:48.665+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_e931b9ab-1a2f-4fb8-a9cc-39426c4badd5.png end
[2025-08-29T17:07:48.665+08:00] adb.shell screencap completed
[2025-08-29T17:07:48.665+08:00] Pulling screenshot file from device
[2025-08-29T17:07:48.665+08:00] adb pull /data/local/tmp/midscene_screenshot_e931b9ab-1a2f-4fb8-a9cc-39426c4badd5.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\shz25wktth.png
[2025-08-29T17:07:48.738+08:00] adb pull /data/local/tmp/midscene_screenshot_e931b9ab-1a2f-4fb8-a9cc-39426c4badd5.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\shz25wktth.png end
[2025-08-29T17:07:48.738+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\shz25wktth.png
[2025-08-29T17:07:48.739+08:00] adb shell rm /data/local/tmp/midscene_screenshot_e931b9ab-1a2f-4fb8-a9cc-39426c4badd5.png
[2025-08-29T17:07:48.802+08:00] adb shell rm /data/local/tmp/midscene_screenshot_e931b9ab-1a2f-4fb8-a9cc-39426c4badd5.png end
[2025-08-29T17:07:48.802+08:00] Resizing screenshot image
[2025-08-29T17:07:48.876+08:00] Image resize completed
[2025-08-29T17:07:48.876+08:00] Converting to base64
[2025-08-29T17:07:48.876+08:00] screenshotBase64 end
[2025-08-29T17:07:48.932+08:00] screenshotBase64 begin
[2025-08-29T17:07:48.932+08:00] adb shell wm,size
[2025-08-29T17:07:49.015+08:00] adb shell wm,size end
[2025-08-29T17:07:49.015+08:00] Using Physical size: 1080x2436
[2025-08-29T17:07:49.015+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:07:49.091+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:07:49.091+08:00] Failed to get orientation from input, try display
[2025-08-29T17:07:49.091+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:07:49.207+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:07:49.207+08:00] Screen orientation (fallback): 0
[2025-08-29T17:07:49.208+08:00] adb getScreenDensity 
[2025-08-29T17:07:49.302+08:00] adb getScreenDensity  end
[2025-08-29T17:07:49.302+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:07:49.302+08:00] adb takeScreenshot 
[2025-08-29T17:07:49.477+08:00] adb takeScreenshot  end
[2025-08-29T17:07:49.477+08:00] adb.takeScreenshot completed
[2025-08-29T17:07:49.477+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:07:49.477+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:07:49.478+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:07:49.478+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_3accf898-dd3f-449c-b820-0e04dcf6ad1c.png
[2025-08-29T17:07:50.082+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_3accf898-dd3f-449c-b820-0e04dcf6ad1c.png end
[2025-08-29T17:07:50.082+08:00] adb.shell screencap completed
[2025-08-29T17:07:50.082+08:00] Pulling screenshot file from device
[2025-08-29T17:07:50.082+08:00] adb pull /data/local/tmp/midscene_screenshot_3accf898-dd3f-449c-b820-0e04dcf6ad1c.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\p61ihjx9mg.png
[2025-08-29T17:07:50.145+08:00] adb pull /data/local/tmp/midscene_screenshot_3accf898-dd3f-449c-b820-0e04dcf6ad1c.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\p61ihjx9mg.png end
[2025-08-29T17:07:50.145+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\p61ihjx9mg.png
[2025-08-29T17:07:50.146+08:00] adb shell rm /data/local/tmp/midscene_screenshot_3accf898-dd3f-449c-b820-0e04dcf6ad1c.png
[2025-08-29T17:07:50.212+08:00] adb shell rm /data/local/tmp/midscene_screenshot_3accf898-dd3f-449c-b820-0e04dcf6ad1c.png end
[2025-08-29T17:07:50.212+08:00] Resizing screenshot image
[2025-08-29T17:07:50.259+08:00] Image resize completed
[2025-08-29T17:07:50.259+08:00] Converting to base64
[2025-08-29T17:07:50.259+08:00] screenshotBase64 end
[2025-08-29T17:07:50.259+08:00] adb shell wm,size
[2025-08-29T17:07:50.361+08:00] adb shell wm,size end
[2025-08-29T17:07:50.362+08:00] Using Physical size: 1080x2436
[2025-08-29T17:07:50.362+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:07:50.437+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:07:50.437+08:00] Failed to get orientation from input, try display
[2025-08-29T17:07:50.437+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:07:50.520+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:07:50.520+08:00] Screen orientation (fallback): 0
[2025-08-29T17:07:50.520+08:00] adb getScreenDensity 
[2025-08-29T17:07:50.598+08:00] adb getScreenDensity  end
[2025-08-29T17:07:55.173+08:00] screenshotBase64 begin
[2025-08-29T17:07:55.173+08:00] adb shell wm,size
[2025-08-29T17:07:55.253+08:00] adb shell wm,size end
[2025-08-29T17:07:55.253+08:00] Using Physical size: 1080x2436
[2025-08-29T17:07:55.253+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:07:55.349+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:07:55.349+08:00] Failed to get orientation from input, try display
[2025-08-29T17:07:55.349+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:07:55.465+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:07:55.465+08:00] Screen orientation (fallback): 0
[2025-08-29T17:07:55.465+08:00] adb getScreenDensity 
[2025-08-29T17:07:55.546+08:00] adb getScreenDensity  end
[2025-08-29T17:07:55.546+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:07:55.546+08:00] adb takeScreenshot 
[2025-08-29T17:07:55.718+08:00] adb takeScreenshot  end
[2025-08-29T17:07:55.718+08:00] adb.takeScreenshot completed
[2025-08-29T17:07:55.718+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:07:55.718+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:07:55.718+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:07:55.718+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_762ca86d-8b51-44a5-b301-bf3db98cd413.png
[2025-08-29T17:07:56.244+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_762ca86d-8b51-44a5-b301-bf3db98cd413.png end
[2025-08-29T17:07:56.244+08:00] adb.shell screencap completed
[2025-08-29T17:07:56.244+08:00] Pulling screenshot file from device
[2025-08-29T17:07:56.244+08:00] adb pull /data/local/tmp/midscene_screenshot_762ca86d-8b51-44a5-b301-bf3db98cd413.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ml1375lcaj9.png
[2025-08-29T17:07:56.291+08:00] adb pull /data/local/tmp/midscene_screenshot_762ca86d-8b51-44a5-b301-bf3db98cd413.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ml1375lcaj9.png end
[2025-08-29T17:07:56.291+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ml1375lcaj9.png
[2025-08-29T17:07:56.292+08:00] adb shell rm /data/local/tmp/midscene_screenshot_762ca86d-8b51-44a5-b301-bf3db98cd413.png
[2025-08-29T17:07:56.358+08:00] adb shell rm /data/local/tmp/midscene_screenshot_762ca86d-8b51-44a5-b301-bf3db98cd413.png end
[2025-08-29T17:07:56.358+08:00] Resizing screenshot image
[2025-08-29T17:07:56.406+08:00] Image resize completed
[2025-08-29T17:07:56.406+08:00] Converting to base64
[2025-08-29T17:07:56.406+08:00] screenshotBase64 end
[2025-08-29T17:07:56.456+08:00] screenshotBase64 begin
[2025-08-29T17:07:56.456+08:00] adb shell wm,size
[2025-08-29T17:07:56.536+08:00] adb shell wm,size end
[2025-08-29T17:07:56.536+08:00] Using Physical size: 1080x2436
[2025-08-29T17:07:56.536+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:07:56.633+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:07:56.633+08:00] Failed to get orientation from input, try display
[2025-08-29T17:07:56.633+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:07:56.756+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:07:56.756+08:00] Screen orientation (fallback): 0
[2025-08-29T17:07:56.756+08:00] adb getScreenDensity 
[2025-08-29T17:07:56.850+08:00] adb getScreenDensity  end
[2025-08-29T17:07:56.850+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:07:56.850+08:00] adb takeScreenshot 
[2025-08-29T17:07:57.054+08:00] adb takeScreenshot  end
[2025-08-29T17:07:57.054+08:00] adb.takeScreenshot completed
[2025-08-29T17:07:57.054+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:07:57.054+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:07:57.055+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:07:57.055+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_692ba09f-13f6-41f3-af1d-c73ac7c07da8.png
[2025-08-29T17:07:57.611+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_692ba09f-13f6-41f3-af1d-c73ac7c07da8.png end
[2025-08-29T17:07:57.611+08:00] adb.shell screencap completed
[2025-08-29T17:07:57.611+08:00] Pulling screenshot file from device
[2025-08-29T17:07:57.611+08:00] adb pull /data/local/tmp/midscene_screenshot_692ba09f-13f6-41f3-af1d-c73ac7c07da8.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ngu5nwwix8s.png
[2025-08-29T17:07:57.663+08:00] adb pull /data/local/tmp/midscene_screenshot_692ba09f-13f6-41f3-af1d-c73ac7c07da8.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ngu5nwwix8s.png end
[2025-08-29T17:07:57.663+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ngu5nwwix8s.png
[2025-08-29T17:07:57.664+08:00] adb shell rm /data/local/tmp/midscene_screenshot_692ba09f-13f6-41f3-af1d-c73ac7c07da8.png
[2025-08-29T17:07:57.722+08:00] adb shell rm /data/local/tmp/midscene_screenshot_692ba09f-13f6-41f3-af1d-c73ac7c07da8.png end
[2025-08-29T17:07:57.722+08:00] Resizing screenshot image
[2025-08-29T17:07:57.810+08:00] Image resize completed
[2025-08-29T17:07:57.810+08:00] Converting to base64
[2025-08-29T17:07:57.810+08:00] screenshotBase64 end
[2025-08-29T17:07:57.810+08:00] adb shell wm,size
[2025-08-29T17:07:57.894+08:00] adb shell wm,size end
[2025-08-29T17:07:57.894+08:00] Using Physical size: 1080x2436
[2025-08-29T17:07:57.894+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:07:58.018+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:07:58.018+08:00] Failed to get orientation from input, try display
[2025-08-29T17:07:58.018+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:07:58.117+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:07:58.117+08:00] Screen orientation (fallback): 0
[2025-08-29T17:07:58.117+08:00] adb getScreenDensity 
[2025-08-29T17:07:58.215+08:00] adb getScreenDensity  end
[2025-08-29T17:07:58.217+08:00] adb keyevent 66
[2025-08-29T17:07:58.318+08:00] adb keyevent 66 end
[2025-08-29T17:07:58.526+08:00] screenshotBase64 begin
[2025-08-29T17:07:58.526+08:00] adb shell wm,size
[2025-08-29T17:07:58.615+08:00] adb shell wm,size end
[2025-08-29T17:07:58.615+08:00] Using Physical size: 1080x2436
[2025-08-29T17:07:58.615+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:07:58.710+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:07:58.710+08:00] Failed to get orientation from input, try display
[2025-08-29T17:07:58.710+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:07:58.858+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:07:58.859+08:00] Screen orientation (fallback): 0
[2025-08-29T17:07:58.859+08:00] adb getScreenDensity 
[2025-08-29T17:07:58.950+08:00] adb getScreenDensity  end
[2025-08-29T17:07:58.950+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:07:58.950+08:00] adb takeScreenshot 
[2025-08-29T17:07:59.117+08:00] adb takeScreenshot  end
[2025-08-29T17:07:59.117+08:00] adb.takeScreenshot completed
[2025-08-29T17:07:59.117+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:07:59.117+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:07:59.118+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:07:59.118+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_f7e4198e-5ec3-4089-9392-1fea6b8b7729.png
[2025-08-29T17:07:59.679+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_f7e4198e-5ec3-4089-9392-1fea6b8b7729.png end
[2025-08-29T17:07:59.679+08:00] adb.shell screencap completed
[2025-08-29T17:07:59.679+08:00] Pulling screenshot file from device
[2025-08-29T17:07:59.679+08:00] adb pull /data/local/tmp/midscene_screenshot_f7e4198e-5ec3-4089-9392-1fea6b8b7729.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lgqmnqxx51.png
[2025-08-29T17:07:59.736+08:00] adb pull /data/local/tmp/midscene_screenshot_f7e4198e-5ec3-4089-9392-1fea6b8b7729.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lgqmnqxx51.png end
[2025-08-29T17:07:59.736+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lgqmnqxx51.png
[2025-08-29T17:07:59.737+08:00] adb shell rm /data/local/tmp/midscene_screenshot_f7e4198e-5ec3-4089-9392-1fea6b8b7729.png
[2025-08-29T17:07:59.824+08:00] adb shell rm /data/local/tmp/midscene_screenshot_f7e4198e-5ec3-4089-9392-1fea6b8b7729.png end
[2025-08-29T17:07:59.824+08:00] Resizing screenshot image
[2025-08-29T17:07:59.874+08:00] Image resize completed
[2025-08-29T17:07:59.874+08:00] Converting to base64
[2025-08-29T17:07:59.875+08:00] screenshotBase64 end
[2025-08-29T17:07:59.908+08:00] screenshotBase64 begin
[2025-08-29T17:07:59.908+08:00] adb shell wm,size
[2025-08-29T17:07:59.991+08:00] adb shell wm,size end
[2025-08-29T17:07:59.991+08:00] Using Physical size: 1080x2436
[2025-08-29T17:07:59.991+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:08:00.074+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:08:00.074+08:00] Failed to get orientation from input, try display
[2025-08-29T17:08:00.074+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:08:00.197+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:08:00.197+08:00] Screen orientation (fallback): 0
[2025-08-29T17:08:00.197+08:00] adb getScreenDensity 
[2025-08-29T17:08:00.285+08:00] adb getScreenDensity  end
[2025-08-29T17:08:00.285+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:08:00.285+08:00] adb takeScreenshot 
[2025-08-29T17:08:00.467+08:00] adb takeScreenshot  end
[2025-08-29T17:08:00.467+08:00] adb.takeScreenshot completed
[2025-08-29T17:08:00.467+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:08:00.467+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:08:00.467+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:08:00.468+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_2b3896ba-8586-4bf7-8868-7e14bccb664f.png
[2025-08-29T17:08:00.997+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_2b3896ba-8586-4bf7-8868-7e14bccb664f.png end
[2025-08-29T17:08:00.998+08:00] adb.shell screencap completed
[2025-08-29T17:08:00.998+08:00] Pulling screenshot file from device
[2025-08-29T17:08:00.998+08:00] adb pull /data/local/tmp/midscene_screenshot_2b3896ba-8586-4bf7-8868-7e14bccb664f.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\se5qpo49kg.png
[2025-08-29T17:08:01.052+08:00] adb pull /data/local/tmp/midscene_screenshot_2b3896ba-8586-4bf7-8868-7e14bccb664f.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\se5qpo49kg.png end
[2025-08-29T17:08:01.052+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\se5qpo49kg.png
[2025-08-29T17:08:01.053+08:00] adb shell rm /data/local/tmp/midscene_screenshot_2b3896ba-8586-4bf7-8868-7e14bccb664f.png
[2025-08-29T17:08:01.116+08:00] adb shell rm /data/local/tmp/midscene_screenshot_2b3896ba-8586-4bf7-8868-7e14bccb664f.png end
[2025-08-29T17:08:01.116+08:00] Resizing screenshot image
[2025-08-29T17:08:01.165+08:00] Image resize completed
[2025-08-29T17:08:01.165+08:00] Converting to base64
[2025-08-29T17:08:01.165+08:00] screenshotBase64 end
[2025-08-29T17:08:01.217+08:00] screenshotBase64 begin
[2025-08-29T17:08:01.217+08:00] adb shell wm,size
[2025-08-29T17:08:01.308+08:00] adb shell wm,size end
[2025-08-29T17:08:01.308+08:00] Using Physical size: 1080x2436
[2025-08-29T17:08:01.308+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:08:01.408+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:08:01.408+08:00] Failed to get orientation from input, try display
[2025-08-29T17:08:01.408+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:08:01.531+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:08:01.531+08:00] Screen orientation (fallback): 0
[2025-08-29T17:08:01.531+08:00] adb getScreenDensity 
[2025-08-29T17:08:01.615+08:00] adb getScreenDensity  end
[2025-08-29T17:08:01.615+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:08:01.615+08:00] adb takeScreenshot 
[2025-08-29T17:08:01.785+08:00] adb takeScreenshot  end
[2025-08-29T17:08:01.785+08:00] adb.takeScreenshot completed
[2025-08-29T17:08:01.785+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:08:01.785+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:08:01.785+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:08:01.785+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_8885f95a-26df-4e0f-9607-f70f8be7c8f4.png
[2025-08-29T17:08:02.308+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_8885f95a-26df-4e0f-9607-f70f8be7c8f4.png end
[2025-08-29T17:08:02.308+08:00] adb.shell screencap completed
[2025-08-29T17:08:02.308+08:00] Pulling screenshot file from device
[2025-08-29T17:08:02.308+08:00] adb pull /data/local/tmp/midscene_screenshot_8885f95a-26df-4e0f-9607-f70f8be7c8f4.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\mmuyoy563ep.png
[2025-08-29T17:08:02.364+08:00] adb pull /data/local/tmp/midscene_screenshot_8885f95a-26df-4e0f-9607-f70f8be7c8f4.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\mmuyoy563ep.png end
[2025-08-29T17:08:02.364+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\mmuyoy563ep.png
[2025-08-29T17:08:02.364+08:00] adb shell rm /data/local/tmp/midscene_screenshot_8885f95a-26df-4e0f-9607-f70f8be7c8f4.png
[2025-08-29T17:08:02.423+08:00] adb shell rm /data/local/tmp/midscene_screenshot_8885f95a-26df-4e0f-9607-f70f8be7c8f4.png end
[2025-08-29T17:08:02.423+08:00] Resizing screenshot image
[2025-08-29T17:08:02.473+08:00] Image resize completed
[2025-08-29T17:08:02.473+08:00] Converting to base64
[2025-08-29T17:08:02.473+08:00] screenshotBase64 end
[2025-08-29T17:08:02.473+08:00] adb shell wm,size
[2025-08-29T17:08:02.557+08:00] adb shell wm,size end
[2025-08-29T17:08:02.557+08:00] Using Physical size: 1080x2436
[2025-08-29T17:08:02.557+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:08:02.645+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:08:02.645+08:00] Failed to get orientation from input, try display
[2025-08-29T17:08:02.645+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:08:02.732+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:08:02.733+08:00] Screen orientation (fallback): 0
[2025-08-29T17:08:02.733+08:00] adb getScreenDensity 
[2025-08-29T17:08:02.829+08:00] adb getScreenDensity  end
[2025-08-29T17:08:02.888+08:00] screenshotBase64 begin
[2025-08-29T17:08:02.888+08:00] adb shell wm,size
[2025-08-29T17:08:02.993+08:00] adb shell wm,size end
[2025-08-29T17:08:02.993+08:00] Using Physical size: 1080x2436
[2025-08-29T17:08:02.993+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:08:03.094+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:08:03.094+08:00] Failed to get orientation from input, try display
[2025-08-29T17:08:03.094+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:08:03.232+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:08:03.232+08:00] Screen orientation (fallback): 0
[2025-08-29T17:08:03.232+08:00] adb getScreenDensity 
[2025-08-29T17:08:03.313+08:00] adb getScreenDensity  end
[2025-08-29T17:08:03.313+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:08:03.313+08:00] adb takeScreenshot 
[2025-08-29T17:08:03.488+08:00] adb takeScreenshot  end
[2025-08-29T17:08:03.488+08:00] adb.takeScreenshot completed
[2025-08-29T17:08:03.488+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:08:03.488+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:08:03.488+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:08:03.488+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_ade0ca8a-469a-4c99-a42d-b9eabc4c5be1.png
[2025-08-29T17:08:04.006+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_ade0ca8a-469a-4c99-a42d-b9eabc4c5be1.png end
[2025-08-29T17:08:04.006+08:00] adb.shell screencap completed
[2025-08-29T17:08:04.006+08:00] Pulling screenshot file from device
[2025-08-29T17:08:04.006+08:00] adb pull /data/local/tmp/midscene_screenshot_ade0ca8a-469a-4c99-a42d-b9eabc4c5be1.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\u22k1hjazwa.png
[2025-08-29T17:08:04.053+08:00] adb pull /data/local/tmp/midscene_screenshot_ade0ca8a-469a-4c99-a42d-b9eabc4c5be1.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\u22k1hjazwa.png end
[2025-08-29T17:08:04.053+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\u22k1hjazwa.png
[2025-08-29T17:08:04.053+08:00] adb shell rm /data/local/tmp/midscene_screenshot_ade0ca8a-469a-4c99-a42d-b9eabc4c5be1.png
[2025-08-29T17:08:04.113+08:00] adb shell rm /data/local/tmp/midscene_screenshot_ade0ca8a-469a-4c99-a42d-b9eabc4c5be1.png end
[2025-08-29T17:08:04.113+08:00] Resizing screenshot image
[2025-08-29T17:08:04.157+08:00] Image resize completed
[2025-08-29T17:08:04.157+08:00] Converting to base64
[2025-08-29T17:08:04.157+08:00] screenshotBase64 end
[2025-08-29T17:08:04.157+08:00] adb shell wm,size
[2025-08-29T17:08:04.251+08:00] adb shell wm,size end
[2025-08-29T17:08:04.251+08:00] Using Physical size: 1080x2436
[2025-08-29T17:08:04.251+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:08:04.351+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:08:04.351+08:00] Failed to get orientation from input, try display
[2025-08-29T17:08:04.351+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:08:04.471+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:08:04.471+08:00] Screen orientation (fallback): 0
[2025-08-29T17:08:04.471+08:00] adb getScreenDensity 
[2025-08-29T17:08:04.563+08:00] adb getScreenDensity  end
[2025-08-29T17:08:08.749+08:00] screenshotBase64 begin
[2025-08-29T17:08:08.749+08:00] adb shell wm,size
[2025-08-29T17:08:08.851+08:00] adb shell wm,size end
[2025-08-29T17:08:08.851+08:00] Using Physical size: 1080x2436
[2025-08-29T17:08:08.851+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:08:08.953+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:08:08.953+08:00] Failed to get orientation from input, try display
[2025-08-29T17:08:08.953+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:08:09.067+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:08:09.067+08:00] Screen orientation (fallback): 0
[2025-08-29T17:08:09.067+08:00] adb getScreenDensity 
[2025-08-29T17:08:09.163+08:00] adb getScreenDensity  end
[2025-08-29T17:08:09.163+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:08:09.163+08:00] adb takeScreenshot 
[2025-08-29T17:08:09.364+08:00] adb takeScreenshot  end
[2025-08-29T17:08:09.364+08:00] adb.takeScreenshot completed
[2025-08-29T17:08:09.364+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:08:09.364+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:08:09.364+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:08:09.364+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_9050d57d-019e-471a-a9be-b65c682a6d02.png
[2025-08-29T17:08:09.889+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_9050d57d-019e-471a-a9be-b65c682a6d02.png end
[2025-08-29T17:08:09.889+08:00] adb.shell screencap completed
[2025-08-29T17:08:09.889+08:00] Pulling screenshot file from device
[2025-08-29T17:08:09.889+08:00] adb pull /data/local/tmp/midscene_screenshot_9050d57d-019e-471a-a9be-b65c682a6d02.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lmkll8fgx1p.png
[2025-08-29T17:08:09.941+08:00] adb pull /data/local/tmp/midscene_screenshot_9050d57d-019e-471a-a9be-b65c682a6d02.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lmkll8fgx1p.png end
[2025-08-29T17:08:09.941+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lmkll8fgx1p.png
[2025-08-29T17:08:09.942+08:00] adb shell rm /data/local/tmp/midscene_screenshot_9050d57d-019e-471a-a9be-b65c682a6d02.png
[2025-08-29T17:08:09.999+08:00] adb shell rm /data/local/tmp/midscene_screenshot_9050d57d-019e-471a-a9be-b65c682a6d02.png end
[2025-08-29T17:08:09.999+08:00] Resizing screenshot image
[2025-08-29T17:08:10.042+08:00] Image resize completed
[2025-08-29T17:08:10.042+08:00] Converting to base64
[2025-08-29T17:08:10.042+08:00] screenshotBase64 end
[2025-08-29T17:08:10.101+08:00] screenshotBase64 begin
[2025-08-29T17:08:10.101+08:00] adb shell wm,size
[2025-08-29T17:08:10.195+08:00] adb shell wm,size end
[2025-08-29T17:08:10.195+08:00] Using Physical size: 1080x2436
[2025-08-29T17:08:10.195+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:08:10.291+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:08:10.291+08:00] Failed to get orientation from input, try display
[2025-08-29T17:08:10.292+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:08:10.419+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:08:10.419+08:00] Screen orientation (fallback): 0
[2025-08-29T17:08:10.419+08:00] adb getScreenDensity 
[2025-08-29T17:08:10.509+08:00] adb getScreenDensity  end
[2025-08-29T17:08:10.510+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:08:10.510+08:00] adb takeScreenshot 
[2025-08-29T17:08:10.685+08:00] adb takeScreenshot  end
[2025-08-29T17:08:10.685+08:00] adb.takeScreenshot completed
[2025-08-29T17:08:10.685+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:08:10.685+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:08:10.686+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:08:10.686+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_d0ce488b-5494-4ae4-a045-4de86c9a4d68.png
[2025-08-29T17:08:11.213+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_d0ce488b-5494-4ae4-a045-4de86c9a4d68.png end
[2025-08-29T17:08:11.213+08:00] adb.shell screencap completed
[2025-08-29T17:08:11.213+08:00] Pulling screenshot file from device
[2025-08-29T17:08:11.213+08:00] adb pull /data/local/tmp/midscene_screenshot_d0ce488b-5494-4ae4-a045-4de86c9a4d68.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\cb5u7ob9crl.png
[2025-08-29T17:08:11.263+08:00] adb pull /data/local/tmp/midscene_screenshot_d0ce488b-5494-4ae4-a045-4de86c9a4d68.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\cb5u7ob9crl.png end
[2025-08-29T17:08:11.263+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\cb5u7ob9crl.png
[2025-08-29T17:08:11.263+08:00] adb shell rm /data/local/tmp/midscene_screenshot_d0ce488b-5494-4ae4-a045-4de86c9a4d68.png
[2025-08-29T17:08:11.347+08:00] adb shell rm /data/local/tmp/midscene_screenshot_d0ce488b-5494-4ae4-a045-4de86c9a4d68.png end
[2025-08-29T17:08:11.347+08:00] Resizing screenshot image
[2025-08-29T17:08:11.391+08:00] Image resize completed
[2025-08-29T17:08:11.391+08:00] Converting to base64
[2025-08-29T17:08:11.391+08:00] screenshotBase64 end
[2025-08-29T17:08:11.391+08:00] adb shell wm,size
[2025-08-29T17:08:11.482+08:00] adb shell wm,size end
[2025-08-29T17:08:11.482+08:00] Using Physical size: 1080x2436
[2025-08-29T17:08:11.482+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:08:11.569+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:08:11.570+08:00] Failed to get orientation from input, try display
[2025-08-29T17:08:11.570+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:08:11.691+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:08:11.691+08:00] Screen orientation (fallback): 0
[2025-08-29T17:08:11.691+08:00] adb getScreenDensity 
[2025-08-29T17:08:11.773+08:00] adb getScreenDensity  end
[2025-08-29T17:08:11.834+08:00] screenshotBase64 begin
[2025-08-29T17:08:11.834+08:00] adb shell wm,size
[2025-08-29T17:08:11.928+08:00] adb shell wm,size end
[2025-08-29T17:08:11.928+08:00] Using Physical size: 1080x2436
[2025-08-29T17:08:11.928+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:08:12.013+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:08:12.013+08:00] Failed to get orientation from input, try display
[2025-08-29T17:08:12.013+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:08:12.123+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:08:12.124+08:00] Screen orientation (fallback): 0
[2025-08-29T17:08:12.124+08:00] adb getScreenDensity 
[2025-08-29T17:08:12.216+08:00] adb getScreenDensity  end
[2025-08-29T17:08:12.216+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:08:12.216+08:00] adb takeScreenshot 
[2025-08-29T17:08:12.403+08:00] adb takeScreenshot  end
[2025-08-29T17:08:12.403+08:00] adb.takeScreenshot completed
[2025-08-29T17:08:12.403+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:08:12.403+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:08:12.403+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:08:12.403+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_309eb2f9-1d1b-4b3d-a834-b5c6ce840cfc.png
[2025-08-29T17:08:12.895+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_309eb2f9-1d1b-4b3d-a834-b5c6ce840cfc.png end
[2025-08-29T17:08:12.895+08:00] adb.shell screencap completed
[2025-08-29T17:08:12.895+08:00] Pulling screenshot file from device
[2025-08-29T17:08:12.895+08:00] adb pull /data/local/tmp/midscene_screenshot_309eb2f9-1d1b-4b3d-a834-b5c6ce840cfc.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\v18gb40558.png
[2025-08-29T17:08:12.944+08:00] adb pull /data/local/tmp/midscene_screenshot_309eb2f9-1d1b-4b3d-a834-b5c6ce840cfc.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\v18gb40558.png end
[2025-08-29T17:08:12.945+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\v18gb40558.png
[2025-08-29T17:08:12.945+08:00] adb shell rm /data/local/tmp/midscene_screenshot_309eb2f9-1d1b-4b3d-a834-b5c6ce840cfc.png
[2025-08-29T17:08:13.007+08:00] adb shell rm /data/local/tmp/midscene_screenshot_309eb2f9-1d1b-4b3d-a834-b5c6ce840cfc.png end
[2025-08-29T17:08:13.007+08:00] Resizing screenshot image
[2025-08-29T17:08:13.061+08:00] Image resize completed
[2025-08-29T17:08:13.061+08:00] Converting to base64
[2025-08-29T17:08:13.061+08:00] screenshotBase64 end
[2025-08-29T17:08:13.061+08:00] adb shell wm,size
[2025-08-29T17:08:13.132+08:00] adb shell wm,size end
[2025-08-29T17:08:13.132+08:00] Using Physical size: 1080x2436
[2025-08-29T17:08:13.132+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:08:13.224+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:08:13.224+08:00] Failed to get orientation from input, try display
[2025-08-29T17:08:13.224+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:08:13.324+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:08:13.324+08:00] Screen orientation (fallback): 0
[2025-08-29T17:08:13.324+08:00] adb getScreenDensity 
[2025-08-29T17:08:13.428+08:00] adb getScreenDensity  end
[2025-08-29T17:08:17.070+08:00] screenshotBase64 begin
[2025-08-29T17:08:17.071+08:00] adb shell wm,size
[2025-08-29T17:08:17.152+08:00] adb shell wm,size end
[2025-08-29T17:08:17.153+08:00] Using Physical size: 1080x2436
[2025-08-29T17:08:17.153+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:08:17.286+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:08:17.286+08:00] Failed to get orientation from input, try display
[2025-08-29T17:08:17.286+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:08:17.430+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:08:17.431+08:00] Screen orientation (fallback): 0
[2025-08-29T17:08:17.431+08:00] adb getScreenDensity 
[2025-08-29T17:08:17.535+08:00] adb getScreenDensity  end
[2025-08-29T17:08:17.535+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:08:17.535+08:00] adb takeScreenshot 
[2025-08-29T17:08:17.746+08:00] adb takeScreenshot  end
[2025-08-29T17:08:17.746+08:00] adb.takeScreenshot completed
[2025-08-29T17:08:17.746+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:08:17.746+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:08:17.746+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:08:17.746+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_d617c5da-318e-4637-aed3-03623cf3892f.png
[2025-08-29T17:08:18.263+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_d617c5da-318e-4637-aed3-03623cf3892f.png end
[2025-08-29T17:08:18.263+08:00] adb.shell screencap completed
[2025-08-29T17:08:18.263+08:00] Pulling screenshot file from device
[2025-08-29T17:08:18.263+08:00] adb pull /data/local/tmp/midscene_screenshot_d617c5da-318e-4637-aed3-03623cf3892f.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\1wlzwyihxyh.png
[2025-08-29T17:08:18.333+08:00] adb pull /data/local/tmp/midscene_screenshot_d617c5da-318e-4637-aed3-03623cf3892f.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\1wlzwyihxyh.png end
[2025-08-29T17:08:18.333+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\1wlzwyihxyh.png
[2025-08-29T17:08:18.333+08:00] adb shell rm /data/local/tmp/midscene_screenshot_d617c5da-318e-4637-aed3-03623cf3892f.png
[2025-08-29T17:08:18.394+08:00] adb shell rm /data/local/tmp/midscene_screenshot_d617c5da-318e-4637-aed3-03623cf3892f.png end
[2025-08-29T17:08:18.394+08:00] Resizing screenshot image
[2025-08-29T17:08:18.436+08:00] Image resize completed
[2025-08-29T17:08:18.436+08:00] Converting to base64
[2025-08-29T17:08:18.436+08:00] screenshotBase64 end
[2025-08-29T17:08:18.436+08:00] screenshotBase64 begin
[2025-08-29T17:08:18.436+08:00] adb shell wm,size
[2025-08-29T17:08:18.519+08:00] adb shell wm,size end
[2025-08-29T17:08:18.519+08:00] Using Physical size: 1080x2436
[2025-08-29T17:08:18.519+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:08:18.627+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:08:18.627+08:00] Failed to get orientation from input, try display
[2025-08-29T17:08:18.627+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:08:18.750+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:08:18.750+08:00] Screen orientation (fallback): 0
[2025-08-29T17:08:18.750+08:00] adb getScreenDensity 
[2025-08-29T17:08:18.857+08:00] adb getScreenDensity  end
[2025-08-29T17:08:18.857+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:08:18.857+08:00] adb takeScreenshot 
[2025-08-29T17:08:19.039+08:00] adb takeScreenshot  end
[2025-08-29T17:08:19.039+08:00] adb.takeScreenshot completed
[2025-08-29T17:08:19.039+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:08:19.040+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:08:19.040+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:08:19.040+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_dea245b3-6157-4e8f-9da5-dd97239d4519.png
[2025-08-29T17:08:19.594+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_dea245b3-6157-4e8f-9da5-dd97239d4519.png end
[2025-08-29T17:08:19.594+08:00] adb.shell screencap completed
[2025-08-29T17:08:19.594+08:00] Pulling screenshot file from device
[2025-08-29T17:08:19.594+08:00] adb pull /data/local/tmp/midscene_screenshot_dea245b3-6157-4e8f-9da5-dd97239d4519.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\k5wfbujmea.png
[2025-08-29T17:08:19.648+08:00] adb pull /data/local/tmp/midscene_screenshot_dea245b3-6157-4e8f-9da5-dd97239d4519.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\k5wfbujmea.png end
[2025-08-29T17:08:19.648+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\k5wfbujmea.png
[2025-08-29T17:08:19.648+08:00] adb shell rm /data/local/tmp/midscene_screenshot_dea245b3-6157-4e8f-9da5-dd97239d4519.png
[2025-08-29T17:08:19.720+08:00] adb shell rm /data/local/tmp/midscene_screenshot_dea245b3-6157-4e8f-9da5-dd97239d4519.png end
[2025-08-29T17:08:19.720+08:00] Resizing screenshot image
[2025-08-29T17:08:19.773+08:00] Image resize completed
[2025-08-29T17:14:43.174+08:00] Initializing ADB with device ID: ASALE3741B000022
[2025-08-29T17:14:43.175+08:00] adb shell wm,size
[2025-08-29T17:14:43.277+08:00] adb shell wm,size end
[2025-08-29T17:14:43.278+08:00] Using Physical size: 1080x2436
[2025-08-29T17:14:43.278+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:14:43.371+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:14:43.371+08:00] Failed to get orientation from input, try display
[2025-08-29T17:14:43.371+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:14:43.505+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:14:43.505+08:00] Screen orientation (fallback): 0
[2025-08-29T17:14:43.505+08:00] ADB initialized successfully 
DeviceId: ASALE3741B000022
ScreenSize:
  physical size: 1080x2436

[2025-08-29T17:14:43.505+08:00] Launching app: com.transsion.aivoiceassistant
[2025-08-29T17:14:43.505+08:00] adb activateApp com.transsion.aivoiceassistant
[2025-08-29T17:14:43.773+08:00] adb activateApp com.transsion.aivoiceassistant end
[2025-08-29T17:14:43.773+08:00] Successfully launched: com.transsion.aivoiceassistant
[2025-08-29T17:14:48.855+08:00] screenshotBase64 begin
[2025-08-29T17:14:48.855+08:00] adb shell wm,size
[2025-08-29T17:14:48.952+08:00] adb shell wm,size end
[2025-08-29T17:14:48.952+08:00] Using Physical size: 1080x2436
[2025-08-29T17:14:48.952+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:14:49.050+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:14:49.050+08:00] Failed to get orientation from input, try display
[2025-08-29T17:14:49.050+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:14:49.168+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:14:49.168+08:00] Screen orientation (fallback): 0
[2025-08-29T17:14:49.168+08:00] adb getScreenDensity 
[2025-08-29T17:14:49.273+08:00] adb getScreenDensity  end
[2025-08-29T17:14:49.276+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:14:49.276+08:00] adb takeScreenshot 
[2025-08-29T17:14:49.455+08:00] adb takeScreenshot  end
[2025-08-29T17:14:49.455+08:00] adb.takeScreenshot completed
[2025-08-29T17:14:49.455+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:14:49.455+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:14:49.456+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:14:49.456+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_17ce28fb-ca2c-48cf-b790-b31a224e593b.png
[2025-08-29T17:14:51.816+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_17ce28fb-ca2c-48cf-b790-b31a224e593b.png end
[2025-08-29T17:14:51.816+08:00] adb.shell screencap completed
[2025-08-29T17:14:51.816+08:00] Pulling screenshot file from device
[2025-08-29T17:14:51.816+08:00] adb pull /data/local/tmp/midscene_screenshot_17ce28fb-ca2c-48cf-b790-b31a224e593b.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\iyer3w6cfj.png
[2025-08-29T17:14:51.950+08:00] adb pull /data/local/tmp/midscene_screenshot_17ce28fb-ca2c-48cf-b790-b31a224e593b.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\iyer3w6cfj.png end
[2025-08-29T17:14:51.950+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\iyer3w6cfj.png
[2025-08-29T17:14:51.953+08:00] adb shell rm /data/local/tmp/midscene_screenshot_17ce28fb-ca2c-48cf-b790-b31a224e593b.png
[2025-08-29T17:14:52.013+08:00] adb shell rm /data/local/tmp/midscene_screenshot_17ce28fb-ca2c-48cf-b790-b31a224e593b.png end
[2025-08-29T17:14:52.013+08:00] Resizing screenshot image
[2025-08-29T17:14:52.186+08:00] Image resize completed
[2025-08-29T17:14:52.186+08:00] Converting to base64
[2025-08-29T17:14:52.186+08:00] screenshotBase64 end
[2025-08-29T17:14:52.186+08:00] adb shell wm,size
[2025-08-29T17:14:52.271+08:00] adb shell wm,size end
[2025-08-29T17:14:52.272+08:00] Using Physical size: 1080x2436
[2025-08-29T17:14:52.272+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:14:52.356+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:14:52.356+08:00] Failed to get orientation from input, try display
[2025-08-29T17:14:52.356+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:14:52.449+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:14:52.449+08:00] Screen orientation (fallback): 0
[2025-08-29T17:14:52.449+08:00] adb getScreenDensity 
[2025-08-29T17:14:52.535+08:00] adb getScreenDensity  end
[2025-08-29T17:14:58.336+08:00] screenshotBase64 begin
[2025-08-29T17:14:58.336+08:00] adb shell wm,size
[2025-08-29T17:14:58.440+08:00] adb shell wm,size end
[2025-08-29T17:14:58.440+08:00] Using Physical size: 1080x2436
[2025-08-29T17:14:58.440+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:14:58.519+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:14:58.519+08:00] Failed to get orientation from input, try display
[2025-08-29T17:14:58.519+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:14:58.609+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:14:58.609+08:00] Screen orientation (fallback): 0
[2025-08-29T17:14:58.609+08:00] adb getScreenDensity 
[2025-08-29T17:14:58.698+08:00] adb getScreenDensity  end
[2025-08-29T17:14:58.698+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:14:58.698+08:00] adb takeScreenshot 
[2025-08-29T17:14:58.907+08:00] adb takeScreenshot  end
[2025-08-29T17:14:58.907+08:00] adb.takeScreenshot completed
[2025-08-29T17:14:58.907+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:14:58.908+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:14:58.908+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:14:58.908+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_ffd236fc-06b6-460e-80c2-1780459c5106.png
[2025-08-29T17:14:59.620+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_ffd236fc-06b6-460e-80c2-1780459c5106.png end
[2025-08-29T17:14:59.620+08:00] adb.shell screencap completed
[2025-08-29T17:14:59.620+08:00] Pulling screenshot file from device
[2025-08-29T17:14:59.620+08:00] adb pull /data/local/tmp/midscene_screenshot_ffd236fc-06b6-460e-80c2-1780459c5106.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\jdha9jmwhr.png
[2025-08-29T17:14:59.662+08:00] adb pull /data/local/tmp/midscene_screenshot_ffd236fc-06b6-460e-80c2-1780459c5106.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\jdha9jmwhr.png end
[2025-08-29T17:14:59.662+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\jdha9jmwhr.png
[2025-08-29T17:14:59.662+08:00] adb shell rm /data/local/tmp/midscene_screenshot_ffd236fc-06b6-460e-80c2-1780459c5106.png
[2025-08-29T17:14:59.717+08:00] adb shell rm /data/local/tmp/midscene_screenshot_ffd236fc-06b6-460e-80c2-1780459c5106.png end
[2025-08-29T17:14:59.717+08:00] Resizing screenshot image
[2025-08-29T17:14:59.764+08:00] Image resize completed
[2025-08-29T17:14:59.764+08:00] Converting to base64
[2025-08-29T17:14:59.764+08:00] screenshotBase64 end
[2025-08-29T17:14:59.764+08:00] adb shell wm,size
[2025-08-29T17:14:59.861+08:00] adb shell wm,size end
[2025-08-29T17:14:59.861+08:00] Using Physical size: 1080x2436
[2025-08-29T17:14:59.861+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:14:59.939+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:14:59.939+08:00] Failed to get orientation from input, try display
[2025-08-29T17:14:59.939+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:00.063+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:00.063+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:00.063+08:00] adb getScreenDensity 
[2025-08-29T17:15:00.145+08:00] adb getScreenDensity  end
[2025-08-29T17:15:00.148+08:00] screenshotBase64 begin
[2025-08-29T17:15:00.148+08:00] adb shell wm,size
[2025-08-29T17:15:00.220+08:00] adb shell wm,size end
[2025-08-29T17:15:00.220+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:00.220+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:00.297+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:00.297+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:00.297+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:00.410+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:00.410+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:00.410+08:00] adb getScreenDensity 
[2025-08-29T17:15:00.514+08:00] adb getScreenDensity  end
[2025-08-29T17:15:00.514+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:15:00.514+08:00] adb takeScreenshot 
[2025-08-29T17:15:00.699+08:00] adb takeScreenshot  end
[2025-08-29T17:15:00.699+08:00] adb.takeScreenshot completed
[2025-08-29T17:15:00.699+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:15:00.699+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:15:00.699+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:15:00.699+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_5af7ec09-857f-4a4c-ac48-4ade91d894ee.png
[2025-08-29T17:15:02.841+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_5af7ec09-857f-4a4c-ac48-4ade91d894ee.png end
[2025-08-29T17:15:02.841+08:00] adb.shell screencap completed
[2025-08-29T17:15:02.841+08:00] Pulling screenshot file from device
[2025-08-29T17:15:02.841+08:00] adb pull /data/local/tmp/midscene_screenshot_5af7ec09-857f-4a4c-ac48-4ade91d894ee.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\osx5cuz4h6.png
[2025-08-29T17:15:02.943+08:00] adb pull /data/local/tmp/midscene_screenshot_5af7ec09-857f-4a4c-ac48-4ade91d894ee.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\osx5cuz4h6.png end
[2025-08-29T17:15:02.943+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\osx5cuz4h6.png
[2025-08-29T17:15:02.946+08:00] adb shell rm /data/local/tmp/midscene_screenshot_5af7ec09-857f-4a4c-ac48-4ade91d894ee.png
[2025-08-29T17:15:03.009+08:00] adb shell rm /data/local/tmp/midscene_screenshot_5af7ec09-857f-4a4c-ac48-4ade91d894ee.png end
[2025-08-29T17:15:03.009+08:00] Resizing screenshot image
[2025-08-29T17:15:03.072+08:00] Image resize completed
[2025-08-29T17:15:03.072+08:00] Converting to base64
[2025-08-29T17:15:03.072+08:00] screenshotBase64 end
[2025-08-29T17:15:03.133+08:00] screenshotBase64 begin
[2025-08-29T17:15:03.133+08:00] adb shell wm,size
[2025-08-29T17:15:03.226+08:00] adb shell wm,size end
[2025-08-29T17:15:03.227+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:03.227+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:03.323+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:03.323+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:03.323+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:03.426+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:03.426+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:03.426+08:00] adb getScreenDensity 
[2025-08-29T17:15:03.527+08:00] adb getScreenDensity  end
[2025-08-29T17:15:03.527+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:15:03.527+08:00] adb takeScreenshot 
[2025-08-29T17:15:03.722+08:00] adb takeScreenshot  end
[2025-08-29T17:15:03.722+08:00] adb.takeScreenshot completed
[2025-08-29T17:15:03.722+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:15:03.722+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:15:03.722+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:15:03.722+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_9f6bef3e-d0db-4897-ad12-c42911a74eaa.png
[2025-08-29T17:15:06.074+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_9f6bef3e-d0db-4897-ad12-c42911a74eaa.png end
[2025-08-29T17:15:06.074+08:00] adb.shell screencap completed
[2025-08-29T17:15:06.074+08:00] Pulling screenshot file from device
[2025-08-29T17:15:06.074+08:00] adb pull /data/local/tmp/midscene_screenshot_9f6bef3e-d0db-4897-ad12-c42911a74eaa.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\chpo8g1l0fv.png
[2025-08-29T17:15:06.189+08:00] adb pull /data/local/tmp/midscene_screenshot_9f6bef3e-d0db-4897-ad12-c42911a74eaa.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\chpo8g1l0fv.png end
[2025-08-29T17:15:06.189+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\chpo8g1l0fv.png
[2025-08-29T17:15:06.191+08:00] adb shell rm /data/local/tmp/midscene_screenshot_9f6bef3e-d0db-4897-ad12-c42911a74eaa.png
[2025-08-29T17:15:06.258+08:00] adb shell rm /data/local/tmp/midscene_screenshot_9f6bef3e-d0db-4897-ad12-c42911a74eaa.png end
[2025-08-29T17:15:06.258+08:00] Resizing screenshot image
[2025-08-29T17:15:06.335+08:00] Image resize completed
[2025-08-29T17:15:06.335+08:00] Converting to base64
[2025-08-29T17:15:06.336+08:00] screenshotBase64 end
[2025-08-29T17:15:06.336+08:00] adb shell wm,size
[2025-08-29T17:15:06.420+08:00] adb shell wm,size end
[2025-08-29T17:15:06.420+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:06.420+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:06.506+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:06.506+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:06.506+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:06.608+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:06.609+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:06.609+08:00] adb getScreenDensity 
[2025-08-29T17:15:06.723+08:00] adb getScreenDensity  end
[2025-08-29T17:15:06.724+08:00] adb push D:\aigc\aigc_ui_tools\node_modules\@midscene\android\bin\yadb /data/local/tmp
[2025-08-29T17:15:06.830+08:00] adb push D:\aigc\aigc_ui_tools\node_modules\@midscene\android\bin\yadb /data/local/tmp end
[2025-08-29T17:15:06.830+08:00] adb shell input swipe 413 413 413 413 150
[2025-08-29T17:15:07.056+08:00] adb shell input swipe 413 413 413 413 150 end
[2025-08-29T17:15:07.056+08:00] adb shell app_process -Djava.class.path=/data/local/tmp/yadb /data/local/tmp com.ysbing.yadb.Main -keyboard "~CLEAR~"
[2025-08-29T17:15:08.932+08:00] adb shell app_process -Djava.class.path=/data/local/tmp/yadb /data/local/tmp com.ysbing.yadb.Main -keyboard "~CLEAR~" end
[2025-08-29T17:15:08.933+08:00] adb isSoftKeyboardPresent 
[2025-08-29T17:15:09.139+08:00] adb isSoftKeyboardPresent  end
[2025-08-29T17:15:09.140+08:00] adb shell app_process -Djava.class.path=/data/local/tmp/yadb /data/local/tmp com.ysbing.yadb.Main -keyboard "open Bluetooth"
[2025-08-29T17:15:10.409+08:00] adb shell app_process -Djava.class.path=/data/local/tmp/yadb /data/local/tmp com.ysbing.yadb.Main -keyboard "open Bluetooth" end
[2025-08-29T17:15:10.409+08:00] adb isSoftKeyboardPresent 
[2025-08-29T17:15:10.601+08:00] adb isSoftKeyboardPresent  end
[2025-08-29T17:15:10.601+08:00] Keyboard has no UI; no closing necessary
[2025-08-29T17:15:10.816+08:00] screenshotBase64 begin
[2025-08-29T17:15:10.816+08:00] adb shell wm,size
[2025-08-29T17:15:10.919+08:00] adb shell wm,size end
[2025-08-29T17:15:10.920+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:10.920+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:11.006+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:11.007+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:11.007+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:11.121+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:11.121+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:11.121+08:00] adb getScreenDensity 
[2025-08-29T17:15:11.212+08:00] adb getScreenDensity  end
[2025-08-29T17:15:11.212+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:15:11.212+08:00] adb takeScreenshot 
[2025-08-29T17:15:11.500+08:00] adb takeScreenshot  end
[2025-08-29T17:15:11.500+08:00] adb.takeScreenshot completed
[2025-08-29T17:15:11.500+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:15:11.500+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:15:11.500+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:15:11.500+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_dc85b80f-86cb-4374-9739-533ef099c5a7.png
[2025-08-29T17:15:14.549+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_dc85b80f-86cb-4374-9739-533ef099c5a7.png end
[2025-08-29T17:15:14.549+08:00] adb.shell screencap completed
[2025-08-29T17:15:14.549+08:00] Pulling screenshot file from device
[2025-08-29T17:15:14.549+08:00] adb pull /data/local/tmp/midscene_screenshot_dc85b80f-86cb-4374-9739-533ef099c5a7.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\3m3xrto93h9.png
[2025-08-29T17:15:14.682+08:00] adb pull /data/local/tmp/midscene_screenshot_dc85b80f-86cb-4374-9739-533ef099c5a7.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\3m3xrto93h9.png end
[2025-08-29T17:15:14.682+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\3m3xrto93h9.png
[2025-08-29T17:15:14.685+08:00] adb shell rm /data/local/tmp/midscene_screenshot_dc85b80f-86cb-4374-9739-533ef099c5a7.png
[2025-08-29T17:15:14.750+08:00] adb shell rm /data/local/tmp/midscene_screenshot_dc85b80f-86cb-4374-9739-533ef099c5a7.png end
[2025-08-29T17:15:14.750+08:00] Resizing screenshot image
[2025-08-29T17:15:14.834+08:00] Image resize completed
[2025-08-29T17:15:14.834+08:00] Converting to base64
[2025-08-29T17:15:14.834+08:00] screenshotBase64 end
[2025-08-29T17:15:14.898+08:00] screenshotBase64 begin
[2025-08-29T17:15:14.898+08:00] adb shell wm,size
[2025-08-29T17:15:14.988+08:00] adb shell wm,size end
[2025-08-29T17:15:14.988+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:14.988+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:15.077+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:15.077+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:15.077+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:15.215+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:15.215+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:15.216+08:00] adb getScreenDensity 
[2025-08-29T17:15:15.302+08:00] adb getScreenDensity  end
[2025-08-29T17:15:15.303+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:15:15.303+08:00] adb takeScreenshot 
[2025-08-29T17:15:15.527+08:00] adb takeScreenshot  end
[2025-08-29T17:15:15.527+08:00] adb.takeScreenshot completed
[2025-08-29T17:15:15.527+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:15:15.527+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:15:15.527+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:15:15.527+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_592d93c7-ec17-4501-b4e6-76673b2d005f.png
[2025-08-29T17:15:16.101+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_592d93c7-ec17-4501-b4e6-76673b2d005f.png end
[2025-08-29T17:15:16.101+08:00] adb.shell screencap completed
[2025-08-29T17:15:16.101+08:00] Pulling screenshot file from device
[2025-08-29T17:15:16.101+08:00] adb pull /data/local/tmp/midscene_screenshot_592d93c7-ec17-4501-b4e6-76673b2d005f.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lvjhqqnm1p.png
[2025-08-29T17:15:16.148+08:00] adb pull /data/local/tmp/midscene_screenshot_592d93c7-ec17-4501-b4e6-76673b2d005f.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lvjhqqnm1p.png end
[2025-08-29T17:15:16.148+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lvjhqqnm1p.png
[2025-08-29T17:15:16.149+08:00] adb shell rm /data/local/tmp/midscene_screenshot_592d93c7-ec17-4501-b4e6-76673b2d005f.png
[2025-08-29T17:15:16.228+08:00] adb shell rm /data/local/tmp/midscene_screenshot_592d93c7-ec17-4501-b4e6-76673b2d005f.png end
[2025-08-29T17:15:16.228+08:00] Resizing screenshot image
[2025-08-29T17:15:16.278+08:00] Image resize completed
[2025-08-29T17:15:16.278+08:00] Converting to base64
[2025-08-29T17:15:16.278+08:00] screenshotBase64 end
[2025-08-29T17:15:16.278+08:00] adb shell wm,size
[2025-08-29T17:15:16.375+08:00] adb shell wm,size end
[2025-08-29T17:15:16.375+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:16.375+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:16.475+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:16.475+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:16.475+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:16.604+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:16.604+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:16.604+08:00] adb getScreenDensity 
[2025-08-29T17:15:16.714+08:00] adb getScreenDensity  end
[2025-08-29T17:15:21.323+08:00] screenshotBase64 begin
[2025-08-29T17:15:21.323+08:00] adb shell wm,size
[2025-08-29T17:15:21.410+08:00] adb shell wm,size end
[2025-08-29T17:15:21.410+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:21.410+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:21.505+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:21.505+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:21.505+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:21.600+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:21.600+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:21.600+08:00] adb getScreenDensity 
[2025-08-29T17:15:21.703+08:00] adb getScreenDensity  end
[2025-08-29T17:15:21.703+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:15:21.703+08:00] adb takeScreenshot 
[2025-08-29T17:15:21.882+08:00] adb takeScreenshot  end
[2025-08-29T17:15:21.882+08:00] adb.takeScreenshot completed
[2025-08-29T17:15:21.882+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:15:21.882+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:15:21.883+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:15:21.883+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_800ee543-049f-4622-a2de-8522439f5cd8.png
[2025-08-29T17:15:22.395+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_800ee543-049f-4622-a2de-8522439f5cd8.png end
[2025-08-29T17:15:22.395+08:00] adb.shell screencap completed
[2025-08-29T17:15:22.395+08:00] Pulling screenshot file from device
[2025-08-29T17:15:22.395+08:00] adb pull /data/local/tmp/midscene_screenshot_800ee543-049f-4622-a2de-8522439f5cd8.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\54dp8sjwari.png
[2025-08-29T17:15:22.444+08:00] adb pull /data/local/tmp/midscene_screenshot_800ee543-049f-4622-a2de-8522439f5cd8.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\54dp8sjwari.png end
[2025-08-29T17:15:22.444+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\54dp8sjwari.png
[2025-08-29T17:15:22.444+08:00] adb shell rm /data/local/tmp/midscene_screenshot_800ee543-049f-4622-a2de-8522439f5cd8.png
[2025-08-29T17:15:22.507+08:00] adb shell rm /data/local/tmp/midscene_screenshot_800ee543-049f-4622-a2de-8522439f5cd8.png end
[2025-08-29T17:15:22.507+08:00] Resizing screenshot image
[2025-08-29T17:15:22.551+08:00] Image resize completed
[2025-08-29T17:15:22.551+08:00] Converting to base64
[2025-08-29T17:15:22.551+08:00] screenshotBase64 end
[2025-08-29T17:15:22.598+08:00] screenshotBase64 begin
[2025-08-29T17:15:22.598+08:00] adb shell wm,size
[2025-08-29T17:15:22.687+08:00] adb shell wm,size end
[2025-08-29T17:15:22.687+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:22.687+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:22.785+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:22.785+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:22.785+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:22.921+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:22.921+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:22.922+08:00] adb getScreenDensity 
[2025-08-29T17:15:23.019+08:00] adb getScreenDensity  end
[2025-08-29T17:15:23.019+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:15:23.019+08:00] adb takeScreenshot 
[2025-08-29T17:15:23.206+08:00] adb takeScreenshot  end
[2025-08-29T17:15:23.206+08:00] adb.takeScreenshot completed
[2025-08-29T17:15:23.206+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:15:23.206+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:15:23.206+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:15:23.206+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_1439d091-14b5-4ab8-a890-b811bc234f26.png
[2025-08-29T17:15:23.731+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_1439d091-14b5-4ab8-a890-b811bc234f26.png end
[2025-08-29T17:15:23.731+08:00] adb.shell screencap completed
[2025-08-29T17:15:23.731+08:00] Pulling screenshot file from device
[2025-08-29T17:15:23.731+08:00] adb pull /data/local/tmp/midscene_screenshot_1439d091-14b5-4ab8-a890-b811bc234f26.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\cxusxklqkvb.png
[2025-08-29T17:15:23.781+08:00] adb pull /data/local/tmp/midscene_screenshot_1439d091-14b5-4ab8-a890-b811bc234f26.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\cxusxklqkvb.png end
[2025-08-29T17:15:23.781+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\cxusxklqkvb.png
[2025-08-29T17:15:23.782+08:00] adb shell rm /data/local/tmp/midscene_screenshot_1439d091-14b5-4ab8-a890-b811bc234f26.png
[2025-08-29T17:15:23.846+08:00] adb shell rm /data/local/tmp/midscene_screenshot_1439d091-14b5-4ab8-a890-b811bc234f26.png end
[2025-08-29T17:15:23.846+08:00] Resizing screenshot image
[2025-08-29T17:15:23.891+08:00] Image resize completed
[2025-08-29T17:15:23.891+08:00] Converting to base64
[2025-08-29T17:15:23.891+08:00] screenshotBase64 end
[2025-08-29T17:15:23.891+08:00] adb shell wm,size
[2025-08-29T17:15:23.981+08:00] adb shell wm,size end
[2025-08-29T17:15:23.981+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:23.981+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:24.084+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:24.084+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:24.084+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:24.205+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:24.206+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:24.206+08:00] adb getScreenDensity 
[2025-08-29T17:15:24.307+08:00] adb getScreenDensity  end
[2025-08-29T17:15:24.308+08:00] adb keyevent 66
[2025-08-29T17:15:24.410+08:00] adb keyevent 66 end
[2025-08-29T17:15:24.620+08:00] screenshotBase64 begin
[2025-08-29T17:15:24.620+08:00] adb shell wm,size
[2025-08-29T17:15:24.742+08:00] adb shell wm,size end
[2025-08-29T17:15:24.742+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:24.742+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:24.846+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:24.846+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:24.846+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:24.940+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:24.940+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:24.940+08:00] adb getScreenDensity 
[2025-08-29T17:15:25.015+08:00] adb getScreenDensity  end
[2025-08-29T17:15:25.016+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:15:25.016+08:00] adb takeScreenshot 
[2025-08-29T17:15:25.184+08:00] adb takeScreenshot  end
[2025-08-29T17:15:25.184+08:00] adb.takeScreenshot completed
[2025-08-29T17:15:25.184+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:15:25.184+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:15:25.184+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:15:25.184+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_6046f958-550d-4d1f-abf6-56754965ef25.png
[2025-08-29T17:15:25.699+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_6046f958-550d-4d1f-abf6-56754965ef25.png end
[2025-08-29T17:15:25.700+08:00] adb.shell screencap completed
[2025-08-29T17:15:25.700+08:00] Pulling screenshot file from device
[2025-08-29T17:15:25.700+08:00] adb pull /data/local/tmp/midscene_screenshot_6046f958-550d-4d1f-abf6-56754965ef25.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\9catminks1m.png
[2025-08-29T17:15:25.741+08:00] adb pull /data/local/tmp/midscene_screenshot_6046f958-550d-4d1f-abf6-56754965ef25.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\9catminks1m.png end
[2025-08-29T17:15:25.741+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\9catminks1m.png
[2025-08-29T17:15:25.742+08:00] adb shell rm /data/local/tmp/midscene_screenshot_6046f958-550d-4d1f-abf6-56754965ef25.png
[2025-08-29T17:15:25.806+08:00] adb shell rm /data/local/tmp/midscene_screenshot_6046f958-550d-4d1f-abf6-56754965ef25.png end
[2025-08-29T17:15:25.806+08:00] Resizing screenshot image
[2025-08-29T17:15:25.855+08:00] Image resize completed
[2025-08-29T17:15:25.855+08:00] Converting to base64
[2025-08-29T17:15:25.855+08:00] screenshotBase64 end
[2025-08-29T17:15:27.902+08:00] screenshotBase64 begin
[2025-08-29T17:15:27.903+08:00] adb shell wm,size
[2025-08-29T17:15:27.988+08:00] adb shell wm,size end
[2025-08-29T17:15:27.988+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:27.988+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:28.102+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:28.102+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:28.102+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:28.206+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:28.206+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:28.206+08:00] adb getScreenDensity 
[2025-08-29T17:15:28.294+08:00] adb getScreenDensity  end
[2025-08-29T17:15:28.294+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:15:28.294+08:00] adb takeScreenshot 
[2025-08-29T17:15:28.499+08:00] adb takeScreenshot  end
[2025-08-29T17:15:28.500+08:00] adb.takeScreenshot completed
[2025-08-29T17:15:28.500+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:15:28.500+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:15:28.500+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:15:28.500+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_35a53e82-9c20-460c-856d-f69a327fbdd4.png
[2025-08-29T17:15:29.020+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_35a53e82-9c20-460c-856d-f69a327fbdd4.png end
[2025-08-29T17:15:29.020+08:00] adb.shell screencap completed
[2025-08-29T17:15:29.020+08:00] Pulling screenshot file from device
[2025-08-29T17:15:29.020+08:00] adb pull /data/local/tmp/midscene_screenshot_35a53e82-9c20-460c-856d-f69a327fbdd4.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\r9cs1rrcnj.png
[2025-08-29T17:15:29.075+08:00] adb pull /data/local/tmp/midscene_screenshot_35a53e82-9c20-460c-856d-f69a327fbdd4.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\r9cs1rrcnj.png end
[2025-08-29T17:15:29.075+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\r9cs1rrcnj.png
[2025-08-29T17:15:29.076+08:00] adb shell rm /data/local/tmp/midscene_screenshot_35a53e82-9c20-460c-856d-f69a327fbdd4.png
[2025-08-29T17:15:29.139+08:00] adb shell rm /data/local/tmp/midscene_screenshot_35a53e82-9c20-460c-856d-f69a327fbdd4.png end
[2025-08-29T17:15:29.139+08:00] Resizing screenshot image
[2025-08-29T17:15:29.191+08:00] Image resize completed
[2025-08-29T17:15:29.191+08:00] Converting to base64
[2025-08-29T17:15:29.191+08:00] screenshotBase64 end
[2025-08-29T17:15:29.254+08:00] screenshotBase64 begin
[2025-08-29T17:15:29.254+08:00] adb shell wm,size
[2025-08-29T17:15:29.345+08:00] adb shell wm,size end
[2025-08-29T17:15:29.345+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:29.345+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:29.438+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:29.438+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:29.438+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:29.539+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:29.539+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:29.539+08:00] adb getScreenDensity 
[2025-08-29T17:15:29.634+08:00] adb getScreenDensity  end
[2025-08-29T17:15:29.634+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:15:29.634+08:00] adb takeScreenshot 
[2025-08-29T17:15:29.817+08:00] adb takeScreenshot  end
[2025-08-29T17:15:29.817+08:00] adb.takeScreenshot completed
[2025-08-29T17:15:29.817+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:15:29.817+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:15:29.818+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:15:29.818+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_f185962a-e1ff-4213-9571-4ebbd9fdabba.png
[2025-08-29T17:15:30.316+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_f185962a-e1ff-4213-9571-4ebbd9fdabba.png end
[2025-08-29T17:15:30.316+08:00] adb.shell screencap completed
[2025-08-29T17:15:30.316+08:00] Pulling screenshot file from device
[2025-08-29T17:15:30.316+08:00] adb pull /data/local/tmp/midscene_screenshot_f185962a-e1ff-4213-9571-4ebbd9fdabba.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\iry7gkqfctl.png
[2025-08-29T17:15:30.359+08:00] adb pull /data/local/tmp/midscene_screenshot_f185962a-e1ff-4213-9571-4ebbd9fdabba.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\iry7gkqfctl.png end
[2025-08-29T17:15:30.359+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\iry7gkqfctl.png
[2025-08-29T17:15:30.360+08:00] adb shell rm /data/local/tmp/midscene_screenshot_f185962a-e1ff-4213-9571-4ebbd9fdabba.png
[2025-08-29T17:15:30.420+08:00] adb shell rm /data/local/tmp/midscene_screenshot_f185962a-e1ff-4213-9571-4ebbd9fdabba.png end
[2025-08-29T17:15:30.420+08:00] Resizing screenshot image
[2025-08-29T17:15:30.466+08:00] Image resize completed
[2025-08-29T17:15:30.466+08:00] Converting to base64
[2025-08-29T17:15:30.467+08:00] screenshotBase64 end
[2025-08-29T17:15:30.467+08:00] adb shell wm,size
[2025-08-29T17:15:30.565+08:00] adb shell wm,size end
[2025-08-29T17:15:30.565+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:30.565+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:30.647+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:30.647+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:30.647+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:30.736+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:30.736+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:30.736+08:00] adb getScreenDensity 
[2025-08-29T17:15:30.812+08:00] adb getScreenDensity  end
[2025-08-29T17:15:30.874+08:00] screenshotBase64 begin
[2025-08-29T17:15:30.874+08:00] adb shell wm,size
[2025-08-29T17:15:30.960+08:00] adb shell wm,size end
[2025-08-29T17:15:30.960+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:30.960+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:31.060+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:31.060+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:31.060+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:31.158+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:31.158+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:31.158+08:00] adb getScreenDensity 
[2025-08-29T17:15:31.249+08:00] adb getScreenDensity  end
[2025-08-29T17:15:31.249+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:15:31.249+08:00] adb takeScreenshot 
[2025-08-29T17:15:31.433+08:00] adb takeScreenshot  end
[2025-08-29T17:15:31.434+08:00] adb.takeScreenshot completed
[2025-08-29T17:15:31.434+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:15:31.434+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:15:31.434+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:15:31.434+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_d58a5fab-6305-4722-a752-5c8d120d6ca6.png
[2025-08-29T17:15:31.947+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_d58a5fab-6305-4722-a752-5c8d120d6ca6.png end
[2025-08-29T17:15:31.947+08:00] adb.shell screencap completed
[2025-08-29T17:15:31.947+08:00] Pulling screenshot file from device
[2025-08-29T17:15:31.947+08:00] adb pull /data/local/tmp/midscene_screenshot_d58a5fab-6305-4722-a752-5c8d120d6ca6.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ouyw6y92zks.png
[2025-08-29T17:15:31.991+08:00] adb pull /data/local/tmp/midscene_screenshot_d58a5fab-6305-4722-a752-5c8d120d6ca6.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ouyw6y92zks.png end
[2025-08-29T17:15:31.991+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ouyw6y92zks.png
[2025-08-29T17:15:31.992+08:00] adb shell rm /data/local/tmp/midscene_screenshot_d58a5fab-6305-4722-a752-5c8d120d6ca6.png
[2025-08-29T17:15:32.048+08:00] adb shell rm /data/local/tmp/midscene_screenshot_d58a5fab-6305-4722-a752-5c8d120d6ca6.png end
[2025-08-29T17:15:32.048+08:00] Resizing screenshot image
[2025-08-29T17:15:32.096+08:00] Image resize completed
[2025-08-29T17:15:32.096+08:00] Converting to base64
[2025-08-29T17:15:32.096+08:00] screenshotBase64 end
[2025-08-29T17:15:32.096+08:00] adb shell wm,size
[2025-08-29T17:15:32.169+08:00] adb shell wm,size end
[2025-08-29T17:15:32.169+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:32.169+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:32.268+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:32.268+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:32.268+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:32.379+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:32.379+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:32.379+08:00] adb getScreenDensity 
[2025-08-29T17:15:32.474+08:00] adb getScreenDensity  end
[2025-08-29T17:15:35.384+08:00] screenshotBase64 begin
[2025-08-29T17:15:35.384+08:00] adb shell wm,size
[2025-08-29T17:15:35.476+08:00] adb shell wm,size end
[2025-08-29T17:15:35.476+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:35.476+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:35.554+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:35.554+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:35.554+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:35.665+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:35.665+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:35.665+08:00] adb getScreenDensity 
[2025-08-29T17:15:35.762+08:00] adb getScreenDensity  end
[2025-08-29T17:15:35.762+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:15:35.762+08:00] adb takeScreenshot 
[2025-08-29T17:15:35.946+08:00] adb takeScreenshot  end
[2025-08-29T17:15:35.946+08:00] adb.takeScreenshot completed
[2025-08-29T17:15:35.946+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:15:35.946+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:15:35.946+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:15:35.946+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_69a6c6e4-2df5-45ac-b1be-44d6dccd8a8b.png
[2025-08-29T17:15:36.490+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_69a6c6e4-2df5-45ac-b1be-44d6dccd8a8b.png end
[2025-08-29T17:15:36.490+08:00] adb.shell screencap completed
[2025-08-29T17:15:36.490+08:00] Pulling screenshot file from device
[2025-08-29T17:15:36.490+08:00] adb pull /data/local/tmp/midscene_screenshot_69a6c6e4-2df5-45ac-b1be-44d6dccd8a8b.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\7bz60774337.png
[2025-08-29T17:15:36.529+08:00] adb pull /data/local/tmp/midscene_screenshot_69a6c6e4-2df5-45ac-b1be-44d6dccd8a8b.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\7bz60774337.png end
[2025-08-29T17:15:36.529+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\7bz60774337.png
[2025-08-29T17:15:36.530+08:00] adb shell rm /data/local/tmp/midscene_screenshot_69a6c6e4-2df5-45ac-b1be-44d6dccd8a8b.png
[2025-08-29T17:15:36.582+08:00] adb shell rm /data/local/tmp/midscene_screenshot_69a6c6e4-2df5-45ac-b1be-44d6dccd8a8b.png end
[2025-08-29T17:15:36.583+08:00] Resizing screenshot image
[2025-08-29T17:15:36.623+08:00] Image resize completed
[2025-08-29T17:15:36.624+08:00] Converting to base64
[2025-08-29T17:15:36.624+08:00] screenshotBase64 end
[2025-08-29T17:15:36.681+08:00] screenshotBase64 begin
[2025-08-29T17:15:36.681+08:00] adb shell wm,size
[2025-08-29T17:15:36.777+08:00] adb shell wm,size end
[2025-08-29T17:15:36.777+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:36.777+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:36.871+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:36.871+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:36.871+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:36.971+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:36.971+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:36.971+08:00] adb getScreenDensity 
[2025-08-29T17:15:37.038+08:00] adb getScreenDensity  end
[2025-08-29T17:15:37.038+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:15:37.038+08:00] adb takeScreenshot 
[2025-08-29T17:15:37.226+08:00] adb takeScreenshot  end
[2025-08-29T17:15:37.226+08:00] adb.takeScreenshot completed
[2025-08-29T17:15:37.226+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:15:37.226+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:15:37.226+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:15:37.226+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_2ec651e2-6b79-4b5b-b71b-34ecb620a490.png
[2025-08-29T17:15:37.727+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_2ec651e2-6b79-4b5b-b71b-34ecb620a490.png end
[2025-08-29T17:15:37.727+08:00] adb.shell screencap completed
[2025-08-29T17:15:37.727+08:00] Pulling screenshot file from device
[2025-08-29T17:15:37.727+08:00] adb pull /data/local/tmp/midscene_screenshot_2ec651e2-6b79-4b5b-b71b-34ecb620a490.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\a1qkm2x34o.png
[2025-08-29T17:15:37.775+08:00] adb pull /data/local/tmp/midscene_screenshot_2ec651e2-6b79-4b5b-b71b-34ecb620a490.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\a1qkm2x34o.png end
[2025-08-29T17:15:37.775+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\a1qkm2x34o.png
[2025-08-29T17:15:37.775+08:00] adb shell rm /data/local/tmp/midscene_screenshot_2ec651e2-6b79-4b5b-b71b-34ecb620a490.png
[2025-08-29T17:15:37.839+08:00] adb shell rm /data/local/tmp/midscene_screenshot_2ec651e2-6b79-4b5b-b71b-34ecb620a490.png end
[2025-08-29T17:15:37.839+08:00] Resizing screenshot image
[2025-08-29T17:15:37.892+08:00] Image resize completed
[2025-08-29T17:15:37.892+08:00] Converting to base64
[2025-08-29T17:15:37.892+08:00] screenshotBase64 end
[2025-08-29T17:15:37.892+08:00] adb shell wm,size
[2025-08-29T17:15:37.959+08:00] adb shell wm,size end
[2025-08-29T17:15:37.959+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:37.959+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:38.032+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:38.032+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:38.032+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:38.120+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:38.120+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:38.120+08:00] adb getScreenDensity 
[2025-08-29T17:15:38.211+08:00] adb getScreenDensity  end
[2025-08-29T17:15:38.280+08:00] screenshotBase64 begin
[2025-08-29T17:15:38.280+08:00] adb shell wm,size
[2025-08-29T17:15:38.355+08:00] adb shell wm,size end
[2025-08-29T17:15:38.355+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:38.355+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:38.436+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:38.436+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:38.436+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:38.526+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:38.526+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:38.526+08:00] adb getScreenDensity 
[2025-08-29T17:15:38.618+08:00] adb getScreenDensity  end
[2025-08-29T17:15:38.619+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:15:38.619+08:00] adb takeScreenshot 
[2025-08-29T17:15:38.787+08:00] adb takeScreenshot  end
[2025-08-29T17:15:38.787+08:00] adb.takeScreenshot completed
[2025-08-29T17:15:38.787+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:15:38.787+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:15:38.788+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:15:38.788+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_1742aaa0-49a8-4a85-92b6-626c2a269542.png
[2025-08-29T17:15:39.277+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_1742aaa0-49a8-4a85-92b6-626c2a269542.png end
[2025-08-29T17:15:39.277+08:00] adb.shell screencap completed
[2025-08-29T17:15:39.277+08:00] Pulling screenshot file from device
[2025-08-29T17:15:39.277+08:00] adb pull /data/local/tmp/midscene_screenshot_1742aaa0-49a8-4a85-92b6-626c2a269542.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\dqg1cyd3n3d.png
[2025-08-29T17:15:39.323+08:00] adb pull /data/local/tmp/midscene_screenshot_1742aaa0-49a8-4a85-92b6-626c2a269542.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\dqg1cyd3n3d.png end
[2025-08-29T17:15:39.323+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\dqg1cyd3n3d.png
[2025-08-29T17:15:39.323+08:00] adb shell rm /data/local/tmp/midscene_screenshot_1742aaa0-49a8-4a85-92b6-626c2a269542.png
[2025-08-29T17:15:39.375+08:00] adb shell rm /data/local/tmp/midscene_screenshot_1742aaa0-49a8-4a85-92b6-626c2a269542.png end
[2025-08-29T17:15:39.375+08:00] Resizing screenshot image
[2025-08-29T17:15:39.421+08:00] Image resize completed
[2025-08-29T17:15:39.421+08:00] Converting to base64
[2025-08-29T17:15:39.421+08:00] screenshotBase64 end
[2025-08-29T17:15:39.421+08:00] adb shell wm,size
[2025-08-29T17:15:39.491+08:00] adb shell wm,size end
[2025-08-29T17:15:39.491+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:39.491+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:39.562+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:39.562+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:39.562+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:39.660+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:39.660+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:39.660+08:00] adb getScreenDensity 
[2025-08-29T17:15:39.741+08:00] adb getScreenDensity  end
[2025-08-29T17:15:42.715+08:00] screenshotBase64 begin
[2025-08-29T17:15:42.715+08:00] adb shell wm,size
[2025-08-29T17:15:42.823+08:00] adb shell wm,size end
[2025-08-29T17:15:42.823+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:42.823+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:42.916+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:42.916+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:42.916+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:43.026+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:43.026+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:43.027+08:00] adb getScreenDensity 
[2025-08-29T17:15:43.111+08:00] adb getScreenDensity  end
[2025-08-29T17:15:43.111+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:15:43.111+08:00] adb takeScreenshot 
[2025-08-29T17:15:43.295+08:00] adb takeScreenshot  end
[2025-08-29T17:15:43.295+08:00] adb.takeScreenshot completed
[2025-08-29T17:15:43.295+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:15:43.295+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:15:43.295+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:15:43.295+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_a6da13b9-e5f2-45ff-955e-87c6f3f486d5.png
[2025-08-29T17:15:43.822+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_a6da13b9-e5f2-45ff-955e-87c6f3f486d5.png end
[2025-08-29T17:15:43.822+08:00] adb.shell screencap completed
[2025-08-29T17:15:43.822+08:00] Pulling screenshot file from device
[2025-08-29T17:15:43.822+08:00] adb pull /data/local/tmp/midscene_screenshot_a6da13b9-e5f2-45ff-955e-87c6f3f486d5.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lwqf603hkz.png
[2025-08-29T17:15:43.867+08:00] adb pull /data/local/tmp/midscene_screenshot_a6da13b9-e5f2-45ff-955e-87c6f3f486d5.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lwqf603hkz.png end
[2025-08-29T17:15:43.867+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lwqf603hkz.png
[2025-08-29T17:15:43.867+08:00] adb shell rm /data/local/tmp/midscene_screenshot_a6da13b9-e5f2-45ff-955e-87c6f3f486d5.png
[2025-08-29T17:15:43.954+08:00] adb shell rm /data/local/tmp/midscene_screenshot_a6da13b9-e5f2-45ff-955e-87c6f3f486d5.png end
[2025-08-29T17:15:43.954+08:00] Resizing screenshot image
[2025-08-29T17:15:43.998+08:00] Image resize completed
[2025-08-29T17:15:43.998+08:00] Converting to base64
[2025-08-29T17:15:43.998+08:00] screenshotBase64 end
[2025-08-29T17:15:44.057+08:00] screenshotBase64 begin
[2025-08-29T17:15:44.057+08:00] adb shell wm,size
[2025-08-29T17:15:44.135+08:00] adb shell wm,size end
[2025-08-29T17:15:44.135+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:44.135+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:44.228+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:44.228+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:44.228+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:44.324+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:44.324+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:44.324+08:00] adb getScreenDensity 
[2025-08-29T17:15:44.417+08:00] adb getScreenDensity  end
[2025-08-29T17:15:44.417+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:15:44.417+08:00] adb takeScreenshot 
[2025-08-29T17:15:44.626+08:00] adb takeScreenshot  end
[2025-08-29T17:15:44.626+08:00] adb.takeScreenshot completed
[2025-08-29T17:15:44.626+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:15:44.626+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:15:44.626+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:15:44.626+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_3f1a135a-ce15-4606-a0cb-28ec5db4c0d9.png
[2025-08-29T17:15:45.150+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_3f1a135a-ce15-4606-a0cb-28ec5db4c0d9.png end
[2025-08-29T17:15:45.150+08:00] adb.shell screencap completed
[2025-08-29T17:15:45.150+08:00] Pulling screenshot file from device
[2025-08-29T17:15:45.150+08:00] adb pull /data/local/tmp/midscene_screenshot_3f1a135a-ce15-4606-a0cb-28ec5db4c0d9.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\8j3pxchdvpg.png
[2025-08-29T17:15:45.199+08:00] adb pull /data/local/tmp/midscene_screenshot_3f1a135a-ce15-4606-a0cb-28ec5db4c0d9.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\8j3pxchdvpg.png end
[2025-08-29T17:15:45.199+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\8j3pxchdvpg.png
[2025-08-29T17:15:45.199+08:00] adb shell rm /data/local/tmp/midscene_screenshot_3f1a135a-ce15-4606-a0cb-28ec5db4c0d9.png
[2025-08-29T17:15:45.257+08:00] adb shell rm /data/local/tmp/midscene_screenshot_3f1a135a-ce15-4606-a0cb-28ec5db4c0d9.png end
[2025-08-29T17:15:45.257+08:00] Resizing screenshot image
[2025-08-29T17:15:45.300+08:00] Image resize completed
[2025-08-29T17:15:45.300+08:00] Converting to base64
[2025-08-29T17:15:45.300+08:00] screenshotBase64 end
[2025-08-29T17:15:45.300+08:00] adb shell wm,size
[2025-08-29T17:15:45.388+08:00] adb shell wm,size end
[2025-08-29T17:15:45.388+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:45.388+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:45.494+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:45.494+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:45.494+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:45.607+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:45.607+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:45.607+08:00] adb getScreenDensity 
[2025-08-29T17:15:45.698+08:00] adb getScreenDensity  end
[2025-08-29T17:15:45.772+08:00] screenshotBase64 begin
[2025-08-29T17:15:45.772+08:00] adb shell wm,size
[2025-08-29T17:15:45.863+08:00] adb shell wm,size end
[2025-08-29T17:15:45.863+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:45.863+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:45.954+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:45.954+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:45.955+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:46.043+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:46.043+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:46.043+08:00] adb getScreenDensity 
[2025-08-29T17:15:46.127+08:00] adb getScreenDensity  end
[2025-08-29T17:15:46.127+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:15:46.127+08:00] adb takeScreenshot 
[2025-08-29T17:15:46.316+08:00] adb takeScreenshot  end
[2025-08-29T17:15:46.316+08:00] adb.takeScreenshot completed
[2025-08-29T17:15:46.316+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:15:46.316+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:15:46.316+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:15:46.316+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_efde327a-cc27-4947-91b0-28b96fe64354.png
[2025-08-29T17:15:46.828+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_efde327a-cc27-4947-91b0-28b96fe64354.png end
[2025-08-29T17:15:46.828+08:00] adb.shell screencap completed
[2025-08-29T17:15:46.828+08:00] Pulling screenshot file from device
[2025-08-29T17:15:46.828+08:00] adb pull /data/local/tmp/midscene_screenshot_efde327a-cc27-4947-91b0-28b96fe64354.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ekyp3sawrro.png
[2025-08-29T17:15:46.890+08:00] adb pull /data/local/tmp/midscene_screenshot_efde327a-cc27-4947-91b0-28b96fe64354.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ekyp3sawrro.png end
[2025-08-29T17:15:46.890+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ekyp3sawrro.png
[2025-08-29T17:15:46.890+08:00] adb shell rm /data/local/tmp/midscene_screenshot_efde327a-cc27-4947-91b0-28b96fe64354.png
[2025-08-29T17:15:46.959+08:00] adb shell rm /data/local/tmp/midscene_screenshot_efde327a-cc27-4947-91b0-28b96fe64354.png end
[2025-08-29T17:15:46.959+08:00] Resizing screenshot image
[2025-08-29T17:15:47.006+08:00] Image resize completed
[2025-08-29T17:15:47.006+08:00] Converting to base64
[2025-08-29T17:15:47.006+08:00] screenshotBase64 end
[2025-08-29T17:15:47.006+08:00] adb shell wm,size
[2025-08-29T17:15:47.089+08:00] adb shell wm,size end
[2025-08-29T17:15:47.089+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:47.089+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:47.166+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:47.166+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:47.166+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:47.280+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:47.280+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:47.280+08:00] adb getScreenDensity 
[2025-08-29T17:15:47.367+08:00] adb getScreenDensity  end
[2025-08-29T17:15:51.039+08:00] screenshotBase64 begin
[2025-08-29T17:15:51.039+08:00] adb shell wm,size
[2025-08-29T17:15:51.143+08:00] adb shell wm,size end
[2025-08-29T17:15:51.144+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:51.144+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:51.230+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:51.230+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:51.230+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:51.261+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys display | grep mCurrentOrientation'' exited with code 3221225786'; Command output: <empty>
[2025-08-29T17:15:51.261+08:00] Failed to get orientation from display, default to 0
[2025-08-29T17:15:51.261+08:00] adb getScreenDensity 
[2025-08-29T17:15:51.346+08:00] adb getScreenDensity  end
[2025-08-29T17:15:51.346+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:15:51.346+08:00] adb takeScreenshot 
[2025-08-29T17:15:51.539+08:00] adb takeScreenshot  end
[2025-08-29T17:15:51.539+08:00] adb.takeScreenshot completed
[2025-08-29T17:15:51.539+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:15:51.539+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:15:51.540+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:15:51.540+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_0c4207ed-3c87-401e-a2bc-97d1f01fad9d.png
[2025-08-29T17:15:52.040+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_0c4207ed-3c87-401e-a2bc-97d1f01fad9d.png end
[2025-08-29T17:15:52.040+08:00] adb.shell screencap completed
[2025-08-29T17:15:52.040+08:00] Pulling screenshot file from device
[2025-08-29T17:15:52.040+08:00] adb pull /data/local/tmp/midscene_screenshot_0c4207ed-3c87-401e-a2bc-97d1f01fad9d.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lpjabk6vfy.png
[2025-08-29T17:15:52.096+08:00] adb pull /data/local/tmp/midscene_screenshot_0c4207ed-3c87-401e-a2bc-97d1f01fad9d.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lpjabk6vfy.png end
[2025-08-29T17:15:52.096+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lpjabk6vfy.png
[2025-08-29T17:15:52.097+08:00] adb shell rm /data/local/tmp/midscene_screenshot_0c4207ed-3c87-401e-a2bc-97d1f01fad9d.png
[2025-08-29T17:15:52.157+08:00] adb shell rm /data/local/tmp/midscene_screenshot_0c4207ed-3c87-401e-a2bc-97d1f01fad9d.png end
[2025-08-29T17:15:52.157+08:00] Resizing screenshot image
[2025-08-29T17:15:52.214+08:00] Image resize completed
[2025-08-29T17:15:52.214+08:00] Converting to base64
[2025-08-29T17:15:52.214+08:00] screenshotBase64 end
[2025-08-29T17:15:52.214+08:00] screenshotBase64 begin
[2025-08-29T17:15:52.214+08:00] adb shell wm,size
[2025-08-29T17:15:52.320+08:00] adb shell wm,size end
[2025-08-29T17:15:52.320+08:00] Using Physical size: 1080x2436
[2025-08-29T17:15:52.320+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:15:52.438+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:15:52.438+08:00] Failed to get orientation from input, try display
[2025-08-29T17:15:52.438+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:15:52.568+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:15:52.568+08:00] Screen orientation (fallback): 0
[2025-08-29T17:15:52.568+08:00] adb getScreenDensity 
[2025-08-29T17:15:52.656+08:00] adb getScreenDensity  end
[2025-08-29T17:15:52.656+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:15:52.656+08:00] adb takeScreenshot 
[2025-08-29T17:15:52.845+08:00] adb takeScreenshot  end
[2025-08-29T17:15:52.845+08:00] adb.takeScreenshot completed
[2025-08-29T17:15:52.845+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:15:52.845+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:15:52.845+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:15:52.845+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_6c130791-e469-457e-97eb-471d9a955921.png
[2025-08-29T17:15:53.348+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_6c130791-e469-457e-97eb-471d9a955921.png end
[2025-08-29T17:15:53.348+08:00] adb.shell screencap completed
[2025-08-29T17:15:53.348+08:00] Pulling screenshot file from device
[2025-08-29T17:15:53.348+08:00] adb pull /data/local/tmp/midscene_screenshot_6c130791-e469-457e-97eb-471d9a955921.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\b0zh71p7ssd.png
[2025-08-29T17:15:53.390+08:00] adb pull /data/local/tmp/midscene_screenshot_6c130791-e469-457e-97eb-471d9a955921.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\b0zh71p7ssd.png end
[2025-08-29T17:15:53.390+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\b0zh71p7ssd.png
[2025-08-29T17:15:53.390+08:00] adb shell rm /data/local/tmp/midscene_screenshot_6c130791-e469-457e-97eb-471d9a955921.png
[2025-08-29T17:15:53.451+08:00] adb shell rm /data/local/tmp/midscene_screenshot_6c130791-e469-457e-97eb-471d9a955921.png end
[2025-08-29T17:15:53.452+08:00] Resizing screenshot image
[2025-08-29T17:15:53.492+08:00] Image resize completed
[2025-08-29T17:15:53.492+08:00] Converting to base64
[2025-08-29T17:15:53.492+08:00] screenshotBase64 end
[2025-08-29T17:16:14.768+08:00] Initializing ADB with device ID: ASALE3741B000022
[2025-08-29T17:16:14.770+08:00] adb shell wm,size
[2025-08-29T17:16:14.884+08:00] adb shell wm,size end
[2025-08-29T17:16:14.884+08:00] Using Physical size: 1080x2436
[2025-08-29T17:16:14.884+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:16:15.008+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:16:15.008+08:00] Failed to get orientation from input, try display
[2025-08-29T17:16:15.008+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:16:15.130+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:16:15.131+08:00] Screen orientation (fallback): 0
[2025-08-29T17:16:15.131+08:00] ADB initialized successfully 
DeviceId: ASALE3741B000022
ScreenSize:
  physical size: 1080x2436

[2025-08-29T17:16:15.132+08:00] Launching app: com.transsion.aivoiceassistant
[2025-08-29T17:16:15.132+08:00] adb activateApp com.transsion.aivoiceassistant
[2025-08-29T17:16:15.488+08:00] adb activateApp com.transsion.aivoiceassistant end
[2025-08-29T17:16:15.488+08:00] Successfully launched: com.transsion.aivoiceassistant
[2025-08-29T17:16:20.584+08:00] screenshotBase64 begin
[2025-08-29T17:16:20.584+08:00] adb shell wm,size
[2025-08-29T17:16:20.700+08:00] adb shell wm,size end
[2025-08-29T17:16:20.700+08:00] Using Physical size: 1080x2436
[2025-08-29T17:16:20.700+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:16:20.792+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:16:20.792+08:00] Failed to get orientation from input, try display
[2025-08-29T17:16:20.792+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:16:20.909+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:16:20.909+08:00] Screen orientation (fallback): 0
[2025-08-29T17:16:20.909+08:00] adb getScreenDensity 
[2025-08-29T17:16:21.001+08:00] adb getScreenDensity  end
[2025-08-29T17:16:21.002+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:16:21.002+08:00] adb takeScreenshot 
[2025-08-29T17:16:21.194+08:00] adb takeScreenshot  end
[2025-08-29T17:16:21.194+08:00] adb.takeScreenshot completed
[2025-08-29T17:16:21.194+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:16:21.194+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:16:21.195+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:16:21.195+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_1c3c380d-8643-41cb-bf51-bedb693ce602.png
[2025-08-29T17:16:21.749+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_1c3c380d-8643-41cb-bf51-bedb693ce602.png end
[2025-08-29T17:16:21.749+08:00] adb.shell screencap completed
[2025-08-29T17:16:21.749+08:00] Pulling screenshot file from device
[2025-08-29T17:16:21.749+08:00] adb pull /data/local/tmp/midscene_screenshot_1c3c380d-8643-41cb-bf51-bedb693ce602.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\1ggpqkz03em.png
[2025-08-29T17:16:21.797+08:00] adb pull /data/local/tmp/midscene_screenshot_1c3c380d-8643-41cb-bf51-bedb693ce602.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\1ggpqkz03em.png end
[2025-08-29T17:16:21.797+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\1ggpqkz03em.png
[2025-08-29T17:16:21.798+08:00] adb shell rm /data/local/tmp/midscene_screenshot_1c3c380d-8643-41cb-bf51-bedb693ce602.png
[2025-08-29T17:16:21.860+08:00] adb shell rm /data/local/tmp/midscene_screenshot_1c3c380d-8643-41cb-bf51-bedb693ce602.png end
[2025-08-29T17:16:21.860+08:00] Resizing screenshot image
[2025-08-29T17:16:21.986+08:00] Image resize completed
[2025-08-29T17:16:21.986+08:00] Converting to base64
[2025-08-29T17:16:21.987+08:00] screenshotBase64 end
[2025-08-29T17:16:21.987+08:00] adb shell wm,size
[2025-08-29T17:16:22.080+08:00] adb shell wm,size end
[2025-08-29T17:16:22.081+08:00] Using Physical size: 1080x2436
[2025-08-29T17:16:22.081+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:16:22.181+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:16:22.181+08:00] Failed to get orientation from input, try display
[2025-08-29T17:16:22.181+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:16:22.298+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:16:22.298+08:00] Screen orientation (fallback): 0
[2025-08-29T17:16:22.298+08:00] adb getScreenDensity 
[2025-08-29T17:16:22.382+08:00] adb getScreenDensity  end
[2025-08-29T17:16:27.970+08:00] screenshotBase64 begin
[2025-08-29T17:16:27.971+08:00] adb shell wm,size
[2025-08-29T17:16:28.067+08:00] adb shell wm,size end
[2025-08-29T17:16:28.067+08:00] Using Physical size: 1080x2436
[2025-08-29T17:16:28.067+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:16:28.162+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:16:28.162+08:00] Failed to get orientation from input, try display
[2025-08-29T17:16:28.162+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:16:28.284+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:16:28.284+08:00] Screen orientation (fallback): 0
[2025-08-29T17:16:28.284+08:00] adb getScreenDensity 
[2025-08-29T17:16:28.388+08:00] adb getScreenDensity  end
[2025-08-29T17:16:28.389+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:16:28.389+08:00] adb takeScreenshot 
[2025-08-29T17:16:28.588+08:00] adb takeScreenshot  end
[2025-08-29T17:16:28.588+08:00] adb.takeScreenshot completed
[2025-08-29T17:16:28.588+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:16:28.588+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:16:28.589+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:16:28.589+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_d012a36b-51a9-49f3-ab2c-07e2c41f81c1.png
[2025-08-29T17:16:29.128+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_d012a36b-51a9-49f3-ab2c-07e2c41f81c1.png end
[2025-08-29T17:16:29.128+08:00] adb.shell screencap completed
[2025-08-29T17:16:29.128+08:00] Pulling screenshot file from device
[2025-08-29T17:16:29.128+08:00] adb pull /data/local/tmp/midscene_screenshot_d012a36b-51a9-49f3-ab2c-07e2c41f81c1.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\izk0pa5csp.png
[2025-08-29T17:16:29.192+08:00] adb pull /data/local/tmp/midscene_screenshot_d012a36b-51a9-49f3-ab2c-07e2c41f81c1.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\izk0pa5csp.png end
[2025-08-29T17:16:29.192+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\izk0pa5csp.png
[2025-08-29T17:16:29.194+08:00] adb shell rm /data/local/tmp/midscene_screenshot_d012a36b-51a9-49f3-ab2c-07e2c41f81c1.png
[2025-08-29T17:16:29.289+08:00] adb shell rm /data/local/tmp/midscene_screenshot_d012a36b-51a9-49f3-ab2c-07e2c41f81c1.png end
[2025-08-29T17:16:29.289+08:00] Resizing screenshot image
[2025-08-29T17:16:29.446+08:00] Image resize completed
[2025-08-29T17:16:29.446+08:00] Converting to base64
[2025-08-29T17:16:29.446+08:00] screenshotBase64 end
[2025-08-29T17:16:29.446+08:00] adb shell wm,size
[2025-08-29T17:16:29.541+08:00] adb shell wm,size end
[2025-08-29T17:16:29.542+08:00] Using Physical size: 1080x2436
[2025-08-29T17:16:29.542+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:16:29.642+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:16:29.642+08:00] Failed to get orientation from input, try display
[2025-08-29T17:16:29.642+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:16:29.738+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:16:29.738+08:00] Screen orientation (fallback): 0
[2025-08-29T17:16:29.738+08:00] adb getScreenDensity 
[2025-08-29T17:16:29.848+08:00] adb getScreenDensity  end
[2025-08-29T17:16:29.855+08:00] screenshotBase64 begin
[2025-08-29T17:16:29.856+08:00] adb shell wm,size
[2025-08-29T17:16:29.965+08:00] adb shell wm,size end
[2025-08-29T17:16:29.965+08:00] Using Physical size: 1080x2436
[2025-08-29T17:16:29.965+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:16:30.065+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:16:30.065+08:00] Failed to get orientation from input, try display
[2025-08-29T17:16:30.065+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:16:30.175+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:16:30.175+08:00] Screen orientation (fallback): 0
[2025-08-29T17:16:30.175+08:00] adb getScreenDensity 
[2025-08-29T17:16:30.262+08:00] adb getScreenDensity  end
[2025-08-29T17:16:30.263+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:16:30.263+08:00] adb takeScreenshot 
[2025-08-29T17:16:30.443+08:00] adb takeScreenshot  end
[2025-08-29T17:16:30.443+08:00] adb.takeScreenshot completed
[2025-08-29T17:16:30.443+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:16:30.443+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:16:30.444+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:16:30.444+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_38c1dc53-c566-4ae0-812d-86988425ecd7.png
[2025-08-29T17:16:30.958+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_38c1dc53-c566-4ae0-812d-86988425ecd7.png end
[2025-08-29T17:16:30.958+08:00] adb.shell screencap completed
[2025-08-29T17:16:30.958+08:00] Pulling screenshot file from device
[2025-08-29T17:16:30.958+08:00] adb pull /data/local/tmp/midscene_screenshot_38c1dc53-c566-4ae0-812d-86988425ecd7.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\7e41xgm4mtu.png
[2025-08-29T17:16:31.009+08:00] adb pull /data/local/tmp/midscene_screenshot_38c1dc53-c566-4ae0-812d-86988425ecd7.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\7e41xgm4mtu.png end
[2025-08-29T17:16:31.009+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\7e41xgm4mtu.png
[2025-08-29T17:16:31.010+08:00] adb shell rm /data/local/tmp/midscene_screenshot_38c1dc53-c566-4ae0-812d-86988425ecd7.png
[2025-08-29T17:16:31.082+08:00] adb shell rm /data/local/tmp/midscene_screenshot_38c1dc53-c566-4ae0-812d-86988425ecd7.png end
[2025-08-29T17:16:31.082+08:00] Resizing screenshot image
[2025-08-29T17:16:31.208+08:00] Image resize completed
[2025-08-29T17:16:31.208+08:00] Converting to base64
[2025-08-29T17:16:31.208+08:00] screenshotBase64 end
[2025-08-29T17:16:31.273+08:00] screenshotBase64 begin
[2025-08-29T17:16:31.273+08:00] adb shell wm,size
[2025-08-29T17:16:31.369+08:00] adb shell wm,size end
[2025-08-29T17:16:31.369+08:00] Using Physical size: 1080x2436
[2025-08-29T17:16:31.369+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:16:31.472+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:16:31.473+08:00] Failed to get orientation from input, try display
[2025-08-29T17:16:31.473+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:16:31.596+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:16:31.596+08:00] Screen orientation (fallback): 0
[2025-08-29T17:16:31.597+08:00] adb getScreenDensity 
[2025-08-29T17:16:31.690+08:00] adb getScreenDensity  end
[2025-08-29T17:16:31.690+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:16:31.690+08:00] adb takeScreenshot 
[2025-08-29T17:16:31.869+08:00] adb takeScreenshot  end
[2025-08-29T17:16:31.869+08:00] adb.takeScreenshot completed
[2025-08-29T17:16:31.869+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:16:31.869+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:16:31.870+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:16:31.870+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_5bf91dcc-7179-4057-868d-29b6694e288a.png
[2025-08-29T17:16:32.389+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_5bf91dcc-7179-4057-868d-29b6694e288a.png end
[2025-08-29T17:16:32.389+08:00] adb.shell screencap completed
[2025-08-29T17:16:32.389+08:00] Pulling screenshot file from device
[2025-08-29T17:16:32.389+08:00] adb pull /data/local/tmp/midscene_screenshot_5bf91dcc-7179-4057-868d-29b6694e288a.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\e23rr0nx4s.png
[2025-08-29T17:16:32.458+08:00] adb pull /data/local/tmp/midscene_screenshot_5bf91dcc-7179-4057-868d-29b6694e288a.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\e23rr0nx4s.png end
[2025-08-29T17:16:32.459+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\e23rr0nx4s.png
[2025-08-29T17:16:32.460+08:00] adb shell rm /data/local/tmp/midscene_screenshot_5bf91dcc-7179-4057-868d-29b6694e288a.png
[2025-08-29T17:16:32.540+08:00] adb shell rm /data/local/tmp/midscene_screenshot_5bf91dcc-7179-4057-868d-29b6694e288a.png end
[2025-08-29T17:16:32.540+08:00] Resizing screenshot image
[2025-08-29T17:16:32.675+08:00] Image resize completed
[2025-08-29T17:16:32.675+08:00] Converting to base64
[2025-08-29T17:16:32.675+08:00] screenshotBase64 end
[2025-08-29T17:16:32.675+08:00] adb shell wm,size
[2025-08-29T17:16:32.770+08:00] adb shell wm,size end
[2025-08-29T17:16:32.770+08:00] Using Physical size: 1080x2436
[2025-08-29T17:16:32.770+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:16:32.862+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:16:32.862+08:00] Failed to get orientation from input, try display
[2025-08-29T17:16:32.862+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:16:32.988+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:16:32.988+08:00] Screen orientation (fallback): 0
[2025-08-29T17:16:32.988+08:00] adb getScreenDensity 
[2025-08-29T17:16:33.086+08:00] adb getScreenDensity  end
[2025-08-29T17:16:33.088+08:00] adb push D:\aigc\aigc_ui_tools\node_modules\@midscene\android\bin\yadb /data/local/tmp
[2025-08-29T17:16:33.217+08:00] adb push D:\aigc\aigc_ui_tools\node_modules\@midscene\android\bin\yadb /data/local/tmp end
[2025-08-29T17:16:33.218+08:00] adb shell input swipe 413 413 413 413 150
[2025-08-29T17:16:33.488+08:00] adb shell input swipe 413 413 413 413 150 end
[2025-08-29T17:16:33.488+08:00] adb shell app_process -Djava.class.path=/data/local/tmp/yadb /data/local/tmp com.ysbing.yadb.Main -keyboard "~CLEAR~"
[2025-08-29T17:16:34.752+08:00] adb shell app_process -Djava.class.path=/data/local/tmp/yadb /data/local/tmp com.ysbing.yadb.Main -keyboard "~CLEAR~" end
[2025-08-29T17:16:34.753+08:00] adb isSoftKeyboardPresent 
[2025-08-29T17:16:34.961+08:00] adb isSoftKeyboardPresent  end
[2025-08-29T17:16:34.961+08:00] adb shell app_process -Djava.class.path=/data/local/tmp/yadb /data/local/tmp com.ysbing.yadb.Main -keyboard "open Bluetooth"
[2025-08-29T17:16:36.248+08:00] adb shell app_process -Djava.class.path=/data/local/tmp/yadb /data/local/tmp com.ysbing.yadb.Main -keyboard "open Bluetooth" end
[2025-08-29T17:16:36.248+08:00] adb isSoftKeyboardPresent 
[2025-08-29T17:16:36.435+08:00] adb isSoftKeyboardPresent  end
[2025-08-29T17:16:36.435+08:00] Keyboard has no UI; no closing necessary
[2025-08-29T17:16:36.645+08:00] screenshotBase64 begin
[2025-08-29T17:16:36.645+08:00] adb shell wm,size
[2025-08-29T17:16:36.742+08:00] adb shell wm,size end
[2025-08-29T17:16:36.742+08:00] Using Physical size: 1080x2436
[2025-08-29T17:16:36.742+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:16:36.856+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:16:36.856+08:00] Failed to get orientation from input, try display
[2025-08-29T17:16:36.856+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:16:36.979+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:16:36.979+08:00] Screen orientation (fallback): 0
[2025-08-29T17:16:36.980+08:00] adb getScreenDensity 
[2025-08-29T17:16:37.095+08:00] adb getScreenDensity  end
[2025-08-29T17:16:37.095+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:16:37.095+08:00] adb takeScreenshot 
[2025-08-29T17:16:37.290+08:00] adb takeScreenshot  end
[2025-08-29T17:16:37.290+08:00] adb.takeScreenshot completed
[2025-08-29T17:16:37.290+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:16:37.290+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:16:37.291+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:16:37.291+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_a2face07-f282-41d7-a463-3c4ac6619ea3.png
[2025-08-29T17:16:37.828+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_a2face07-f282-41d7-a463-3c4ac6619ea3.png end
[2025-08-29T17:16:37.828+08:00] adb.shell screencap completed
[2025-08-29T17:16:37.828+08:00] Pulling screenshot file from device
[2025-08-29T17:16:37.828+08:00] adb pull /data/local/tmp/midscene_screenshot_a2face07-f282-41d7-a463-3c4ac6619ea3.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\5yx2nf2vaxj.png
[2025-08-29T17:16:37.904+08:00] adb pull /data/local/tmp/midscene_screenshot_a2face07-f282-41d7-a463-3c4ac6619ea3.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\5yx2nf2vaxj.png end
[2025-08-29T17:16:37.904+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\5yx2nf2vaxj.png
[2025-08-29T17:16:37.906+08:00] adb shell rm /data/local/tmp/midscene_screenshot_a2face07-f282-41d7-a463-3c4ac6619ea3.png
[2025-08-29T17:16:37.990+08:00] adb shell rm /data/local/tmp/midscene_screenshot_a2face07-f282-41d7-a463-3c4ac6619ea3.png end
[2025-08-29T17:16:37.990+08:00] Resizing screenshot image
[2025-08-29T17:16:38.125+08:00] Image resize completed
[2025-08-29T17:16:38.125+08:00] Converting to base64
[2025-08-29T17:16:38.126+08:00] screenshotBase64 end
[2025-08-29T17:16:38.212+08:00] screenshotBase64 begin
[2025-08-29T17:16:38.212+08:00] adb shell wm,size
[2025-08-29T17:16:38.311+08:00] adb shell wm,size end
[2025-08-29T17:16:38.311+08:00] Using Physical size: 1080x2436
[2025-08-29T17:16:38.311+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:16:38.444+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:16:38.444+08:00] Failed to get orientation from input, try display
[2025-08-29T17:16:38.444+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:16:38.565+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:16:38.565+08:00] Screen orientation (fallback): 0
[2025-08-29T17:16:38.565+08:00] adb getScreenDensity 
[2025-08-29T17:16:38.676+08:00] adb getScreenDensity  end
[2025-08-29T17:16:38.677+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:16:38.677+08:00] adb takeScreenshot 
[2025-08-29T17:16:38.858+08:00] adb takeScreenshot  end
[2025-08-29T17:16:38.859+08:00] adb.takeScreenshot completed
[2025-08-29T17:16:38.859+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:16:38.859+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:16:38.859+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:16:38.859+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_04cc2ae7-4ff4-430f-93f5-f8ebfcaa5ed7.png
[2025-08-29T17:16:39.375+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_04cc2ae7-4ff4-430f-93f5-f8ebfcaa5ed7.png end
[2025-08-29T17:16:39.375+08:00] adb.shell screencap completed
[2025-08-29T17:16:39.375+08:00] Pulling screenshot file from device
[2025-08-29T17:16:39.375+08:00] adb pull /data/local/tmp/midscene_screenshot_04cc2ae7-4ff4-430f-93f5-f8ebfcaa5ed7.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\3g8u2qyklu3.png
[2025-08-29T17:16:39.432+08:00] adb pull /data/local/tmp/midscene_screenshot_04cc2ae7-4ff4-430f-93f5-f8ebfcaa5ed7.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\3g8u2qyklu3.png end
[2025-08-29T17:16:39.432+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\3g8u2qyklu3.png
[2025-08-29T17:16:39.433+08:00] adb shell rm /data/local/tmp/midscene_screenshot_04cc2ae7-4ff4-430f-93f5-f8ebfcaa5ed7.png
[2025-08-29T17:16:39.499+08:00] adb shell rm /data/local/tmp/midscene_screenshot_04cc2ae7-4ff4-430f-93f5-f8ebfcaa5ed7.png end
[2025-08-29T17:16:39.499+08:00] Resizing screenshot image
[2025-08-29T17:16:39.610+08:00] Image resize completed
[2025-08-29T17:16:39.610+08:00] Converting to base64
[2025-08-29T17:16:39.610+08:00] screenshotBase64 end
[2025-08-29T17:16:39.611+08:00] adb shell wm,size
[2025-08-29T17:16:39.711+08:00] adb shell wm,size end
[2025-08-29T17:16:39.712+08:00] Using Physical size: 1080x2436
[2025-08-29T17:16:39.712+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:16:39.840+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:16:39.840+08:00] Failed to get orientation from input, try display
[2025-08-29T17:16:39.841+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:16:39.989+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:16:39.989+08:00] Screen orientation (fallback): 0
[2025-08-29T17:16:39.990+08:00] adb getScreenDensity 
[2025-08-29T17:16:40.153+08:00] adb getScreenDensity  end
[2025-08-29T17:16:45.700+08:00] screenshotBase64 begin
[2025-08-29T17:16:45.700+08:00] adb shell wm,size
[2025-08-29T17:16:45.807+08:00] adb shell wm,size end
[2025-08-29T17:16:45.807+08:00] Using Physical size: 1080x2436
[2025-08-29T17:16:45.807+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:16:45.943+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:16:45.943+08:00] Failed to get orientation from input, try display
[2025-08-29T17:16:45.943+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:16:46.089+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:16:46.089+08:00] Screen orientation (fallback): 0
[2025-08-29T17:16:46.089+08:00] adb getScreenDensity 
[2025-08-29T17:16:46.206+08:00] adb getScreenDensity  end
[2025-08-29T17:16:46.206+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:16:46.206+08:00] adb takeScreenshot 
[2025-08-29T17:16:46.388+08:00] adb takeScreenshot  end
[2025-08-29T17:16:46.388+08:00] adb.takeScreenshot completed
[2025-08-29T17:16:46.388+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:16:46.388+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:16:46.389+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:16:46.389+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_18747524-5fc8-4ded-9e50-404c3a7b2ad2.png
[2025-08-29T17:16:46.920+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_18747524-5fc8-4ded-9e50-404c3a7b2ad2.png end
[2025-08-29T17:16:46.920+08:00] adb.shell screencap completed
[2025-08-29T17:16:46.920+08:00] Pulling screenshot file from device
[2025-08-29T17:16:46.920+08:00] adb pull /data/local/tmp/midscene_screenshot_18747524-5fc8-4ded-9e50-404c3a7b2ad2.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\vp4g6eqxas.png
[2025-08-29T17:16:46.994+08:00] adb pull /data/local/tmp/midscene_screenshot_18747524-5fc8-4ded-9e50-404c3a7b2ad2.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\vp4g6eqxas.png end
[2025-08-29T17:16:46.994+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\vp4g6eqxas.png
[2025-08-29T17:16:46.996+08:00] adb shell rm /data/local/tmp/midscene_screenshot_18747524-5fc8-4ded-9e50-404c3a7b2ad2.png
[2025-08-29T17:16:47.081+08:00] adb shell rm /data/local/tmp/midscene_screenshot_18747524-5fc8-4ded-9e50-404c3a7b2ad2.png end
[2025-08-29T17:16:47.081+08:00] Resizing screenshot image
[2025-08-29T17:16:47.213+08:00] Image resize completed
[2025-08-29T17:16:47.213+08:00] Converting to base64
[2025-08-29T17:16:47.213+08:00] screenshotBase64 end
[2025-08-29T17:16:47.291+08:00] screenshotBase64 begin
[2025-08-29T17:16:47.291+08:00] adb shell wm,size
[2025-08-29T17:16:47.397+08:00] adb shell wm,size end
[2025-08-29T17:16:47.397+08:00] Using Physical size: 1080x2436
[2025-08-29T17:16:47.397+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:16:47.507+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:16:47.507+08:00] Failed to get orientation from input, try display
[2025-08-29T17:16:47.507+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:16:47.634+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:16:47.635+08:00] Screen orientation (fallback): 0
[2025-08-29T17:16:47.635+08:00] adb getScreenDensity 
[2025-08-29T17:16:47.734+08:00] adb getScreenDensity  end
[2025-08-29T17:16:47.734+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:16:47.734+08:00] adb takeScreenshot 
[2025-08-29T17:16:47.925+08:00] adb takeScreenshot  end
[2025-08-29T17:16:47.925+08:00] adb.takeScreenshot completed
[2025-08-29T17:16:47.925+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:16:47.925+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:16:47.925+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:16:47.925+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_42264e82-bb46-4888-a4ca-71e013ec36ba.png
[2025-08-29T17:16:48.439+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_42264e82-bb46-4888-a4ca-71e013ec36ba.png end
[2025-08-29T17:16:48.440+08:00] adb.shell screencap completed
[2025-08-29T17:16:48.440+08:00] Pulling screenshot file from device
[2025-08-29T17:16:48.440+08:00] adb pull /data/local/tmp/midscene_screenshot_42264e82-bb46-4888-a4ca-71e013ec36ba.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\uroa5a6oxsg.png
[2025-08-29T17:16:48.519+08:00] adb pull /data/local/tmp/midscene_screenshot_42264e82-bb46-4888-a4ca-71e013ec36ba.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\uroa5a6oxsg.png end
[2025-08-29T17:16:48.519+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\uroa5a6oxsg.png
[2025-08-29T17:16:48.520+08:00] adb shell rm /data/local/tmp/midscene_screenshot_42264e82-bb46-4888-a4ca-71e013ec36ba.png
[2025-08-29T17:16:48.584+08:00] adb shell rm /data/local/tmp/midscene_screenshot_42264e82-bb46-4888-a4ca-71e013ec36ba.png end
[2025-08-29T17:16:48.584+08:00] Resizing screenshot image
[2025-08-29T17:16:48.701+08:00] Image resize completed
[2025-08-29T17:16:48.701+08:00] Converting to base64
[2025-08-29T17:16:48.701+08:00] screenshotBase64 end
[2025-08-29T17:16:48.701+08:00] adb shell wm,size
[2025-08-29T17:16:48.804+08:00] adb shell wm,size end
[2025-08-29T17:16:48.804+08:00] Using Physical size: 1080x2436
[2025-08-29T17:16:48.804+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:16:48.906+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:16:48.906+08:00] Failed to get orientation from input, try display
[2025-08-29T17:16:48.906+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:16:49.015+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:16:49.015+08:00] Screen orientation (fallback): 0
[2025-08-29T17:16:49.015+08:00] adb getScreenDensity 
[2025-08-29T17:16:49.103+08:00] adb getScreenDensity  end
[2025-08-29T17:16:49.105+08:00] adb keyevent 66
[2025-08-29T17:16:49.210+08:00] adb keyevent 66 end
[2025-08-29T17:16:49.424+08:00] screenshotBase64 begin
[2025-08-29T17:16:49.424+08:00] adb shell wm,size
[2025-08-29T17:16:49.524+08:00] adb shell wm,size end
[2025-08-29T17:16:49.524+08:00] Using Physical size: 1080x2436
[2025-08-29T17:16:49.524+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:16:49.616+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:16:49.616+08:00] Failed to get orientation from input, try display
[2025-08-29T17:16:49.616+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:16:49.740+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:16:49.740+08:00] Screen orientation (fallback): 0
[2025-08-29T17:16:49.740+08:00] adb getScreenDensity 
[2025-08-29T17:16:49.844+08:00] adb getScreenDensity  end
[2025-08-29T17:16:49.844+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:16:49.844+08:00] adb takeScreenshot 
[2025-08-29T17:16:50.039+08:00] adb takeScreenshot  end
[2025-08-29T17:16:50.039+08:00] adb.takeScreenshot completed
[2025-08-29T17:16:50.039+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:16:50.039+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:16:50.040+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:16:50.040+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_64344e8f-0fff-4482-9b27-1c8c268ffcae.png
[2025-08-29T17:16:50.557+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_64344e8f-0fff-4482-9b27-1c8c268ffcae.png end
[2025-08-29T17:16:50.557+08:00] adb.shell screencap completed
[2025-08-29T17:16:50.557+08:00] Pulling screenshot file from device
[2025-08-29T17:16:50.557+08:00] adb pull /data/local/tmp/midscene_screenshot_64344e8f-0fff-4482-9b27-1c8c268ffcae.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\fe0jvyjtf7.png
[2025-08-29T17:16:50.631+08:00] adb pull /data/local/tmp/midscene_screenshot_64344e8f-0fff-4482-9b27-1c8c268ffcae.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\fe0jvyjtf7.png end
[2025-08-29T17:16:50.631+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\fe0jvyjtf7.png
[2025-08-29T17:16:50.632+08:00] adb shell rm /data/local/tmp/midscene_screenshot_64344e8f-0fff-4482-9b27-1c8c268ffcae.png
[2025-08-29T17:16:50.711+08:00] adb shell rm /data/local/tmp/midscene_screenshot_64344e8f-0fff-4482-9b27-1c8c268ffcae.png end
[2025-08-29T17:16:50.711+08:00] Resizing screenshot image
[2025-08-29T17:16:50.826+08:00] Image resize completed
[2025-08-29T17:16:50.826+08:00] Converting to base64
[2025-08-29T17:16:50.826+08:00] screenshotBase64 end
[2025-08-29T17:16:52.898+08:00] screenshotBase64 begin
[2025-08-29T17:16:52.898+08:00] adb shell wm,size
[2025-08-29T17:16:52.998+08:00] adb shell wm,size end
[2025-08-29T17:16:52.998+08:00] Using Physical size: 1080x2436
[2025-08-29T17:16:52.998+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:16:53.126+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:16:53.126+08:00] Failed to get orientation from input, try display
[2025-08-29T17:16:53.126+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:16:53.231+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:16:53.231+08:00] Screen orientation (fallback): 0
[2025-08-29T17:16:53.231+08:00] adb getScreenDensity 
[2025-08-29T17:16:53.314+08:00] adb getScreenDensity  end
[2025-08-29T17:16:53.314+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:16:53.315+08:00] adb takeScreenshot 
[2025-08-29T17:16:53.505+08:00] adb takeScreenshot  end
[2025-08-29T17:16:53.505+08:00] adb.takeScreenshot completed
[2025-08-29T17:16:53.506+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:16:53.506+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:16:53.506+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:16:53.506+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_956255bf-2e08-442c-900d-122d1b6d1f8b.png
[2025-08-29T17:16:54.040+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_956255bf-2e08-442c-900d-122d1b6d1f8b.png end
[2025-08-29T17:16:54.041+08:00] adb.shell screencap completed
[2025-08-29T17:16:54.041+08:00] Pulling screenshot file from device
[2025-08-29T17:16:54.041+08:00] adb pull /data/local/tmp/midscene_screenshot_956255bf-2e08-442c-900d-122d1b6d1f8b.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\tgcotsnmrvc.png
[2025-08-29T17:16:54.110+08:00] adb pull /data/local/tmp/midscene_screenshot_956255bf-2e08-442c-900d-122d1b6d1f8b.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\tgcotsnmrvc.png end
[2025-08-29T17:16:54.110+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\tgcotsnmrvc.png
[2025-08-29T17:16:54.112+08:00] adb shell rm /data/local/tmp/midscene_screenshot_956255bf-2e08-442c-900d-122d1b6d1f8b.png
[2025-08-29T17:16:54.191+08:00] adb shell rm /data/local/tmp/midscene_screenshot_956255bf-2e08-442c-900d-122d1b6d1f8b.png end
[2025-08-29T17:16:54.191+08:00] Resizing screenshot image
[2025-08-29T17:16:54.301+08:00] Image resize completed
[2025-08-29T17:16:54.301+08:00] Converting to base64
[2025-08-29T17:16:54.301+08:00] screenshotBase64 end
[2025-08-29T17:16:54.368+08:00] screenshotBase64 begin
[2025-08-29T17:16:54.368+08:00] adb shell wm,size
[2025-08-29T17:16:54.469+08:00] adb shell wm,size end
[2025-08-29T17:16:54.469+08:00] Using Physical size: 1080x2436
[2025-08-29T17:16:54.469+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:16:54.575+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:16:54.575+08:00] Failed to get orientation from input, try display
[2025-08-29T17:16:54.575+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:16:54.701+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:16:54.701+08:00] Screen orientation (fallback): 0
[2025-08-29T17:16:54.701+08:00] adb getScreenDensity 
[2025-08-29T17:16:54.795+08:00] adb getScreenDensity  end
[2025-08-29T17:16:54.795+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:16:54.795+08:00] adb takeScreenshot 
[2025-08-29T17:16:54.970+08:00] adb takeScreenshot  end
[2025-08-29T17:16:54.970+08:00] adb.takeScreenshot completed
[2025-08-29T17:16:54.970+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:16:54.970+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:16:54.971+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:16:54.971+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_a706bfb0-6775-43e0-89f9-2d1c221d4127.png
[2025-08-29T17:16:55.508+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_a706bfb0-6775-43e0-89f9-2d1c221d4127.png end
[2025-08-29T17:16:55.508+08:00] adb.shell screencap completed
[2025-08-29T17:16:55.508+08:00] Pulling screenshot file from device
[2025-08-29T17:16:55.508+08:00] adb pull /data/local/tmp/midscene_screenshot_a706bfb0-6775-43e0-89f9-2d1c221d4127.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\qo6e4bpipi.png
[2025-08-29T17:16:55.582+08:00] adb pull /data/local/tmp/midscene_screenshot_a706bfb0-6775-43e0-89f9-2d1c221d4127.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\qo6e4bpipi.png end
[2025-08-29T17:16:55.582+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\qo6e4bpipi.png
[2025-08-29T17:16:55.583+08:00] adb shell rm /data/local/tmp/midscene_screenshot_a706bfb0-6775-43e0-89f9-2d1c221d4127.png
[2025-08-29T17:16:55.672+08:00] adb shell rm /data/local/tmp/midscene_screenshot_a706bfb0-6775-43e0-89f9-2d1c221d4127.png end
[2025-08-29T17:16:55.672+08:00] Resizing screenshot image
[2025-08-29T17:16:55.775+08:00] Image resize completed
[2025-08-29T17:16:55.775+08:00] Converting to base64
[2025-08-29T17:16:55.775+08:00] screenshotBase64 end
[2025-08-29T17:16:55.775+08:00] adb shell wm,size
[2025-08-29T17:16:55.878+08:00] adb shell wm,size end
[2025-08-29T17:16:55.878+08:00] Using Physical size: 1080x2436
[2025-08-29T17:16:55.878+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:16:55.991+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:16:55.992+08:00] Failed to get orientation from input, try display
[2025-08-29T17:16:55.992+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:16:56.097+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:16:56.097+08:00] Screen orientation (fallback): 0
[2025-08-29T17:16:56.097+08:00] adb getScreenDensity 
[2025-08-29T17:16:56.198+08:00] adb getScreenDensity  end
[2025-08-29T17:16:56.290+08:00] screenshotBase64 begin
[2025-08-29T17:16:56.290+08:00] adb shell wm,size
[2025-08-29T17:16:56.397+08:00] adb shell wm,size end
[2025-08-29T17:16:56.397+08:00] Using Physical size: 1080x2436
[2025-08-29T17:16:56.397+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:16:56.501+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:16:56.501+08:00] Failed to get orientation from input, try display
[2025-08-29T17:16:56.501+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:16:56.619+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:16:56.619+08:00] Screen orientation (fallback): 0
[2025-08-29T17:16:56.619+08:00] adb getScreenDensity 
[2025-08-29T17:16:56.724+08:00] adb getScreenDensity  end
[2025-08-29T17:16:56.724+08:00] Taking screenshot via adb.takeScreenshot
[2025-08-29T17:16:56.724+08:00] adb takeScreenshot 
[2025-08-29T17:16:56.930+08:00] adb takeScreenshot  end
[2025-08-29T17:16:56.930+08:00] adb.takeScreenshot completed
[2025-08-29T17:16:56.930+08:00] Invalid image buffer detected: not a valid image format
[2025-08-29T17:16:56.930+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-08-29T17:16:56.930+08:00] Fallback: taking screenshot via shell screencap
[2025-08-29T17:16:56.931+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_fcbb84c5-3a8b-43c9-8ae8-dcef2312c16d.png
[2025-08-29T17:16:57.467+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_fcbb84c5-3a8b-43c9-8ae8-dcef2312c16d.png end
[2025-08-29T17:16:57.467+08:00] adb.shell screencap completed
[2025-08-29T17:16:57.467+08:00] Pulling screenshot file from device
[2025-08-29T17:16:57.467+08:00] adb pull /data/local/tmp/midscene_screenshot_fcbb84c5-3a8b-43c9-8ae8-dcef2312c16d.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\z2uk9btdncs.png
[2025-08-29T17:16:57.522+08:00] adb pull /data/local/tmp/midscene_screenshot_fcbb84c5-3a8b-43c9-8ae8-dcef2312c16d.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\z2uk9btdncs.png end
[2025-08-29T17:16:57.522+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\z2uk9btdncs.png
[2025-08-29T17:16:57.523+08:00] adb shell rm /data/local/tmp/midscene_screenshot_fcbb84c5-3a8b-43c9-8ae8-dcef2312c16d.png
[2025-08-29T17:16:57.600+08:00] adb shell rm /data/local/tmp/midscene_screenshot_fcbb84c5-3a8b-43c9-8ae8-dcef2312c16d.png end
[2025-08-29T17:16:57.600+08:00] Resizing screenshot image
[2025-08-29T17:16:57.711+08:00] Image resize completed
[2025-08-29T17:16:57.711+08:00] Converting to base64
[2025-08-29T17:16:57.711+08:00] screenshotBase64 end
[2025-08-29T17:16:57.712+08:00] adb shell wm,size
[2025-08-29T17:16:57.831+08:00] adb shell wm,size end
[2025-08-29T17:16:57.832+08:00] Using Physical size: 1080x2436
[2025-08-29T17:16:57.832+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-08-29T17:16:57.972+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-08-29T17:16:57.972+08:00] Failed to get orientation from input, try display
[2025-08-29T17:16:57.972+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-08-29T17:16:58.101+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-08-29T17:16:58.102+08:00] Screen orientation (fallback): 0
[2025-08-29T17:16:58.102+08:00] adb getScreenDensity 
[2025-08-29T17:16:58.198+08:00] adb getScreenDensity  end
