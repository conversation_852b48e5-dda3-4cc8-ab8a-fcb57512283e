"""
多模态UI语义增强智能体
基于AutoGen框架实现支持图像和文本的UI元素语义分析
"""
import sys
import asyncio
import json
import base64
from io import BytesIO
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
from loguru import logger
from PIL import Image

# 添加example目录到路径
sys.path.append(str(Path(__file__).parent.parent / "example"))

# AutoGen相关导入
AUTOGEN_AVAILABLE = False

def _try_import_autogen():
    """尝试导入AutoGen模块"""
    global AUTOGEN_AVAILABLE

    if AUTOGEN_AVAILABLE:
        return True

    try:
        from autogen_agentchat.agents import AssistantAgent
        from autogen_agentchat.messages import TextMessage, MultiModalMessage
        from autogen_agentchat.ui import Console
        from autogen_core import Image as AGImage
        from example.llms import qwenvl_model_client

        AUTOGEN_AVAILABLE = True
        logger.info("AutoGen多模态模块导入成功")
        return True

    except ImportError as e:
        logger.warning(f"AutoGen依赖不可用: {e}")
        logger.warning("将使用降级模式，禁用多模态AI语义增强功能")
        AUTOGEN_AVAILABLE = False
        return False
    except Exception as e:
        logger.error(f"AutoGen导入异常: {e}")
        AUTOGEN_AVAILABLE = False
        return False

from backend.config import config
from backend.models.element_model import UIElement
from backend.models.page_model import UIPage


class MultiModalUISemanticAgent:
    """多模态UI语义分析智能体 - 基于AutoGen框架"""

    def __init__(self):
        """初始化多模态UI语义分析智能体"""
        self.ai_config = config.ai
        self.model_client = None
        self.agent = None
        self.fallback_enabled = self.ai_config.fallback_enabled

        # 初始化智能体
        self._initialize_agent()

    def _initialize_agent(self):
        """初始化AutoGen多模态智能体"""
        if not _try_import_autogen():
            logger.warning("AutoGen不可用，跳过多模态智能体初始化")
            self.model_client = None
            self.agent = None
            return

        try:
            # 导入AutoGen组件
            from autogen_agentchat.agents import AssistantAgent
            from example.llms import qwenvl_model_client

            logger.info("初始化多模态UI语义分析智能体...")

            # 创建支持视觉的模型客户端
            self.model_client = qwenvl_model_client()

            # 创建智能体 - 按照AutoGen标准方式
            self.agent = AssistantAgent(
                name="multimodal_ui_semantic_analyzer",
                model_client=self.model_client,
                system_message=self._get_multimodal_system_prompt()
            )

            logger.info("多模态UI语义分析智能体初始化成功")

        except Exception as e:
            logger.error(f"初始化多模态智能体失败: {e}")
            self.model_client = None
            self.agent = None
            if not self.fallback_enabled:
                raise

    def _get_multimodal_system_prompt(self) -> str:
        """获取多模态系统提示词"""
        return """你是一个专业的Android UI元素分析专家，具备多模态分析能力。

任务：分析UI元素的语义和功能，结合页面截图和元素信息，返回JSON格式结果。

分析要点：
1. 结合页面截图理解元素的视觉上下文
2. 分析元素类型、位置、文本内容和属性
3. 推断用户交互意图和功能分类
4. 提供准确的语义描述

返回格式：
{
    "semantic_description": "元素的语义描述（中文）",
    "expected_action": "预期的用户操作",
    "element_category": "元素类别",
    "confidence_score": 0.95,
    "visual_context": "基于截图的视觉上下文描述",
    "interaction_hints": "交互提示"
}

请确保返回有效的JSON格式，并充分利用视觉信息提高分析准确性。"""

    def _load_screenshot_as_image(self, screenshot_path: str) -> Optional['AGImage']:
        """加载截图为AutoGen Image对象"""
        try:
            if not _try_import_autogen():
                return None

            from autogen_core import Image as AGImage

            # 加载PIL图像
            pil_image = Image.open(screenshot_path)

            # 转换为AutoGen Image对象
            ag_image = AGImage(pil_image)

            logger.debug(f"成功加载截图: {screenshot_path}")
            return ag_image

        except Exception as e:
            logger.error(f"加载截图失败: {e}")
            return None

    def _create_multimodal_analysis_prompt(self,
                                         element: UIElement,
                                         page_context: Dict[str, Any] = None) -> str:
        """创建多模态元素分析提示词"""
        prompt_parts = [
            "请分析以下UI元素，结合页面截图提供准确的语义分析：",
            "",
            "元素信息：",
            f"- 类型: {element.element_type}",
            f"- 文本: {element.text or '无'}",
            f"- 资源ID: {element.resource_id or '无'}",
            f"- 描述: {element.content_desc or '无'}",
            f"- 位置: ({element.position.left}, {element.position.top}) - ({element.position.right}, {element.position.bottom})" if element.position else "位置未知",
            f"- 可点击: {'是' if element.attributes and element.attributes.clickable else '否'}",
            f"- 可编辑: {'是' if element.attributes and element.attributes.editable else '否'}",
            f"- 可滚动: {'是' if element.attributes and element.attributes.scrollable else '否'}",
        ]

        if page_context:
            prompt_parts.extend([
                "",
                "页面上下文：",
                f"- 应用: {page_context.get('package_name', '未知')}",
                f"- 页面: {page_context.get('page_name', '未知')}",
                f"- Activity: {page_context.get('activity_name', '未知')}"
            ])

        prompt_parts.extend([
            "",
            "请基于截图中的视觉信息和元素属性，返回JSON格式的详细分析结果。",
            "特别关注元素在页面中的视觉位置、周围环境和用户交互意图。"
        ])

        return "\n".join(prompt_parts)

    async def analyze_element_with_screenshot(self,
                                            element: UIElement,
                                            screenshot_path: str,
                                            page_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        使用截图分析单个元素的语义信息 - 多模态分析

        Args:
            element: UI元素
            screenshot_path: 页面截图路径
            page_context: 页面上下文信息

        Returns:
            Dict[str, Any]: 语义分析结果
        """
        try:
            if not _try_import_autogen() or not self.agent:
                logger.debug("AutoGen不可用，使用降级处理")
                return self._fallback_element_analysis(element)

            logger.debug(f"多模态分析元素: {element.element_type} - {element.text or element.resource_id}")

            # 加载截图
            screenshot_image = self._load_screenshot_as_image(screenshot_path)
            if not screenshot_image:
                logger.warning("无法加载截图，使用文本模式分析")
                return await self._analyze_element_text_only(element, page_context)

            # 创建多模态消息
            from autogen_agentchat.messages import MultiModalMessage

            analysis_prompt = self._create_multimodal_analysis_prompt(element, page_context)

            # 创建包含图像和文本的多模态消息
            multimodal_message = MultiModalMessage(
                content=[analysis_prompt, screenshot_image],
                source="User"
            )

            # 使用AutoGen标准方式运行任务
            result = await self.agent.run(task=multimodal_message)

            # 解析响应
            if result and result.messages:
                content = result.messages[-1].content
                return self._parse_multimodal_analysis_result(content, element)
            else:
                logger.warning("多模态智能体返回空响应")
                return self._fallback_element_analysis(element)

        except Exception as e:
            logger.error(f"多模态元素语义分析失败: {e}")
            if self.fallback_enabled:
                return self._fallback_element_analysis(element)
            else:
                raise

    async def _analyze_element_text_only(self,
                                       element: UIElement,
                                       page_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """文本模式元素分析（当截图不可用时）"""
        try:
            from autogen_agentchat.messages import TextMessage

            analysis_prompt = self._create_multimodal_analysis_prompt(element, page_context)
            text_message = TextMessage(content=analysis_prompt, source="User")

            result = await self.agent.run(task=text_message)

            if result and result.messages:
                content = result.messages[-1].content
                return self._parse_multimodal_analysis_result(content, element)
            else:
                return self._fallback_element_analysis(element)

        except Exception as e:
            logger.error(f"文本模式分析失败: {e}")
            return self._fallback_element_analysis(element)

    def _parse_multimodal_analysis_result(self, content: str, element: UIElement) -> Dict[str, Any]:
        """解析多模态分析结果"""
        try:
            # 尝试解析JSON
            if isinstance(content, str):
                # 提取JSON部分
                start_idx = content.find('{')
                end_idx = content.rfind('}') + 1
                if start_idx >= 0 and end_idx > start_idx:
                    json_str = content[start_idx:end_idx]
                    result = json.loads(json_str)

                    # 验证必要字段
                    required_fields = ['semantic_description', 'expected_action', 'element_category', 'confidence_score']
                    if all(field in result for field in required_fields):
                        logger.debug(f"多模态元素语义分析成功: {result['semantic_description']}")
                        return result

        except json.JSONDecodeError as e:
            logger.warning(f"解析多模态JSON失败: {e}")
        except Exception as e:
            logger.warning(f"解析多模态结果异常: {e}")

        # 解析失败，使用降级处理
        logger.warning("多模态AI分析结果格式不正确，使用降级处理")
        return self._fallback_element_analysis(element)

    def _fallback_element_analysis(self, element: UIElement) -> Dict[str, Any]:
        """
        降级处理：基于规则的元素分析

        Args:
            element: UI元素

        Returns:
            Dict[str, Any]: 分析结果
        """
        try:
            logger.debug(f"使用规则分析元素: {element.element_type}")

            # 基于元素类型和属性进行分析
            element_type = element.element_type.lower()
            text = element.text or ""
            resource_id = element.resource_id or ""
            content_desc = element.content_desc or ""

            # 默认结果
            result = {
                "semantic_description": "",
                "expected_action": "",
                "element_category": "",
                "confidence_score": 0.7,
                "visual_context": "基于规则分析，无视觉上下文",
                "interaction_hints": ""
            }

            # 按钮类元素
            if 'button' in element_type or (element.attributes and element.attributes.clickable):
                result["element_category"] = "按钮"
                if text:
                    result["semantic_description"] = f"'{text}' 按钮"
                    result["expected_action"] = f"点击{text}"
                    result["interaction_hints"] = f"点击执行{text}相关操作"
                else:
                    result["semantic_description"] = "可点击按钮"
                    result["expected_action"] = "点击操作"
                    result["interaction_hints"] = "点击执行相关操作"

            # 文本输入框
            elif 'edittext' in element_type or (element.attributes and element.attributes.editable):
                result["element_category"] = "输入框"
                if text:
                    result["semantic_description"] = f"包含'{text}'的输入框"
                    result["expected_action"] = "编辑文本内容"
                    result["interaction_hints"] = "点击后可输入或编辑文本"
                else:
                    result["semantic_description"] = "文本输入框"
                    result["expected_action"] = "输入文本"
                    result["interaction_hints"] = "点击后可输入文本"

            # 文本标签
            elif 'textview' in element_type:
                result["element_category"] = "文本标签"
                if text:
                    result["semantic_description"] = f"显示'{text}'的文本标签"
                    result["expected_action"] = "查看信息"
                    result["interaction_hints"] = "显示信息内容"
                else:
                    result["semantic_description"] = "文本显示区域"
                    result["expected_action"] = "查看内容"
                    result["interaction_hints"] = "显示文本内容"

            # 图片元素
            elif 'imageview' in element_type or 'image' in element_type:
                result["element_category"] = "图片"
                if content_desc:
                    result["semantic_description"] = f"图片: {content_desc}"
                else:
                    result["semantic_description"] = "图片元素"
                result["expected_action"] = "查看图片"
                result["interaction_hints"] = "显示图像内容"

            # 其他可点击元素
            elif element.attributes and element.attributes.clickable:
                result["element_category"] = "可点击元素"
                if text:
                    result["semantic_description"] = f"可点击的'{text}'"
                    result["expected_action"] = f"点击{text}"
                    result["interaction_hints"] = f"点击执行{text}相关功能"
                else:
                    result["semantic_description"] = "可点击元素"
                    result["expected_action"] = "点击操作"
                    result["interaction_hints"] = "点击执行相关功能"

            # 默认情况
            else:
                result["element_category"] = "界面元素"
                if text:
                    result["semantic_description"] = f"显示'{text}'的界面元素"
                else:
                    result["semantic_description"] = "界面元素"
                result["expected_action"] = "查看或交互"
                result["interaction_hints"] = "界面显示元素"

            return result

        except Exception as e:
            logger.error(f"降级分析失败: {e}")
            return {
                "semantic_description": "未知元素",
                "expected_action": "未知操作",
                "element_category": "未知",
                "confidence_score": 0.1,
                "visual_context": "分析失败",
                "interaction_hints": "无法确定交互方式"
            }

    async def enhance_page_elements_with_screenshot(self,
                                                  page: UIPage,
                                                  screenshot_path: str) -> Tuple[int, int]:
        """
        使用截图增强页面中所有元素的语义信息 - 多模态分析

        Args:
            page: UI页面对象
            screenshot_path: 页面截图路径

        Returns:
            Tuple[int, int]: (成功增强的元素数量, 总元素数量)
        """
        try:
            logger.info(f"开始多模态增强页面元素语义: {page.page_name}")

            # 如果AutoGen不可用，使用降级处理
            if not _try_import_autogen() or not self.agent:
                logger.info("AutoGen不可用，使用降级语义分析")
                return self._fallback_enhance_page_elements(page)

            # 准备页面上下文
            page_context = {
                'page_name': page.page_name,
                'activity_name': page.activity_name,
                'package_name': page.package_name
            }

            enhanced_count = 0
            total_elements = len(page.elements)

            # 只增强可操作元素
            actionable_elements = [elem for elem in page.elements if elem.is_actionable()]
            logger.info(f"需要增强的可操作元素: {len(actionable_elements)}")

            # 批量处理元素
            for i, element in enumerate(actionable_elements):
                try:
                    logger.debug(f"多模态增强元素 {i+1}/{len(actionable_elements)}: {element.element_type}")

                    # 使用多模态分析元素语义
                    semantic_result = await self.analyze_element_with_screenshot(
                        element, screenshot_path, page_context
                    )

                    # 更新元素信息
                    self._update_element_with_semantic_result(element, semantic_result)
                    enhanced_count += 1

                except Exception as e:
                    logger.warning(f"多模态增强元素失败: {e}")
                    continue

            logger.info(f"多模态页面元素语义增强完成: {enhanced_count}/{len(actionable_elements)} 个可操作元素")
            return enhanced_count, total_elements

        except Exception as e:
            logger.error(f"多模态页面元素语义增强失败: {e}")
            return 0, len(page.elements) if page.elements else 0

    def _update_element_with_semantic_result(self, element: UIElement, semantic_result: Dict[str, Any]):
        """更新元素的语义信息"""
        element.semantic_description = semantic_result.get('semantic_description', '')
        element.expected_action = semantic_result.get('expected_action', '')
        element.element_category = semantic_result.get('element_category', '')
        element.confidence_score = semantic_result.get('confidence_score', 0.0)
        element.updated_at = datetime.now()

    def _fallback_enhance_page_elements(self, page) -> Tuple[int, int]:
        """
        降级处理：批量增强页面元素

        Args:
            page: UI页面对象

        Returns:
            Tuple[int, int]: (成功增强的元素数量, 总元素数量)
        """
        try:
            logger.info(f"使用降级模式增强页面元素: {page.page_name}")

            enhanced_count = 0
            total_elements = len(page.elements)

            # 只增强可操作元素
            actionable_elements = [elem for elem in page.elements if elem.is_actionable()]

            for element in actionable_elements:
                try:
                    # 使用降级分析
                    semantic_result = self._fallback_element_analysis(element)

                    # 使用统一的更新方法
                    self._update_element_with_semantic_result(element, semantic_result)
                    enhanced_count += 1

                except Exception as e:
                    logger.warning(f"降级增强元素失败: {e}")
                    continue

            logger.info(f"降级模式元素增强完成: {enhanced_count}/{len(actionable_elements)} 个可操作元素")
            return enhanced_count, total_elements

        except Exception as e:
            logger.error(f"降级模式页面元素增强失败: {e}")
            return 0, len(page.elements) if page.elements else 0