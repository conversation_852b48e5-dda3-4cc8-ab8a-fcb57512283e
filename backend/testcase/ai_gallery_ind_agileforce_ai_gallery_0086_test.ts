/**
 * 自动生成的测试脚本
 * 
 * 测试用例ID: IND_Agileforce_AI Gallery_0086
 * 业务模块: 独立产品
 * 生成时间: 2025-09-01 19:05:26
 * 文件描述: 自动化测试脚本 - IND_Agileforce_AI Gallery_0086
 * 
 * 测试步骤:
 * 1.点击图片查看
2.第一次执行返回操作（三键导航返回、边缘手势返回）
3.进行任意操作（点击/滑动）

PS--科普：
<下滑退出引导页只考虑第一次执行返回时的效果>
如果第一次是下滑手势退出成功就不会出现该引导页，后面也不会出现该引导页；
如果第一次是按【返回键】或者按【三键导航中的返回键】退出大图，以及【下滑手势退出大图但没成功】才会在清除缓存后再次出现引导页
 *
 * 预期结果:
 * 1.图片预览界面可以打开查看，底部显示菜单栏，顶部显示日期
2.出现大图引导：下滑也可以退出预览
3.退出引导
（PS：引导仅出现一次，二次验证需要清除数据后再满足前置条件）
 * 
 * 注意: 此文件由AI自动生成，请根据实际情况调整
 */

import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';
import "dotenv/config";

const sleep = (ms: number) => new Promise((r) => setTimeout(r, ms));

async function runImagePreviewTest() {
    try {
        // 1. 设备连接和初始化
        console.log("Connecting to device...");
        const devices = await getConnectedDevices();
        if (devices.length === 0) {
            throw new Error("No connected devices found");
        }
        
        const device = new AndroidDevice(devices[0].udid);
        await device.connect();
        
        // 2. 创建AI智能体并配置上下文
        const agent = new AndroidAgent(device, {
            aiActionContext: 'Handle any permission popups or system dialogs that may appear during testing. Close any unexpected popups.'
        });

        // 3. 启动应用 (需要替换为实际包名)
        console.log("Launching application...");
        await device.launch('com.gallery20');

        // 4. 测试步骤1: 点击图片查看
        console.log("Opening image preview...");
        await agent.aiAction('点击第一张图片打开预览');
        await agent.aiWaitFor("图片预览界面完全加载");
        
        // 验证图片预览界面元素
        await agent.aiAssert("顶部显示日期信息");
        await agent.aiAssert("底部显示菜单栏");
        
        // 5. 测试步骤2: 验证引导提示
        console.log("Checking for guide prompt...");
        await agent.aiAssert("显示'下滑也可以退出预览'的引导提示");

        // 6. 测试步骤3: 第一次返回操作 (使用三键导航返回)
        console.log("Performing first back operation...");
        await agent.aiAction('点击三键导航中的返回键退出预览');
        await agent.aiWaitFor("返回图片列表视图");

        // 7. 测试步骤4: 再次打开图片并尝试其他返回方式
        console.log("Re-opening image preview for second test...");
        await agent.aiAction('点击第一张图片打开预览');
        await agent.aiWaitFor("图片预览界面完全加载");

        // 验证是否不再显示引导提示
        console.log("Verifying guide prompt is no longer shown...");
        try {
            await agent.aiAssert("不显示'下滑也可以退出预览'的引导提示", { timeout: 2000 });
        } catch (e) {
            console.error("引导提示仍然显示，不符合预期");
            throw e;
        }

        // 尝试手势返回
        console.log("Attempting swipe gesture to exit...");
        await agent.aiAction('从屏幕顶部向下滑动退出预览');
        await agent.aiWaitFor("返回图片列表视图");

        // 8. 清理: 清除应用数据以重置状态
        console.log("Clearing app data for next test...");
        await device.clearAppData('com.example.galleryapp');

        console.log("Test completed successfully!");
    } catch (error) {
        console.error("Test failed:", error);
        throw error;
    } finally {
        // 确保设备断开连接
        await device?.disconnect();
    }
}

// 执行测试
runImagePreviewTest().catch(console.error);