[2025-09-01T16:53:50.946+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-01T16:53:50.946+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-01T16:53:50.946+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T16:53:50.946+08:00] provider has no specific model SDK declared
[2025-09-01T17:22:26.046+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-01T17:22:26.046+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-01T17:22:26.047+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:22:26.047+08:00] provider has no specific model SDK declared
[2025-09-01T17:22:26.047+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:22:26.047+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-01T17:22:26.047+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-01T17:22:26.047+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:22:26.047+08:00] provider has no specific model SDK declared
[2025-09-01T17:22:26.047+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:22:26.047+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:22:26.047+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-01T17:22:26.047+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:22:26.047+08:00] provider has no specific model SDK declared
[2025-09-01T17:22:26.047+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:22:26.047+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:22:26.047+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-01T17:22:26.048+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:22:26.048+08:00] provider has no specific model SDK declared
[2025-09-01T17:22:26.048+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:23:44.360+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-01T17:23:44.361+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-01T17:23:44.361+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:23:44.361+08:00] provider has no specific model SDK declared
[2025-09-01T17:23:44.361+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:23:44.361+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-01T17:23:44.361+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-01T17:23:44.361+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:23:44.361+08:00] provider has no specific model SDK declared
[2025-09-01T17:23:44.361+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:23:44.361+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:23:44.361+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-01T17:23:44.362+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:23:44.362+08:00] provider has no specific model SDK declared
[2025-09-01T17:23:44.362+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:23:44.362+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:23:44.362+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-01T17:23:44.362+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:23:44.362+08:00] provider has no specific model SDK declared
[2025-09-01T17:23:44.362+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:26:13.006+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-01T17:26:13.006+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-01T17:26:13.006+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:26:13.006+08:00] provider has no specific model SDK declared
[2025-09-01T17:26:13.007+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:26:13.007+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-01T17:26:13.007+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-01T17:26:13.007+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:26:13.007+08:00] provider has no specific model SDK declared
[2025-09-01T17:26:13.007+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:26:13.007+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:26:13.007+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-01T17:26:13.007+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:26:13.007+08:00] provider has no specific model SDK declared
[2025-09-01T17:26:13.007+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:26:13.007+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:26:13.007+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-01T17:26:13.007+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:26:13.007+08:00] provider has no specific model SDK declared
[2025-09-01T17:26:13.007+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:27:13.477+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-01T17:27:13.477+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-01T17:27:13.478+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:27:13.478+08:00] provider has no specific model SDK declared
[2025-09-01T17:27:13.478+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:27:13.478+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-01T17:27:13.478+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-01T17:27:13.478+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:27:13.478+08:00] provider has no specific model SDK declared
[2025-09-01T17:27:13.478+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:27:13.478+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:27:13.478+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-01T17:27:13.478+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:27:13.478+08:00] provider has no specific model SDK declared
[2025-09-01T17:27:13.478+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:27:13.478+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:27:13.478+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-01T17:27:13.478+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:27:13.478+08:00] provider has no specific model SDK declared
[2025-09-01T17:27:13.479+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:28:22.968+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-01T17:28:22.968+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-01T17:28:22.969+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:28:22.969+08:00] provider has no specific model SDK declared
[2025-09-01T17:28:22.969+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:28:22.969+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-01T17:28:22.969+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-01T17:28:22.969+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:28:22.970+08:00] provider has no specific model SDK declared
[2025-09-01T17:28:22.970+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:28:22.970+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:28:22.970+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-01T17:28:22.970+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:28:22.970+08:00] provider has no specific model SDK declared
[2025-09-01T17:28:22.970+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:28:22.970+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:28:22.970+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-01T17:28:22.970+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:28:22.970+08:00] provider has no specific model SDK declared
[2025-09-01T17:28:22.970+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:35:50.149+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-01T17:35:50.149+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-01T17:35:50.149+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:35:50.149+08:00] provider has no specific model SDK declared
[2025-09-01T17:35:50.149+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:35:50.149+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-01T17:35:50.150+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-01T17:35:50.150+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:35:50.150+08:00] provider has no specific model SDK declared
[2025-09-01T17:35:50.150+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:35:50.150+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:35:50.150+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-01T17:35:50.150+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:35:50.150+08:00] provider has no specific model SDK declared
[2025-09-01T17:35:50.150+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:35:50.150+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:35:50.150+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-01T17:35:50.150+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:35:50.150+08:00] provider has no specific model SDK declared
[2025-09-01T17:35:50.150+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:56:52.856+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-01T17:56:52.856+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-01T17:56:52.857+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:56:52.857+08:00] provider has no specific model SDK declared
[2025-09-01T17:56:52.857+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:56:52.857+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-01T17:56:52.857+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-01T17:56:52.857+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:56:52.857+08:00] provider has no specific model SDK declared
[2025-09-01T17:56:52.857+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:56:52.857+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:56:52.857+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-01T17:56:52.857+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:56:52.857+08:00] provider has no specific model SDK declared
[2025-09-01T17:56:52.857+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:56:52.857+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:56:52.857+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-01T17:56:52.857+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:56:52.858+08:00] provider has no specific model SDK declared
[2025-09-01T17:56:52.858+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:57:23.476+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-01T17:57:23.476+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-01T17:57:23.477+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:57:23.477+08:00] provider has no specific model SDK declared
[2025-09-01T17:57:23.477+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:57:23.477+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-01T17:57:23.477+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-01T17:57:23.477+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:57:23.477+08:00] provider has no specific model SDK declared
[2025-09-01T17:57:23.477+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:57:23.477+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:57:23.477+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-01T17:57:23.478+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:57:23.478+08:00] provider has no specific model SDK declared
[2025-09-01T17:57:23.478+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:57:23.478+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:57:23.478+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-01T17:57:23.478+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:57:23.478+08:00] provider has no specific model SDK declared
[2025-09-01T17:57:23.478+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:59:56.495+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-01T17:59:56.495+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-01T17:59:56.495+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:59:56.495+08:00] provider has no specific model SDK declared
[2025-09-01T17:59:56.495+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_*******************************704',
  openaiExtraConfig: undefined,
  modelName: 'qwen-vl-max-latest',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-09-01T17:59:56.496+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig qwen-vl-max-latest
[2025-09-01T17:59:56.496+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-01T17:59:56.496+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:59:56.496+08:00] provider has no specific model SDK declared
[2025-09-01T17:59:56.496+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_*******************************704',
  openaiExtraConfig: undefined,
  modelName: 'qwen-vl-max-latest',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-09-01T17:59:56.496+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:59:56.496+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-01T17:59:56.496+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:59:56.496+08:00] provider has no specific model SDK declared
[2025-09-01T17:59:56.496+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_*******************************704',
  openaiExtraConfig: undefined,
  modelName: 'qwen-vl-max-latest',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-09-01T17:59:56.496+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:59:56.496+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-01T17:59:56.496+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:59:56.496+08:00] provider has no specific model SDK declared
[2025-09-01T17:59:56.496+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_*******************************704',
  openaiExtraConfig: undefined,
  modelName: 'qwen-vl-max-latest',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-09-01T18:01:00.398+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-01T18:01:00.398+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-01T18:01:00.398+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T18:01:00.398+08:00] provider has no specific model SDK declared
[2025-09-01T18:01:00.399+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_*******************************704',
  openaiExtraConfig: undefined,
  modelName: 'qwen-vl-max',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-09-01T18:01:00.399+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig qwen-vl-max
[2025-09-01T18:01:00.399+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-01T18:01:00.399+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T18:01:00.399+08:00] provider has no specific model SDK declared
[2025-09-01T18:01:00.399+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_*******************************704',
  openaiExtraConfig: undefined,
  modelName: 'qwen-vl-max',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-09-01T18:01:00.399+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-01T18:01:00.399+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-01T18:01:00.399+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T18:01:00.399+08:00] provider has no specific model SDK declared
[2025-09-01T18:01:00.399+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_*******************************704',
  openaiExtraConfig: undefined,
  modelName: 'qwen-vl-max',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-09-01T18:01:00.399+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-01T18:01:00.399+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-01T18:01:00.399+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T18:01:00.399+08:00] provider has no specific model SDK declared
[2025-09-01T18:01:00.399+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_*******************************704',
  openaiExtraConfig: undefined,
  modelName: 'qwen-vl-max',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-09-01T18:16:43.290+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-01T18:16:43.291+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-01T18:16:43.292+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T18:16:43.292+08:00] provider has no specific model SDK declared
[2025-09-01T18:16:43.293+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_*******************************704',
  openaiExtraConfig: undefined,
  modelName: 'gemini-2.5-pro',
  vlMode: 'gemini',
  uiTarsVersion: undefined,
  modelDescription: 'gemini mode',
  from: 'legacy-env'
}
[2025-09-01T18:16:43.293+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig gemini-2.5-pro
[2025-09-01T18:16:43.293+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-01T18:16:43.293+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T18:16:43.293+08:00] provider has no specific model SDK declared
[2025-09-01T18:16:43.293+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_*******************************704',
  openaiExtraConfig: undefined,
  modelName: 'gemini-2.5-pro',
  vlMode: 'gemini',
  uiTarsVersion: undefined,
  modelDescription: 'gemini mode',
  from: 'legacy-env'
}
[2025-09-01T18:16:43.294+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-01T18:16:43.294+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-01T18:16:43.294+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T18:16:43.294+08:00] provider has no specific model SDK declared
[2025-09-01T18:16:43.294+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_*******************************704',
  openaiExtraConfig: undefined,
  modelName: 'gemini-2.5-pro',
  vlMode: 'gemini',
  uiTarsVersion: undefined,
  modelDescription: 'gemini mode',
  from: 'legacy-env'
}
[2025-09-01T18:16:43.295+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-01T18:16:43.295+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-01T18:16:43.295+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T18:16:43.295+08:00] provider has no specific model SDK declared
[2025-09-01T18:16:43.295+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_*******************************704',
  openaiExtraConfig: undefined,
  modelName: 'gemini-2.5-pro',
  vlMode: 'gemini',
  uiTarsVersion: undefined,
  modelDescription: 'gemini mode',
  from: 'legacy-env'
}
[2025-09-01T18:19:30.766+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-01T18:19:30.766+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-01T18:19:30.767+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T18:19:30.767+08:00] provider has no specific model SDK declared
[2025-09-01T18:19:30.768+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_*******************************704',
  openaiExtraConfig: undefined,
  modelName: 'qwen-vl-max',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-09-01T18:19:30.768+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig qwen-vl-max
[2025-09-01T18:19:30.768+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-01T18:19:30.769+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T18:19:30.769+08:00] provider has no specific model SDK declared
[2025-09-01T18:19:30.769+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_*******************************704',
  openaiExtraConfig: undefined,
  modelName: 'qwen-vl-max',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-09-01T18:19:30.769+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-01T18:19:30.769+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-01T18:19:30.770+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T18:19:30.770+08:00] provider has no specific model SDK declared
[2025-09-01T18:19:30.770+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_*******************************704',
  openaiExtraConfig: undefined,
  modelName: 'qwen-vl-max',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-09-01T18:19:30.770+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-01T18:19:30.770+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-01T18:19:30.771+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T18:19:30.771+08:00] provider has no specific model SDK declared
[2025-09-01T18:19:30.771+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_*******************************704',
  openaiExtraConfig: undefined,
  modelName: 'qwen-vl-max',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-09-01T18:20:05.606+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-01T18:20:05.606+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-01T18:20:05.606+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T18:20:05.606+08:00] provider has no specific model SDK declared
[2025-09-01T18:20:05.607+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T18:20:05.607+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-01T18:20:05.607+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-01T18:20:05.607+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T18:20:05.607+08:00] provider has no specific model SDK declared
[2025-09-01T18:20:05.607+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T18:20:05.607+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-01T18:20:05.607+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-01T18:20:05.607+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T18:20:05.607+08:00] provider has no specific model SDK declared
[2025-09-01T18:20:05.607+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T18:20:05.607+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-01T18:20:05.607+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-01T18:20:05.608+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T18:20:05.608+08:00] provider has no specific model SDK declared
[2025-09-01T18:20:05.608+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T19:17:55.806+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-01T19:17:55.806+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-01T19:17:55.808+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T19:17:55.808+08:00] provider has no specific model SDK declared
[2025-09-01T19:17:55.811+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T19:17:55.811+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-01T19:17:55.811+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-01T19:17:55.812+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T19:17:55.812+08:00] provider has no specific model SDK declared
[2025-09-01T19:17:55.812+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T19:17:55.812+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-01T19:17:55.812+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-01T19:17:55.813+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T19:17:55.813+08:00] provider has no specific model SDK declared
[2025-09-01T19:17:55.813+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T19:17:55.813+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-01T19:17:55.813+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-01T19:17:55.814+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T19:17:55.814+08:00] provider has no specific model SDK declared
[2025-09-01T19:17:55.814+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T19:28:59.347+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-01T19:28:59.347+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-01T19:28:59.350+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T19:28:59.350+08:00] provider has no specific model SDK declared
[2025-09-01T19:28:59.353+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T19:28:59.353+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-01T19:28:59.353+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-01T19:28:59.354+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T19:28:59.354+08:00] provider has no specific model SDK declared
[2025-09-01T19:28:59.354+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T19:28:59.355+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-01T19:28:59.355+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-01T19:28:59.356+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T19:28:59.356+08:00] provider has no specific model SDK declared
[2025-09-01T19:28:59.356+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T19:28:59.356+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-01T19:28:59.356+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-01T19:28:59.357+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T19:28:59.357+08:00] provider has no specific model SDK declared
[2025-09-01T19:28:59.358+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T19:35:31.779+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-01T19:35:31.779+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-01T19:35:31.781+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T19:35:31.781+08:00] provider has no specific model SDK declared
[2025-09-01T19:35:31.782+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T19:35:31.782+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-01T19:35:31.782+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-01T19:35:31.782+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T19:35:31.782+08:00] provider has no specific model SDK declared
[2025-09-01T19:35:31.783+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T19:35:31.783+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-01T19:35:31.783+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-01T19:35:31.784+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T19:35:31.784+08:00] provider has no specific model SDK declared
[2025-09-01T19:35:31.784+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T19:35:31.784+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-01T19:35:31.784+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-01T19:35:31.784+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T19:35:31.785+08:00] provider has no specific model SDK declared
[2025-09-01T19:35:31.785+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T10:57:51.509+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-10T10:57:51.509+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-10T10:57:51.510+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T10:57:51.510+08:00] provider has no specific model SDK declared
[2025-09-10T10:57:51.510+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T10:57:51.510+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-10T10:57:51.510+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-10T10:57:51.510+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T10:57:51.510+08:00] provider has no specific model SDK declared
[2025-09-10T10:57:51.510+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T10:57:51.510+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-10T10:57:51.510+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-10T10:57:51.510+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T10:57:51.510+08:00] provider has no specific model SDK declared
[2025-09-10T10:57:51.510+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T10:57:51.510+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-10T10:57:51.510+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-10T10:57:51.510+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T10:57:51.510+08:00] provider has no specific model SDK declared
[2025-09-10T10:57:51.510+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:01:14.078+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-10T11:01:14.078+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-10T11:01:14.078+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:01:14.078+08:00] provider has no specific model SDK declared
[2025-09-10T11:01:14.079+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:01:14.079+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-10T11:01:14.079+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-10T11:01:14.079+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:01:14.079+08:00] provider has no specific model SDK declared
[2025-09-10T11:01:14.079+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:01:14.079+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-10T11:01:14.079+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-10T11:01:14.079+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:01:14.079+08:00] provider has no specific model SDK declared
[2025-09-10T11:01:14.079+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:01:14.079+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-10T11:01:14.079+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-10T11:01:14.079+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:01:14.079+08:00] provider has no specific model SDK declared
[2025-09-10T11:01:14.079+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:02:02.660+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-10T11:02:02.660+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-10T11:02:02.660+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:02:02.660+08:00] provider has no specific model SDK declared
[2025-09-10T11:02:02.660+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:02:02.660+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-10T11:02:02.660+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-10T11:02:02.661+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:02:02.661+08:00] provider has no specific model SDK declared
[2025-09-10T11:02:02.661+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:02:02.661+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-10T11:02:02.661+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-10T11:02:02.661+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:02:02.661+08:00] provider has no specific model SDK declared
[2025-09-10T11:02:02.661+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:02:02.661+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-10T11:02:02.661+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-10T11:02:02.661+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:02:02.661+08:00] provider has no specific model SDK declared
[2025-09-10T11:02:02.661+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:03:02.735+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-10T11:03:02.735+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-10T11:03:02.736+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:03:02.736+08:00] provider has no specific model SDK declared
[2025-09-10T11:03:02.736+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:03:02.736+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-10T11:03:02.736+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-10T11:03:02.736+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:03:02.736+08:00] provider has no specific model SDK declared
[2025-09-10T11:03:02.736+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:03:02.736+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-10T11:03:02.736+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-10T11:03:02.736+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:03:02.736+08:00] provider has no specific model SDK declared
[2025-09-10T11:03:02.736+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:03:02.736+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-10T11:03:02.736+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-10T11:03:02.736+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:03:02.736+08:00] provider has no specific model SDK declared
[2025-09-10T11:03:02.736+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:03:26.798+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-10T11:03:26.798+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-10T11:03:26.798+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:03:26.798+08:00] provider has no specific model SDK declared
[2025-09-10T11:03:26.799+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:03:26.799+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-10T11:03:26.799+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-10T11:03:26.799+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:03:26.799+08:00] provider has no specific model SDK declared
[2025-09-10T11:03:26.799+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:03:26.799+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-10T11:03:26.799+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-10T11:03:26.799+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:03:26.799+08:00] provider has no specific model SDK declared
[2025-09-10T11:03:26.799+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:03:26.799+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-10T11:03:26.799+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-10T11:03:26.799+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:03:26.799+08:00] provider has no specific model SDK declared
[2025-09-10T11:03:26.799+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:04:35.163+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-10T11:04:35.163+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-10T11:04:35.163+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:04:35.163+08:00] provider has no specific model SDK declared
[2025-09-10T11:04:35.164+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:04:35.164+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-10T11:04:35.164+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-10T11:04:35.164+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:04:35.164+08:00] provider has no specific model SDK declared
[2025-09-10T11:04:35.164+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:04:35.164+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-10T11:04:35.164+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-10T11:04:35.164+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:04:35.164+08:00] provider has no specific model SDK declared
[2025-09-10T11:04:35.164+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:04:35.164+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-10T11:04:35.164+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-10T11:04:35.164+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:04:35.164+08:00] provider has no specific model SDK declared
[2025-09-10T11:04:35.164+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:10:35.930+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-10T11:10:35.931+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-10T11:10:35.931+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:10:35.931+08:00] provider has no specific model SDK declared
[2025-09-10T11:10:35.931+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:10:35.931+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-10T11:10:35.931+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-10T11:10:35.931+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:10:35.931+08:00] provider has no specific model SDK declared
[2025-09-10T11:10:35.931+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:10:35.931+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-10T11:10:35.931+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-10T11:10:35.932+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:10:35.932+08:00] provider has no specific model SDK declared
[2025-09-10T11:10:35.932+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T11:10:35.932+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-10T11:10:35.932+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-10T11:10:35.932+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T11:10:35.932+08:00] provider has no specific model SDK declared
[2025-09-10T11:10:35.932+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:06:29.658+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-10T12:06:29.658+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-10T12:06:29.659+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:06:29.659+08:00] provider has no specific model SDK declared
[2025-09-10T12:06:29.659+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:06:29.659+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-10T12:06:29.659+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-10T12:06:29.659+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:06:29.659+08:00] provider has no specific model SDK declared
[2025-09-10T12:06:29.659+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:06:29.659+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-10T12:06:29.659+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-10T12:06:29.659+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:06:29.659+08:00] provider has no specific model SDK declared
[2025-09-10T12:06:29.659+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:06:29.659+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-10T12:06:29.659+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-10T12:06:29.659+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:06:29.659+08:00] provider has no specific model SDK declared
[2025-09-10T12:06:29.659+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:10:54.490+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-10T12:10:54.490+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-10T12:10:54.490+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:10:54.490+08:00] provider has no specific model SDK declared
[2025-09-10T12:10:54.490+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:10:54.490+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-10T12:10:54.490+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-10T12:10:54.490+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:10:54.490+08:00] provider has no specific model SDK declared
[2025-09-10T12:10:54.491+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:10:54.491+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-10T12:10:54.491+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-10T12:10:54.491+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:10:54.491+08:00] provider has no specific model SDK declared
[2025-09-10T12:10:54.491+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:10:54.491+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-10T12:10:54.491+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-10T12:10:54.491+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:10:54.491+08:00] provider has no specific model SDK declared
[2025-09-10T12:10:54.491+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:11:12.580+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-10T12:11:12.580+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-10T12:11:12.580+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:11:12.580+08:00] provider has no specific model SDK declared
[2025-09-10T12:11:12.580+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:11:12.580+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-10T12:11:12.580+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-10T12:11:12.580+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:11:12.580+08:00] provider has no specific model SDK declared
[2025-09-10T12:11:12.580+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:11:12.580+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-10T12:11:12.580+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-10T12:11:12.581+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:11:12.581+08:00] provider has no specific model SDK declared
[2025-09-10T12:11:12.581+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:11:12.581+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-10T12:11:12.581+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-10T12:11:12.581+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:11:12.581+08:00] provider has no specific model SDK declared
[2025-09-10T12:11:12.581+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:13:12.954+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-10T12:13:12.954+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-10T12:13:12.954+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:13:12.954+08:00] provider has no specific model SDK declared
[2025-09-10T12:13:12.955+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:13:12.955+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-10T12:13:12.955+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-10T12:13:12.955+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:13:12.955+08:00] provider has no specific model SDK declared
[2025-09-10T12:13:12.955+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:13:12.955+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-10T12:13:12.955+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-10T12:13:12.955+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:13:12.955+08:00] provider has no specific model SDK declared
[2025-09-10T12:13:12.955+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:13:12.955+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-10T12:13:12.955+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-10T12:13:12.955+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:13:12.955+08:00] provider has no specific model SDK declared
[2025-09-10T12:13:12.955+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:15:55.721+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-10T12:15:55.722+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-10T12:15:55.722+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:15:55.722+08:00] provider has no specific model SDK declared
[2025-09-10T12:15:55.722+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:15:55.722+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-10T12:15:55.722+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-10T12:15:55.722+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:15:55.722+08:00] provider has no specific model SDK declared
[2025-09-10T12:15:55.722+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:15:55.722+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-10T12:15:55.722+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-10T12:15:55.723+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:15:55.723+08:00] provider has no specific model SDK declared
[2025-09-10T12:15:55.723+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:15:55.723+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-10T12:15:55.723+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-10T12:15:55.723+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:15:55.723+08:00] provider has no specific model SDK declared
[2025-09-10T12:15:55.723+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:20:01.698+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-10T12:20:01.698+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-10T12:20:01.698+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:20:01.698+08:00] provider has no specific model SDK declared
[2025-09-10T12:20:01.698+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:20:01.698+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-10T12:20:01.698+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-10T12:20:01.698+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:20:01.698+08:00] provider has no specific model SDK declared
[2025-09-10T12:20:01.698+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:20:01.699+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-10T12:20:01.699+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-10T12:20:01.699+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:20:01.699+08:00] provider has no specific model SDK declared
[2025-09-10T12:20:01.699+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:20:01.699+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-10T12:20:01.699+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-10T12:20:01.699+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:20:01.699+08:00] provider has no specific model SDK declared
[2025-09-10T12:20:01.699+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:22:49.848+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-10T12:22:49.848+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-10T12:22:49.848+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:22:49.848+08:00] provider has no specific model SDK declared
[2025-09-10T12:22:49.848+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:22:49.848+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-10T12:22:49.848+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-10T12:22:49.848+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:22:49.848+08:00] provider has no specific model SDK declared
[2025-09-10T12:22:49.848+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:22:49.848+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-10T12:22:49.848+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-10T12:22:49.849+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:22:49.849+08:00] provider has no specific model SDK declared
[2025-09-10T12:22:49.849+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T12:22:49.849+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-10T12:22:49.849+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-10T12:22:49.849+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T12:22:49.849+08:00] provider has no specific model SDK declared
[2025-09-10T12:22:49.849+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T14:51:35.909+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-10T14:51:35.909+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-10T14:51:35.909+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T14:51:35.909+08:00] provider has no specific model SDK declared
[2025-09-10T14:51:35.910+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T14:51:35.910+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-10T14:51:35.910+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-10T14:51:35.910+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T14:51:35.910+08:00] provider has no specific model SDK declared
[2025-09-10T14:51:35.910+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T14:51:35.910+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-10T14:51:35.910+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-10T14:51:35.910+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T14:51:35.910+08:00] provider has no specific model SDK declared
[2025-09-10T14:51:35.910+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-10T14:51:35.910+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-10T14:51:35.910+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-10T14:51:35.910+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-10T14:51:35.910+08:00] provider has no specific model SDK declared
[2025-09-10T14:51:35.910+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
