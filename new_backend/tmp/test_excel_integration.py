#!/usr/bin/env python3
"""
测试Excel集成功能
测试模块包映射和Excel用例读取的完整流程
"""
import os
import sys
import asyncio
from pathlib import Path
from loguru import logger

# 添加相对路径导入
sys.path.append(str(Path(__file__).parent.parent))

from utils.module_package_mapper import ModulePackageMapper
from utils.excel_case_reader import ExcelCaseReader
from agents.batch_processing_agent import BatchProcessingAgent


async def test_module_package_mapping():
    """测试模块包映射功能"""
    try:
        print("="*80)
        print("测试模块包映射功能")
        print("="*80)
        
        # 创建映射管理器
        mapper = ModulePackageMapper()
        
        # 测试从Excel读取包组件映射
        print("\n1. 测试读取包组件映射...")
        components = mapper.read_package_components_from_excel()
        print(f"读取到 {len(components)} 个包组件映射")
        
        # 显示前几个映射
        for i, comp in enumerate(components[:3]):
            print(f"\n映射 {i+1}:")
            print(f"  业务模块: {comp.business_module}")
            print(f"  包名: {comp.package_name}")
            print(f"  组件: {comp.component_name}")
            print(f"  描述: {comp.description}")
        
        # 测试保存到数据库
        print("\n2. 测试保存到数据库...")
        saved_count = mapper.save_package_components_to_db(components)
        print(f"成功保存 {saved_count} 个映射到数据库")
        
        # 测试查询功能
        print("\n3. 测试查询功能...")
        stats = mapper.get_mapping_statistics()
        print(f"统计信息: {stats}")
        
        # 测试根据模块查询包名
        if components:
            test_module = components[0].business_module
            print(f"\n4. 测试查询模块 '{test_module}' 的包信息...")
            packages = mapper.get_package_by_module(test_module)
            print(f"找到 {len(packages)} 个包:")
            for pkg in packages[:3]:
                print(f"  - {pkg['package_name']} ({pkg['component_name']})")
        
        # 清理资源
        mapper.cleanup()
        
        return True
        
    except Exception as e:
        print(f"测试模块包映射失败: {e}")
        return False


async def test_excel_case_reading():
    """测试Excel用例读取功能"""
    try:
        print("\n" + "="*80)
        print("测试Excel用例读取功能")
        print("="*80)
        
        # 创建Excel读取器
        reader = ExcelCaseReader()
        
        # 测试读取Excel文件
        print("\n1. 测试读取Excel用例文件...")
        real_cases = reader.read_test_cases_from_excel()
        print(f"读取到 {len(real_cases)} 个真实用例")
        
        # 显示第一个用例的详细信息
        if real_cases:
            case = real_cases[0]
            print(f"\n第一个用例详情:")
            print(f"  ID: {case.tcid}")
            print(f"  名称: {case.casename}")
            print(f"  模块: {case.group}")
            print(f"  子组: {case.subgroup}")
            print(f"  组件: {case.component}")
            print(f"  类型: {case.type}")
            print(f"  优先级: {case.level}")
            print(f"  目的: {case.purpose}")
            print(f"  前置条件: {case.precondition[:100]}...")
            print(f"  测试步骤: {case.steps[:100]}...")
            print(f"  预期结果: {case.expectresult[:100]}...")
        
        # 测试转换为标准格式
        print("\n2. 测试转换为标准格式...")
        standard_cases = reader.convert_to_standard_format(real_cases)
        print(f"转换为 {len(standard_cases)} 个标准格式用例")
        
        # 显示转换后的第一个用例
        if standard_cases:
            std_case = standard_cases[0]
            print(f"\n转换后的第一个用例:")
            print(f"  ID: {std_case.case_id}")
            print(f"  名称: {std_case.case_name}")
            print(f"  模块: {std_case.module_name}")
            print(f"  包名: {std_case.package_name}")
            print(f"  类型: {std_case.test_type}")
            print(f"  优先级: {std_case.priority}")
        
        # 测试完整处理流程
        print("\n3. 测试完整处理流程...")
        result = reader.process_excel_file()
        print(f"处理结果: {result}")
        
        # 清理资源
        reader.cleanup()
        
        return True
        
    except Exception as e:
        print(f"测试Excel用例读取失败: {e}")
        return False


async def test_batch_processing_integration():
    """测试批处理集成功能"""
    try:
        print("\n" + "="*80)
        print("测试批处理集成功能")
        print("="*80)
        
        # 创建批处理智能体
        batch_agent = BatchProcessingAgent()
        
        # 测试Excel文件处理
        print("\n1. 测试Excel文件批处理...")
        excel_file = Path(__file__).parent.parent / "data" / "case" / "case.xlsx"
        
        if excel_file.exists():
            result = await batch_agent.process_excel_file(str(excel_file))
            print(f"批处理结果: {result}")
            
            if result.get('success'):
                print(f"  - 总用例数: {result.get('total_cases', 0)}")
                print(f"  - 真实用例数: {result.get('real_cases_count', 0)}")
                print(f"  - 工作流ID: {result.get('workflow_id', 'N/A')}")
                print(f"  - 报告文件: {result.get('report_file', 'N/A')}")
            else:
                print(f"  - 错误: {result.get('error', 'Unknown error')}")
        else:
            print(f"Excel文件不存在: {excel_file}")
        
        # 清理资源
        await batch_agent.cleanup()
        
        return True
        
    except Exception as e:
        print(f"测试批处理集成失败: {e}")
        return False


async def main():
    """主函数"""
    try:
        # 设置日志
        logger.remove()
        logger.add(sys.stdout, level="INFO", format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | {message}")
        
        print("开始测试Excel集成功能...")
        
        # 测试1: 模块包映射
        success1 = await test_module_package_mapping()
        
        # 测试2: Excel用例读取
        success2 = await test_excel_case_reading()
        
        # 测试3: 批处理集成
        success3 = await test_batch_processing_integration()
        
        # 总结
        print("\n" + "="*80)
        print("测试总结")
        print("="*80)
        print(f"模块包映射测试: {'✓ 通过' if success1 else '✗ 失败'}")
        print(f"Excel用例读取测试: {'✓ 通过' if success2 else '✗ 失败'}")
        print(f"批处理集成测试: {'✓ 通过' if success3 else '✗ 失败'}")
        
        if all([success1, success2, success3]):
            print("\n🎉 所有测试通过！Excel集成功能正常工作。")
        else:
            print("\n❌ 部分测试失败，请检查错误信息。")
        
    except Exception as e:
        logger.error(f"测试程序执行失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
