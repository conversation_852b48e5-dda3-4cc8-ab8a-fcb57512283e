#!/usr/bin/env python3
"""
测试脚本生成智能体优化
验证自然语言描述和元素库信息的使用
"""
import os
import sys
import asyncio
from pathlib import Path
from loguru import logger

# 添加相对路径导入
sys.path.append(str(Path(__file__).parent.parent))

from models.case_models import TestCase, CaseAnalysisResult
from agents.script_generation_agent import ScriptGenerationAgent


def create_mock_test_case() -> TestCase:
    """创建模拟测试用例"""
    return TestCase(
        case_id="TEST_AI_Gallery_0086",
        case_name="基本功能的大图引导测试",
        test_purpose="验证AI Gallery大图引导功能",
        package_name="com.transsion.aigallery",
        module_name="AI Gallery",
        preconditions="1. 设备已安装AI Gallery应用\n2. 应用首次启动",
        test_steps="""1.点击图片查看
2.第一次执行返回操作（三键导航返回、边缘手势返回）
3.进行任意操作（点击/滑动）
4.验证大图引导显示""",
        expected_results="""1.图片预览界面可以打开查看，底部显示菜单栏，顶部显示日期
2.出现大图引导：下滑也可以退出预览
3.退出引导成功""",
        priority="L1",
        test_type="功能测试"
    )


def create_mock_analysis_result() -> CaseAnalysisResult:
    """创建模拟分析结果"""
    # 模拟提取的操作步骤
    extracted_actions = [
        {
            "step_index": 1,
            "action_type": "click",
            "target_element": "图片缩略图",
            "action_description": "点击图片查看",
            "input_data": None,
            "ai_action_text": "点击图片缩略图进入预览模式"
        },
        {
            "step_index": 2,
            "action_type": "back",
            "target_element": "返回按钮",
            "action_description": "执行返回操作",
            "input_data": None,
            "ai_action_text": "使用返回手势或按钮退出预览"
        },
        {
            "step_index": 3,
            "action_type": "swipe",
            "target_element": "预览区域",
            "action_description": "进行滑动操作",
            "input_data": None,
            "ai_action_text": "在预览区域向下滑动"
        }
    ]
    
    # 模拟提取的断言
    extracted_assertions = [
        {
            "step_index": 1,
            "assertion_type": "page_navigation",
            "target_element": "预览界面",
            "expected_value": "图片预览界面显示",
            "assertion_description": "验证图片预览界面正确显示",
            "ai_assertion_text": "图片预览界面可以打开查看，底部显示菜单栏，顶部显示日期"
        },
        {
            "step_index": 2,
            "assertion_type": "element_exists",
            "target_element": "大图引导",
            "expected_value": "引导提示显示",
            "assertion_description": "验证大图引导显示",
            "ai_assertion_text": "出现大图引导：下滑也可以退出预览"
        }
    ]
    
    # 模拟元素映射信息
    matched_elements = {
        "action_1": {
            "target_description": "图片缩略图",
            "matched_element_id": "image_thumbnail_001",
            "element_type": "ImageView",
            "element_text": "",
            "element_resource_id": "com.transsion.aigallery:id/image_thumbnail",
            "match_confidence": 0.95
        },
        "action_2": {
            "target_description": "返回按钮",
            "matched_element_id": "back_button_001",
            "element_type": "Button",
            "element_text": "返回",
            "element_resource_id": "com.transsion.aigallery:id/back_btn",
            "match_confidence": 0.90
        },
        "action_3": {
            "target_description": "预览区域",
            "matched_element_id": "preview_area_001",
            "element_type": "ViewGroup",
            "element_text": "",
            "element_resource_id": "com.transsion.aigallery:id/preview_container",
            "match_confidence": 0.85
        },
        "assertion_1": {
            "target_description": "预览界面",
            "matched_element_id": "preview_layout_001",
            "element_type": "LinearLayout",
            "element_text": "",
            "element_resource_id": "com.transsion.aigallery:id/preview_layout",
            "match_confidence": 0.88
        },
        "assertion_2": {
            "target_description": "大图引导",
            "matched_element_id": "guide_tooltip_001",
            "element_type": "TextView",
            "element_text": "下滑也可以退出预览",
            "element_resource_id": "com.transsion.aigallery:id/guide_tooltip",
            "match_confidence": 0.92
        }
    }
    
    return CaseAnalysisResult(
        case_id=1,
        analysis_session_id="test_session_001",
        extracted_actions=extracted_actions,
        extracted_assertions=extracted_assertions,
        matched_elements=matched_elements,
        analysis_confidence=0.90,
        completeness_score=0.85,
        clarity_score=0.88,
        analysis_notes="测试用例分析完成，识别了3个操作步骤和2个断言"
    )


async def test_script_generation_optimization():
    """测试脚本生成优化"""
    try:
        print("="*80)
        print("测试脚本生成智能体优化")
        print("="*80)
        
        # 创建脚本生成智能体
        script_agent = ScriptGenerationAgent()
        print("✓ 成功创建脚本生成智能体")
        
        # 创建模拟数据
        test_case = create_mock_test_case()
        analysis_result = create_mock_analysis_result()
        
        print(f"\n测试用例信息:")
        print(f"  - ID: {test_case.case_id}")
        print(f"  - 名称: {test_case.case_name}")
        print(f"  - 包名: {test_case.package_name}")
        print(f"  - 模块: {test_case.module_name}")
        
        print(f"\n分析结果信息:")
        print(f"  - 操作步骤数: {len(analysis_result.extracted_actions)}")
        print(f"  - 断言数: {len(analysis_result.extracted_assertions)}")
        print(f"  - 元素映射数: {len(analysis_result.matched_elements)}")
        print(f"  - 分析置信度: {analysis_result.analysis_confidence}")
        
        # 测试输入准备方法
        print(f"\n1. 测试输入准备方法...")
        generation_input = script_agent._prepare_generation_input(test_case, analysis_result)
        
        print(f"✓ 生成输入长度: {len(generation_input)} 字符")
        
        # 显示输入内容的关键部分
        print(f"\n生成输入内容预览:")
        print("-" * 60)
        lines = generation_input.split('\n')
        for i, line in enumerate(lines[:30]):  # 显示前30行
            print(f"{i+1:2d}: {line}")
        if len(lines) > 30:
            print(f"... (还有 {len(lines) - 30} 行)")
        print("-" * 60)
        
        # 测试自然语言描述生成
        print(f"\n2. 测试自然语言描述生成...")
        
        test_elements = [
            ("登录按钮", "click"),
            ("顶部搜索框", "input"),
            ("底部菜单", "click"),
            ("右上角设置图标", "click"),
            ("页面内容", "swipe")
        ]
        
        for element, action in test_elements:
            natural_desc = script_agent._generate_natural_language_description(element, action)
            print(f"  - {element} + {action} -> {natural_desc}")
        
        # 测试AI断言文本生成
        print(f"\n3. 测试AI断言文本生成...")
        
        test_assertions = [
            ("页面显示登录成功", "用户信息"),
            ("弹窗显示确认信息", "确认对话框"),
            ("搜索结果显示", "商品列表"),
            ("按钮状态变化", "高亮状态")
        ]
        
        for assertion_desc, expected in test_assertions:
            ai_assertion = script_agent._generate_ai_assertion_text(assertion_desc, expected)
            print(f"  - {assertion_desc} + {expected} -> {ai_assertion}")
        
        # 测试元素自然语言描述生成
        print(f"\n4. 测试元素自然语言描述生成...")
        
        for key, mapping in analysis_result.matched_elements.items():
            natural_desc = script_agent._generate_element_natural_description(mapping)
            print(f"  - {key}: {mapping.get('target_description')} -> {natural_desc}")
        
        # 如果有AutoGen环境，测试完整的脚本生成
        print(f"\n5. 测试完整脚本生成...")
        try:
            result = await script_agent.generate_script(test_case, analysis_result)
            
            if result.get('success'):
                script_content = result.get('script_result', {}).get('script_content', '')
                metadata = result.get('script_result', {}).get('script_metadata', {})
                
                print(f"✓ 脚本生成成功")
                print(f"  - 脚本长度: {len(script_content)} 字符")
                print(f"  - 代码行数: {metadata.get('lines_of_code', 0)}")
                print(f"  - 复杂度: {metadata.get('complexity_level', 'unknown')}")
                print(f"  - 使用自然语言: {metadata.get('uses_natural_language', False)}")
                print(f"  - 利用元素映射: {metadata.get('element_mapping_utilized', False)}")
                
                # 显示脚本内容的前几行
                print(f"\n生成的脚本预览:")
                print("-" * 60)
                script_lines = script_content.split('\n')
                for i, line in enumerate(script_lines[:20]):
                    print(f"{i+1:2d}: {line}")
                if len(script_lines) > 20:
                    print(f"... (还有 {len(script_lines) - 20} 行)")
                print("-" * 60)
                
            else:
                print(f"⚠ 脚本生成失败: {result.get('error', 'Unknown error')}")
                print("  (可能是因为AutoGen环境未配置)")
                
        except Exception as e:
            print(f"⚠ 脚本生成测试跳过: {e}")
        
        # 清理资源
        await script_agent.cleanup()
        print(f"\n✓ 资源清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


async def main():
    """主函数"""
    try:
        # 设置日志
        logger.remove()
        logger.add(sys.stdout, level="INFO", format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | {message}")
        
        print("开始测试脚本生成智能体优化...")
        
        # 执行测试
        success = await test_script_generation_optimization()
        
        # 总结
        print("\n" + "="*80)
        print("测试总结")
        print("="*80)
        
        if success:
            print("🎉 脚本生成智能体优化测试通过！")
            print("\n主要改进:")
            print("✓ 优化了prompt，强调使用自然语言描述位置")
            print("✓ 增强了输入准备，提供元素库信息和自然语言建议")
            print("✓ 添加了自然语言描述生成辅助方法")
            print("✓ 改进了元素映射信息的利用方式")
            print("✓ 提供了更好的AI操作和断言文本建议")
        else:
            print("❌ 测试失败，请检查错误信息。")
        
    except Exception as e:
        logger.error(f"测试程序执行失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
