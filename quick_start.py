#!/usr/bin/env python3
"""
基于AutoGen的Android自动化脚本生成系统 - 快速启动脚本
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from backend.script_generation_controller import ScriptGenerationController


async def quick_demo():
    """快速演示脚本生成功能"""
    print("🚀 基于AutoGen的Android自动化脚本生成系统")
    print("=" * 60)
    print("本系统使用AutoGen 0.5.7 + Deepseek大模型 + midsence.js框架")
    print("实现从自然语言测试用例到Android自动化脚本的智能生成")
    print("=" * 60)
    
    try:
        # 创建控制器
        print("\n📋 步骤1: 初始化系统...")
        controller = ScriptGenerationController()
        
        # 初始化系统
        success = await controller.initialize_system()
        if not success:
            print("❌ 系统初始化失败，请检查数据库配置")
            return
        
        print("✅ 系统初始化成功")
        
        # 显示统计信息
        stats = controller.get_generation_statistics()
        print(f"   📊 当前系统状态:")
        print(f"      测试用例: {stats.get('total_test_cases', 0)} 个")
        print(f"      已生成脚本: {stats.get('total_generated_scripts', 0)} 个")
        
        # 演示脚本生成
        print("\n📝 步骤2: 演示脚本生成...")
        
        demo_case = """
        测试用例：用户登录功能验证
        
        前置条件：
        1. 应用已正确安装在测试设备上
        2. 测试设备网络连接正常
        3. 已有有效的测试账号
        
        测试步骤：
        1. 启动应用，等待主页面加载完成
        2. 在主页面找到并点击"登录"按钮
        3. 在登录页面的用户名输入框中输入"<EMAIL>"
        4. 在密码输入框中输入"123456"
        5. 点击"确认登录"按钮
        6. 等待登录处理完成
        7. 验证是否成功跳转到用户主页
        8. 检查页面是否显示用户信息或昵称
        
        预期结果：
        1. 登录操作成功完成，无错误提示
        2. 页面成功跳转到用户主页
        3. 用户信息正确显示
        4. 登录状态保持正常
        """
        
        print("正在生成脚本，请稍候...")
        print("(这个过程需要调用AI模型，可能需要1-2分钟)")
        
        result = await controller.generate_script_from_case_description(
            demo_case, 
            business_module="登录模块"
        )
        
        if result.get("success"):
            print("\n✅ 脚本生成成功!")
            print(f"   📁 文件路径: {result['script_file']}")
            print(f"   📝 脚本名称: {result['script_name']}")
            print(f"   🏷️  业务模块: {result['business_module']}")
            print(f"   ⭐ 审查评分: {result['review_score']}/100")
            
            # 显示生成的脚本内容预览
            print(f"\n📄 生成的脚本预览:")
            print("-" * 50)
            
            try:
                with open(result['script_file'], 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    for i, line in enumerate(lines[:20], 1):
                        print(f"{i:2d}: {line.rstrip()}")
                    
                    if len(lines) > 20:
                        print("    ...")
                        print(f"    (完整文件共 {len(lines)} 行)")
                
                print("-" * 50)
                
            except Exception as e:
                print(f"⚠️  无法预览文件: {e}")
            
            # 显示生成过程信息
            metadata = result.get('generation_metadata', {})
            print(f"\n📈 生成过程统计:")
            case_analysis = metadata.get('case_analysis', {})
            print(f"   🔍 分析到的UI元素: {case_analysis.get('elements_found', 0)} 个")
            print(f"   📦 相关组件映射: {case_analysis.get('components_found', 0)} 个")
            
            review_result = metadata.get('review_result', {})
            print(f"   ✅ 审查通过: {'是' if review_result.get('approved', False) else '否'}")
            print(f"   ⚠️  发现问题: {review_result.get('issues_count', 0)} 个")
            
            file_gen = metadata.get('file_generation', {})
            print(f"   💾 文件大小: {file_gen.get('file_size', 0)} 字节")
            print(f"   🗄️  数据库保存: {'成功' if file_gen.get('database_saved', False) else '失败'}")
            
        else:
            print(f"\n❌ 脚本生成失败: {result.get('error')}")
            print("请检查:")
            print("1. 数据库连接是否正常")
            print("2. AI模型API配置是否正确")
            print("3. 网络连接是否稳定")
        
        # 显示使用说明
        print(f"\n📚 使用说明:")
        print(f"1. 命令行工具: python backend/cli_tool.py --help")
        print(f"2. 完整测试: python temp/test_script_generation_system.py")
        print(f"3. 文档说明: docs/基于AutoGen的Android自动化脚本生成系统使用说明.md")
        print(f"4. 生成的脚本位于: testcase/ 目录")
        
        # 显示testcase目录中的文件
        testcase_dir = Path("testcase")
        if testcase_dir.exists():
            ts_files = list(testcase_dir.rglob("*.ts"))
            if ts_files:
                print(f"\n📁 当前已生成的脚本文件 ({len(ts_files)} 个):")
                for i, file_path in enumerate(ts_files[:5], 1):
                    rel_path = file_path.relative_to(project_root)
                    file_size = file_path.stat().st_size
                    print(f"   {i}. {rel_path} ({file_size} 字节)")
                
                if len(ts_files) > 5:
                    print(f"   ... 还有 {len(ts_files) - 5} 个文件")
        
        print(f"\n🎉 演示完成！系统运行正常。")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现异常: {e}")
        print("\n🔧 故障排除建议:")
        print("1. 检查 backend/config.py 中的数据库配置")
        print("2. 确认 example/llms.py 中的API密钥配置")
        print("3. 验证 backend/data/ 目录下的Excel文件")
        print("4. 运行 python temp/test_script_generation_system.py 进行完整测试")
        
        import traceback
        print(f"\n🐛 详细错误信息:")
        traceback.print_exc()


def main():
    """主函数"""
    print("启动快速演示...")
    asyncio.run(quick_demo())


if __name__ == "__main__":
    main()
