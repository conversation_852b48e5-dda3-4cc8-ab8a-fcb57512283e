"""
模块与包映射关系管理器
基于backend/excel_reader.py的逻辑，实现模块包映射的数据库存储和管理
"""
import os
import sys
import re
import pandas as pd
from typing import Dict, Any, Optional, List
from pathlib import Path
from datetime import datetime
from loguru import logger
from dataclasses import dataclass

# 添加相对路径导入
sys.path.append(str(Path(__file__).parent.parent))

from config import config
from utils.database_manager import DatabaseManager


@dataclass
class PackageComponent:
    """包组件映射数据模型"""
    business_module: str  # 业务模块名称
    package_name: str     # 包名
    component_name: str   # 组件名称
    description: str = "" # 描述
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'business_module': self.business_module,
            'package_name': self.package_name,
            'component_name': self.component_name,
            'description': self.description,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }


class ModulePackageMapper:
    """模块包映射管理器"""
    
    def __init__(self):
        """初始化映射管理器"""
        self.config = config
        self.db_manager = DatabaseManager()
        
        # 数据文件路径
        self.data_dir = Path(__file__).parent.parent / "data"
        self.package_file = self.data_dir / "business_knowledge" / "package_component.xlsx"
        
        logger.info("模块包映射管理器初始化完成")
    
    def _safe_str(self, value, default: str = "") -> str:
        """安全转换为字符串，处理NaN和None值"""
        if pd.isna(value) or value is None:
            return default
        str_value = str(value).strip()
        return str_value if str_value else default
    
    def _extract_package_name(self, raw_value) -> str:
        """
        从原始值中提取纯英文包名
        处理包含中文描述的混合格式
        
        Args:
            raw_value: 原始值，可能包含中文描述
            
        Returns:
            str: 提取的纯英文包名
        """
        if pd.isna(raw_value) or not str(raw_value).strip():
            return ""
        
        text = str(raw_value).strip()
        
        # 跳过明显的无效值
        if text in ['/', '-', 'N/A', '无', '暂无', '待定']:
            return ""
        
        # 正则表达式匹配包名模式（按优先级排序）
        patterns = [
            # 括号内的包名：（com.xxx.xxx）或(com.xxx.xxx)
            r'[（(]([a-zA-Z][a-zA-Z0-9._]+)[）)]',
            # 标准包名格式：com.xxx.xxx
            r'(com\.[a-zA-Z0-9._]+)',
            # Android系统包名
            r'(android\.[a-zA-Z0-9._]+)',
            # 其他标准包名格式
            r'([a-zA-Z][a-zA-Z0-9]*\.[a-zA-Z0-9._]+)',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                # 返回最长的匹配（通常是最完整的包名）
                package_name = max(matches, key=len)
                # 验证包名格式
                if re.match(r'^[a-zA-Z][a-zA-Z0-9._]*$', package_name):
                    return package_name
        
        # 如果没有匹配到，检查整个字符串是否是纯英文包名
        if re.match(r'^[a-zA-Z][a-zA-Z0-9._-]+$', text):
            return text
        
        return ""
    
    def _extract_english_from_description(self, description) -> str:
        """
        从描述字段中提取英文作为组件名
        
        Args:
            description: 描述字段，可能包含中英文混合
            
        Returns:
            str: 提取的英文组件名
        """
        if pd.isna(description) or not str(description).strip():
            return "未知组件"
        
        text = str(description).strip()
        
        # 跳过明显的无效值
        if text in ['/', '-', 'N/A', '无', '暂无', '待定']:
            return "未知组件"
        
        # 提取英文的正则表达式模式（按优先级排序）
        patterns = [
            # 括号内的英文：（English）或(English)
            r'[（(]([a-zA-Z][a-zA-Z0-9\s_-]*)[）)]',
            # 英文单词开头的部分（处理混合格式如 "Launcher启动器"）
            r'^([a-zA-Z][a-zA-Z0-9_-]*)',
            # PascalCase 英文单词
            r'\b([A-Z][a-zA-Z0-9]*(?:[A-Z][a-zA-Z0-9]*)*)\b',
            # 一般英文单词
            r'\b([a-zA-Z][a-zA-Z0-9_-]{2,})\b',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            if matches:
                # 返回最长的匹配
                english_name = max(matches, key=len).strip()
                # 验证是否为有效的英文组件名
                if re.match(r'^[a-zA-Z][a-zA-Z0-9_-]*$', english_name) and len(english_name) >= 2:
                    return english_name
        
        # 如果整个字符串都是英文，直接返回（移除空格）
        if re.match(r'^[a-zA-Z][a-zA-Z0-9\s_-]+$', text):
            # 对于连字符格式，取第一个部分
            if '-' in text:
                return text.split('-')[0].strip()
            return re.sub(r'\s+', '', text)
        
        return "未知组件"
    
    def _extract_component_name(self, component_value, description_value) -> str:
        """
        提取组件名，优先使用 component_value，
        如果为空则从 description_value 中提取英文
        
        Args:
            component_value: 原始组件名字段值
            description_value: 描述字段值
            
        Returns:
            str: 最终的组件名
        """
        # 首先尝试使用原始的 component_value
        if not pd.isna(component_value) and str(component_value).strip():
            component = str(component_value).strip()
            if component not in ['', '未知组件', 'N/A', '/', '-']:
                return component
        
        # 如果 component_value 无效，从 description 中提取英文
        return self._extract_english_from_description(description_value)
    
    def read_package_components_from_excel(self, excel_file_path: str = None) -> List[PackageComponent]:
        """
        从Excel文件读取包组件映射数据
        
        Args:
            excel_file_path: Excel文件路径，如果为None则使用默认路径
            
        Returns:
            List[PackageComponent]: 包组件映射列表
        """
        try:
            if excel_file_path is None:
                excel_file_path = self.package_file
            else:
                excel_file_path = Path(excel_file_path)
            
            if not excel_file_path.exists():
                logger.error(f"包组件映射文件不存在: {excel_file_path}")
                return []
            
            logger.info(f"读取包组件映射文件: {excel_file_path}")
            
            # 读取Excel文件
            df = pd.read_excel(excel_file_path, engine='openpyxl')
            
            # 打印列名以便调试
            logger.info(f"Excel列名: {df.columns.tolist()}")
            
            components = []
            for index, row in df.iterrows():
                try:
                    # 优化的字段映射：使用 subcomponent 作为业务模块名称
                    business_module = self._safe_str(
                        row.get('subcomponent', row.get('子模块 【英文（中文）】subcomponent')),
                        '未知模块'
                    )
                    
                    # 提取纯英文包名
                    package_name = self._extract_package_name(row.get('包名'))
                    
                    # 优化的组件名提取：优先使用 Component，否则从描述中提取英文
                    component_name = self._extract_component_name(
                        row.get('Component'),
                        row.get('子模块 【英文（中文）】subcomponent')
                    )
                    
                    description = self._safe_str(
                        row.get('子模块 【英文（中文）】subcomponent',
                        row.get('模块【英文（中文）】')),
                        ''
                    )
                    
                    # 跳过空数据行（避免保存无意义的数据）
                    if business_module == '未知模块' and not package_name:
                        continue
                    
                    # 跳过无效包名的行
                    if not package_name:
                        logger.debug(f"第{index+1}行跳过：无效包名 '{row.get('包名')}'")
                        continue
                    
                    component = PackageComponent(
                        business_module=business_module,
                        package_name=package_name,
                        component_name=component_name,
                        description=description,
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                    components.append(component)
                    
                except Exception as e:
                    logger.warning(f"解析第{index+1}行组件映射数据失败: {e}")
                    continue
            
            logger.info(f"成功读取 {len(components)} 个包组件映射")
            return components
            
        except Exception as e:
            logger.error(f"读取包组件映射文件失败: {e}")
            return []
    
    def create_package_mapping_table(self):
        """创建包映射表"""
        try:
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS package_component_mapping (
                id INT AUTO_INCREMENT PRIMARY KEY,
                business_module VARCHAR(255) NOT NULL COMMENT '业务模块名称',
                package_name VARCHAR(255) NOT NULL COMMENT '应用包名',
                component_name VARCHAR(255) NOT NULL COMMENT '组件名称',
                description TEXT COMMENT '描述信息',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                INDEX idx_business_module (business_module),
                INDEX idx_package_name (package_name),
                INDEX idx_component_name (component_name),
                UNIQUE KEY uk_module_package_component (business_module, package_name, component_name)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模块包组件映射表';
            """
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(create_table_sql)
                conn.commit()
                
            logger.info("包映射表创建成功")
            
        except Exception as e:
            logger.error(f"创建包映射表失败: {e}")
            raise
    
    def save_package_components_to_db(self, components: List[PackageComponent]) -> int:
        """
        保存包组件映射到数据库
        
        Args:
            components: 包组件映射列表
            
        Returns:
            int: 成功保存的记录数
        """
        try:
            if not components:
                logger.warning("没有要保存的包组件映射数据")
                return 0
            
            # 确保表存在
            self.create_package_mapping_table()
            
            insert_sql = """
            INSERT INTO package_component_mapping 
            (business_module, package_name, component_name, description, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            description = VALUES(description),
            updated_at = VALUES(updated_at)
            """
            
            saved_count = 0
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                for component in components:
                    try:
                        cursor.execute(insert_sql, (
                            component.business_module,
                            component.package_name,
                            component.component_name,
                            component.description,
                            component.created_at,
                            component.updated_at
                        ))
                        saved_count += 1
                        
                    except Exception as e:
                        logger.warning(f"保存组件映射失败: {component.business_module} - {component.package_name}: {e}")
                        continue
                
                conn.commit()
            
            logger.info(f"成功保存 {saved_count} 个包组件映射到数据库")
            return saved_count
            
        except Exception as e:
            logger.error(f"保存包组件映射到数据库失败: {e}")
            return 0
    
    def get_package_by_module(self, business_module: str) -> List[Dict[str, Any]]:
        """
        根据业务模块获取包名列表
        
        Args:
            business_module: 业务模块名称
            
        Returns:
            List[Dict[str, Any]]: 包信息列表
        """
        try:
            query_sql = """
            SELECT business_module, package_name, component_name, description
            FROM package_component_mapping
            WHERE business_module = %s
            ORDER BY package_name, component_name
            """
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor(dictionary=True)
                cursor.execute(query_sql, (business_module,))
                results = cursor.fetchall()
            
            return results
            
        except Exception as e:
            logger.error(f"查询业务模块包信息失败: {e}")
            return []
    
    def get_component_by_package(self, package_name: str) -> List[Dict[str, Any]]:
        """
        根据包名获取组件信息
        
        Args:
            package_name: 应用包名
            
        Returns:
            List[Dict[str, Any]]: 组件信息列表
        """
        try:
            query_sql = """
            SELECT business_module, package_name, component_name, description
            FROM package_component_mapping
            WHERE package_name = %s
            ORDER BY business_module, component_name
            """
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor(dictionary=True)
                cursor.execute(query_sql, (package_name,))
                results = cursor.fetchall()
            
            return results
            
        except Exception as e:
            logger.error(f"查询包组件信息失败: {e}")
            return []
    
    def get_all_mappings(self) -> List[Dict[str, Any]]:
        """
        获取所有包组件映射
        
        Returns:
            List[Dict[str, Any]]: 所有映射信息
        """
        try:
            query_sql = """
            SELECT business_module, package_name, component_name, description, created_at, updated_at
            FROM package_component_mapping
            ORDER BY business_module, package_name, component_name
            """
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor(dictionary=True)
                cursor.execute(query_sql)
                results = cursor.fetchall()
            
            return results
            
        except Exception as e:
            logger.error(f"查询所有包组件映射失败: {e}")
            return []
    
    def update_mapping_from_excel(self, excel_file_path: str = None) -> Dict[str, Any]:
        """
        从Excel文件更新包组件映射
        
        Args:
            excel_file_path: Excel文件路径
            
        Returns:
            Dict[str, Any]: 更新结果
        """
        try:
            logger.info("开始从Excel文件更新包组件映射...")
            
            # 读取Excel数据
            components = self.read_package_components_from_excel(excel_file_path)
            
            if not components:
                return {
                    'success': False,
                    'error': '没有读取到有效的包组件映射数据'
                }
            
            # 保存到数据库
            saved_count = self.save_package_components_to_db(components)
            
            return {
                'success': True,
                'total_read': len(components),
                'saved_count': saved_count,
                'message': f'成功更新 {saved_count} 个包组件映射'
            }
            
        except Exception as e:
            logger.error(f"从Excel更新包组件映射失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_mapping_statistics(self) -> Dict[str, Any]:
        """
        获取映射统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            stats_sql = """
            SELECT 
                COUNT(*) as total_mappings,
                COUNT(DISTINCT business_module) as total_modules,
                COUNT(DISTINCT package_name) as total_packages,
                COUNT(DISTINCT component_name) as total_components
            FROM package_component_mapping
            """
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor(dictionary=True)
                cursor.execute(stats_sql)
                stats = cursor.fetchone()
            
            return stats or {}
            
        except Exception as e:
            logger.error(f"获取映射统计信息失败: {e}")
            return {}
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.db_manager:
                self.db_manager.cleanup()
            
            logger.info("模块包映射管理器资源清理完成")
            
        except Exception as e:
            logger.warning(f"清理模块包映射管理器资源失败: {e}")


def main():
    """测试函数"""
    try:
        # 创建映射管理器
        mapper = ModulePackageMapper()
        
        # 测试从Excel读取数据
        print("=== 测试从Excel读取包组件映射 ===")
        components = mapper.read_package_components_from_excel()
        print(f"读取到 {len(components)} 个包组件映射")
        
        # 显示前几个映射
        for i, comp in enumerate(components[:5]):
            print(f"{i+1}. 模块: {comp.business_module}")
            print(f"   包名: {comp.package_name}")
            print(f"   组件: {comp.component_name}")
            print(f"   描述: {comp.description}")
            print("-" * 50)
        
        # 测试保存到数据库
        print("\n=== 测试保存到数据库 ===")
        saved_count = mapper.save_package_components_to_db(components)
        print(f"成功保存 {saved_count} 个映射到数据库")
        
        # 测试查询功能
        print("\n=== 测试查询功能 ===")
        stats = mapper.get_mapping_statistics()
        print(f"统计信息: {stats}")
        
        # 清理资源
        mapper.cleanup()
        
    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == "__main__":
    main()
