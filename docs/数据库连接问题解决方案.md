# 数据库连接问题解决方案

## 问题描述

系统启动时出现以下错误：
```
(pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'localhost' (using password: YES)")
```

## 问题分析

1. **错误代码1045**: 表示用户名或密码认证失败
2. **MySQL服务状态**: 检测发现MySQL服务未运行
3. **配置信息**: 当前使用root用户，密码包含特殊字符

## 解决方案

### 方案一：启动MySQL服务（推荐）

#### Windows系统：

1. **使用服务管理器**：
   - 按 `Win + R`，输入 `services.msc`
   - 找到 "MySQL" 或 "MySQL80" 服务
   - 右键点击 -> 启动

2. **使用命令行**（需要管理员权限）：
   ```cmd
   net start mysql
   ```
   或
   ```cmd
   net start mysql80
   ```

3. **检查服务状态**：
   ```cmd
   sc query mysql
   ```

#### 如果MySQL未安装：

1. **下载MySQL**：
   - 访问 [MySQL官网](https://dev.mysql.com/downloads/mysql/)
   - 下载MySQL Community Server
   - 选择Windows版本

2. **安装步骤**：
   - 运行安装程序
   - 选择"Developer Default"配置
   - 设置root密码（建议使用简单密码如：123456）
   - 完成安装

### 方案二：修复现有MySQL配置

#### 重置root密码：

1. **停止MySQL服务**：
   ```cmd
   net stop mysql
   ```

2. **以安全模式启动**：
   ```cmd
   mysqld --skip-grant-tables --skip-networking
   ```

3. **新开命令行窗口，连接MySQL**：
   ```cmd
   mysql -u root
   ```

4. **重置密码**：
   ```sql
   USE mysql;
   UPDATE user SET authentication_string=PASSWORD('123456') WHERE User='root';
   FLUSH PRIVILEGES;
   EXIT;
   ```

5. **重启MySQL服务**：
   ```cmd
   net stop mysql
   net start mysql
   ```

### 方案三：创建专用数据库用户（推荐）

1. **连接MySQL**：
   ```cmd
   mysql -u root -p
   ```

2. **创建数据库**：
   ```sql
   CREATE DATABASE ui_analysis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

3. **创建专用用户**：
   ```sql
   CREATE USER 'ui_user'@'localhost' IDENTIFIED BY 'ui_password_123';
   GRANT ALL PRIVILEGES ON ui_analysis.* TO 'ui_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

4. **测试新用户**：
   ```cmd
   mysql -u ui_user -p ui_analysis
   ```

### 方案四：更新项目配置

修改 `backend/config.py` 文件：

```python
@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = "localhost"
    port: int = 3306
    username: str = "ui_user"          # 改为新用户
    password: str = "ui_password_123"  # 改为新密码
    database: str = "ui_analysis"
    charset: str = "utf8mb4"
```

## 验证步骤

### 1. 运行连接测试脚本：
```bash
python temp/test_db_connection.py
```

### 2. 运行完整诊断：
```bash
python temp/check_database_setup.py
```

### 3. 启动主程序：
```bash
python -m backend.main --package com.android.settings
```

## 常见问题

### Q1: 找不到mysql命令
**A**: MySQL的bin目录未添加到系统PATH中
- 找到MySQL安装目录（通常在 `C:\Program Files\MySQL\MySQL Server 8.0\bin`）
- 将该路径添加到系统环境变量PATH中

### Q2: 服务启动失败
**A**: 可能的原因：
- 端口3306被占用
- MySQL配置文件损坏
- 权限不足

**解决方法**：
```cmd
# 检查端口占用
netstat -ano | findstr :3306

# 以管理员身份运行命令行
# 重新安装MySQL服务
```

### Q3: 密码包含特殊字符
**A**: URL编码问题
- 使用简单密码（如：123456）
- 或在连接URL中对特殊字符进行编码

## 推荐配置

为了避免后续问题，推荐使用以下配置：

```python
# 数据库配置
username: str = "ui_user"
password: str = "ui_password_123"  # 简单密码，避免特殊字符
database: str = "ui_analysis"
```

## 自动化脚本

项目提供了以下自动化脚本：

1. **`temp/mysql_setup_guide.py`**: MySQL设置指导
2. **`temp/test_db_connection.py`**: 连接测试
3. **`temp/check_database_setup.py`**: 完整诊断

## 联系支持

如果以上方案都无法解决问题，请提供：
1. 操作系统版本
2. MySQL版本
3. 错误日志
4. 已尝试的解决方案
