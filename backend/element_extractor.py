"""
UI元素提取器
基于UIAutomator2实现UI元素的提取和筛选
"""
import sys
import os
import time
import xml.etree.ElementTree as ET
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
from loguru import logger

# 添加example目录到路径
sys.path.append(str(Path(__file__).parent.parent / "example"))

try:
    import uiautomator2 as u2
    from example.utils.uiautomator2_manager import UIAutomator2Manager
except ImportError as e:
    logger.error(f"导入依赖失败: {e}")
    logger.error("请确保已安装uiautomator2: pip install uiautomator2")
    raise

from .config import config
from .models.element_model import UIElement, ElementPosition, ElementAttributes


class UIElementExtractor:
    """UI元素提取器"""
    
    def __init__(self, device_id: Optional[str] = None):
        """
        初始化元素提取器
        
        Args:
            device_id: 设备ID，为None时使用默认设备
        """
        self.device_id = device_id or config.device.device_id
        self.device = None
        self.ui_manager = UIAutomator2Manager()
        
        # 获取筛选规则
        self.filter_rules = config.get_element_filter_rules()
        
    def connect_device(self) -> bool:
        """
        连接设备
        
        Returns:
            bool: 连接是否成功
        """
        try:
            logger.info(f"正在连接设备: {self.device_id or 'default'}")
            
            # 检查UIAutomator2服务健康状态
            if not self.ui_manager.check_service_health(self.device_id):
                logger.warning("UIAutomator2服务不健康，尝试重启...")
                if not self.ui_manager.restart_service(self.device_id):
                    logger.error("重启UIAutomator2服务失败")
                    return False
            
            # 连接设备
            if self.device_id:
                self.device = u2.connect(self.device_id)
            else:
                self.device = u2.connect()
            
            # 验证连接
            device_info = self.device.device_info
            logger.info(f"设备连接成功: {device_info.get('brand', 'Unknown')} {device_info.get('model', 'Unknown')}")
            return True
            
        except Exception as e:
            logger.error(f"连接设备失败: {e}")
            return False
    
    def get_page_source(self) -> Optional[str]:
        """
        获取当前页面的XML源码
        
        Returns:
            Optional[str]: XML源码，失败时返回None
        """
        try:
            if not self.device:
                logger.error("设备未连接")
                return None
            
            logger.debug("正在获取页面DOM树...")
            
            # 获取页面源码
            xml_content = self.device.dump_hierarchy()
            
            if not xml_content:
                logger.error("获取页面源码失败")
                return None
            
            logger.debug("页面DOM树获取成功")
            return xml_content
            
        except Exception as e:
            logger.error(f"获取页面源码失败: {e}")
            return None
    
    def extract_elements_from_xml(self, xml_content: str) -> List[UIElement]:
        """
        从XML内容中提取UI元素
        
        Args:
            xml_content: XML内容
            
        Returns:
            List[UIElement]: 提取的UI元素列表
        """
        try:
            logger.debug("正在解析XML元素...")
            
            # 解析XML
            root = ET.fromstring(xml_content)
            elements = []
            
            # 递归遍历所有元素
            def traverse_element(xml_element, parent_path="", depth=0):
                try:
                    # 从XML元素创建UIElement对象
                    ui_element = UIElement.from_xml_element(xml_element, parent_path)
                    
                    # 应用筛选规则
                    if self._should_include_element(ui_element):
                        elements.append(ui_element)
                        logger.debug(f"添加元素: {ui_element.element_type} - {ui_element.text or ui_element.resource_id}")
                    
                    # 递归处理子元素
                    element_path = f"{parent_path}/{xml_element.tag}" if parent_path else xml_element.tag
                    for child in xml_element:
                        traverse_element(child, element_path, depth + 1)
                        
                except Exception as e:
                    logger.warning(f"处理XML元素失败: {e}")
            
            # 开始遍历
            traverse_element(root)
            
            logger.info(f"XML解析完成，提取到 {len(elements)} 个有效元素")
            return elements
            
        except Exception as e:
            logger.error(f"解析XML失败: {e}")
            return []
    
    def _should_include_element(self, element: UIElement) -> bool:
        """——
        判断元素是否应该被包含
        
        Args:
            element: UI元素
            
        Returns:
            bool: 是否应该包含
        """
        try:
            # 检查元素是否可见且有效
            if not element.is_visible_and_valid():
                return False
            
            # 检查元素是否可操作
            if not element.is_actionable():
                # 如果元素不可操作，但有文本内容，也可以考虑包含（用于信息提取）
                if not (element.text or element.content_desc):
                    return False
            
            # 检查元素尺寸
            if element.position:
                min_size = self.filter_rules.get("min_size", {"width": 10, "height": 10})
                if (element.position.width < min_size["width"] or 
                    element.position.height < min_size["height"]):
                    return False
            
            # 排除特定类型的元素
            exclude_classes = self.filter_rules.get("exclude_classes", [])
            if element.element_type in exclude_classes:
                return False
            
            # 排除系统UI元素
            if element.package_name and element.package_name.startswith("com.android.systemui"):
                return False
            
            return True
            
        except Exception as e:
            logger.warning(f"元素筛选判断失败: {e}")
            return False
    
    def extract_current_page_elements(self) -> List[UIElement]:
        """
        提取当前页面的所有UI元素
        
        Returns:
            List[UIElement]: 提取的UI元素列表
        """
        try:
            # 获取页面源码
            xml_content = self.get_page_source()
            if not xml_content:
                return []
            
            # 从XML提取元素
            elements = self.extract_elements_from_xml(xml_content)
            
            # 后处理：去重和排序
            elements = self._post_process_elements(elements)
            
            logger.info(f"当前页面元素提取完成，共 {len(elements)} 个元素")
            return elements
            
        except Exception as e:
            logger.error(f"提取当前页面元素失败: {e}")
            return []
    
    def _post_process_elements(self, elements: List[UIElement]) -> List[UIElement]:
        """
        后处理元素列表：去重、排序等
        
        Args:
            elements: 原始元素列表
            
        Returns:
            List[UIElement]: 处理后的元素列表
        """
        try:
            # 去重：基于元素ID
            unique_elements = {}
            for element in elements:
                if element.element_id not in unique_elements:
                    unique_elements[element.element_id] = element
                else:
                    # 如果有重复，保留信息更完整的元素
                    existing = unique_elements[element.element_id]
                    if len(element.text) > len(existing.text):
                        unique_elements[element.element_id] = element
            
            elements = list(unique_elements.values())
            
            # 排序：按照位置从上到下、从左到右
            def sort_key(elem):
                if elem.position:
                    return (elem.position.top, elem.position.left)
                return (0, 0)
            
            elements.sort(key=sort_key)
            
            logger.debug(f"元素后处理完成，去重后剩余 {len(elements)} 个元素")
            return elements
            
        except Exception as e:
            logger.warning(f"元素后处理失败: {e}")
            return elements
    
    def get_actionable_elements(self, elements: List[UIElement] = None) -> List[UIElement]:
        """
        获取可操作的元素
        
        Args:
            elements: 元素列表，为None时提取当前页面元素
            
        Returns:
            List[UIElement]: 可操作的元素列表
        """
        if elements is None:
            elements = self.extract_current_page_elements()
        
        actionable_elements = [elem for elem in elements if elem.is_actionable()]
        logger.info(f"找到 {len(actionable_elements)} 个可操作元素")
        return actionable_elements
    
    def find_elements_by_text(self, text: str, exact_match: bool = True) -> List[UIElement]:
        """
        根据文本查找元素
        
        Args:
            text: 要查找的文本
            exact_match: 是否精确匹配
            
        Returns:
            List[UIElement]: 匹配的元素列表
        """
        elements = self.extract_current_page_elements()
        
        if exact_match:
            matches = [elem for elem in elements if elem.text == text]
        else:
            matches = [elem for elem in elements if text.lower() in elem.text.lower()]
        
        logger.info(f"根据文本 '{text}' 找到 {len(matches)} 个元素")
        return matches
    
    def find_elements_by_resource_id(self, resource_id: str) -> List[UIElement]:
        """
        根据资源ID查找元素
        
        Args:
            resource_id: 资源ID
            
        Returns:
            List[UIElement]: 匹配的元素列表
        """
        elements = self.extract_current_page_elements()
        matches = [elem for elem in elements if elem.resource_id == resource_id]
        
        logger.info(f"根据资源ID '{resource_id}' 找到 {len(matches)} 个元素")
        return matches
    
    def find_elements_by_type(self, element_type: str) -> List[UIElement]:
        """
        根据元素类型查找元素
        
        Args:
            element_type: 元素类型
            
        Returns:
            List[UIElement]: 匹配的元素列表
        """
        elements = self.extract_current_page_elements()
        matches = [elem for elem in elements if elem.element_type == element_type]
        
        logger.info(f"根据类型 '{element_type}' 找到 {len(matches)} 个元素")
        return matches
    
    def wait_for_element(self, locator: Dict[str, str], timeout: int = 10) -> Optional[UIElement]:
        """
        等待元素出现
        
        Args:
            locator: 定位器，如 {"text": "确定"} 或 {"resource_id": "com.example:id/button"}
            timeout: 超时时间（秒）
            
        Returns:
            Optional[UIElement]: 找到的元素，超时返回None
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            elements = self.extract_current_page_elements()
            
            for element in elements:
                match = True
                for key, value in locator.items():
                    if key == "text" and element.text != value:
                        match = False
                        break
                    elif key == "resource_id" and element.resource_id != value:
                        match = False
                        break
                    elif key == "content_desc" and element.content_desc != value:
                        match = False
                        break
                    elif key == "type" and element.element_type != value:
                        match = False
                        break
                
                if match:
                    logger.info(f"找到等待的元素: {locator}")
                    return element
            
            time.sleep(1)
        
        logger.warning(f"等待元素超时: {locator}")
        return None
