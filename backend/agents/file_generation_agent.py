"""
脚本文件生成智能体
将最终脚本存储到MySQL并生成testcase目录下的ts文件
"""
import logging
import os
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage
from autogen_core import CancellationToken

from backend.script_database_manager import ScriptDatabaseManager
from backend.agents.case_info_agent import CaseElementInfo
from example.llms import deepseek_model_client

logger = logging.getLogger(__name__)


class FileGenerationAgent:
    """脚本文件生成智能体"""
    
    def __init__(self, testcase_dir: str = "testcase"):
        """
        初始化文件生成智能体
        
        Args:
            testcase_dir: 测试用例文件目录
        """
        self.testcase_dir = Path(testcase_dir)
        self.script_db = ScriptDatabaseManager()
        self.agent = self._create_agent()
        
        # 确保目录存在
        self.testcase_dir.mkdir(parents=True, exist_ok=True)
        
    def _create_agent(self) -> AssistantAgent:
        """创建AutoGen智能体"""
        system_message = """
你是一个专业的测试文件管理专家，负责将生成的自动化测试脚本保存为规范的文件。

你的职责：
1. 验证脚本内容的完整性
2. 生成规范的文件名
3. 添加必要的文件头注释
4. 确保文件格式正确
5. 提供文件保存的元数据信息

文件命名规范：
- 使用测试用例ID作为基础
- 添加业务模块前缀
- 使用.ts扩展名
- 避免特殊字符

文件头注释应包含：
- 测试用例信息
- 生成时间
- 业务模块
- 脚本描述
"""
        
        return AssistantAgent(
            name="file_generation_agent",
            model_client=deepseek_model_client(),
            system_message=system_message
        )
    
    async def save_script_file(self, script_content: str, case_info: CaseElementInfo, 
                             review_result: Dict[str, Any], generation_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        保存脚本文件到数据库和文件系统
        
        Args:
            script_content: 脚本内容
            case_info: 用例信息
            review_result: 审查结果
            generation_metadata: 生成元数据
            
        Returns:
            Dict[str, Any]: 保存结果
        """
        try:
            logger.info(f"开始保存脚本文件: {case_info.test_case.tcid}")
            
            # 1. 生成文件名和路径
            file_info = await self._generate_file_info(script_content, case_info)
            
            # 2. 添加文件头注释
            final_script = self._add_file_header(script_content, case_info, file_info)
            
            # 3. 保存到数据库
            db_result = await self._save_to_database(
                final_script, case_info, review_result, generation_metadata, file_info
            )
            
            # 4. 保存到文件系统
            file_result = await self._save_to_filesystem(final_script, file_info)
            
            # 5. 更新数据库中的文件路径信息
            self.script_db.update_script_file_path(
                db_result["script_id"], 
                file_result["file_path"], 
                file_result["file_size"]
            )
            
            # 6. 生成保存结果
            save_result = {
                "success": True,
                "script_id": db_result["script_id"],
                "file_path": file_result["file_path"],
                "file_name": file_info["file_name"],
                "file_size": file_result["file_size"],
                "database_saved": db_result["success"],
                "file_saved": file_result["success"],
                "metadata": {
                    "case_id": case_info.test_case.tcid,
                    "business_module": case_info.business_context.get("module_name"),
                    "review_score": review_result.get("overall_score", 0),
                    "generation_time": datetime.now().isoformat()
                }
            }
            
            logger.info(f"脚本文件保存完成: {file_info['file_name']}")
            return save_result
            
        except Exception as e:
            logger.error(f"保存脚本文件失败: {e}")
            raise
    
    async def _generate_file_info(self, script_content: str, case_info: CaseElementInfo) -> Dict[str, Any]:
        """生成文件信息"""
        try:
            # 使用智能体生成文件信息
            task = f"""
请为以下测试脚本生成合适的文件信息：

测试用例ID: {case_info.test_case.tcid}
业务模块: {case_info.business_context.get("module_name", "unknown")}
脚本内容长度: {len(script_content)} 字符

请生成：
1. 规范的文件名（不含路径）
2. 文件描述
3. 建议的子目录（如果需要）

要求：
- 文件名使用英文和数字
- 避免特殊字符
- 体现业务模块和用例特征
- 使用.ts扩展名
"""
            
            result = await self.agent.run(task=task)
            
            if result and result.messages:
                content = result.messages[-1].content
                return self._parse_file_info(content, case_info)
            else:
                return self._generate_default_file_info(case_info)
                
        except Exception as e:
            logger.error(f"生成文件信息失败: {e}")
            return self._generate_default_file_info(case_info)
    
    def _parse_file_info(self, content: str, case_info: CaseElementInfo) -> Dict[str, Any]:
        """解析智能体生成的文件信息"""
        try:
            # 简单的解析逻辑，提取文件名
            lines = content.split('\n')
            file_name = None
            description = ""
            sub_dir = ""
            
            for line in lines:
                line = line.strip()
                if '.ts' in line and not line.startswith('#'):
                    # 提取可能的文件名
                    if ':' in line:
                        file_name = line.split(':')[-1].strip()
                    else:
                        file_name = line
                    break
            
            if not file_name or not file_name.endswith('.ts'):
                file_name = self._generate_safe_filename(case_info)
            
            return {
                "file_name": file_name,
                "description": description or f"自动化测试脚本 - {case_info.test_case.tcid}",
                "sub_dir": sub_dir,
                "full_path": str(self.testcase_dir / (sub_dir if sub_dir else "") / file_name)
            }
            
        except Exception as e:
            logger.error(f"解析文件信息失败: {e}")
            return self._generate_default_file_info(case_info)
    
    def _generate_default_file_info(self, case_info: CaseElementInfo) -> Dict[str, Any]:
        """生成默认文件信息"""
        file_name = self._generate_safe_filename(case_info)
        module_name = case_info.business_context.get("module_name", "unknown")
        
        # 根据业务模块创建子目录
        sub_dir = self._sanitize_dirname(module_name)
        
        return {
            "file_name": file_name,
            "description": f"自动化测试脚本 - {case_info.test_case.tcid}",
            "sub_dir": sub_dir,
            "full_path": str(self.testcase_dir / sub_dir / file_name)
        }
    
    def _generate_safe_filename(self, case_info: CaseElementInfo) -> str:
        """
        生成安全的文件名
        格式: {Component}_{TCID}_test.ts
        """
        tcid = case_info.test_case.tcid
        component = case_info.test_case.component or "UnknownComponent"

        # 清理文件名，确保符合文件系统要求
        safe_tcid = self._sanitize_filename(tcid)
        safe_component = self._sanitize_filename(component)

        # 使用 {Component}_{TCID}_test 格式
        filename = f"{safe_component}_{safe_tcid}_test.ts"

        # 记录文件命名信息
        logger.info(f"生成文件名: {filename} (Component: {component}, TCID: {tcid})")

        return filename
    
    def _sanitize_filename(self, name: str) -> str:
        """清理文件名，移除特殊字符，保持大小写"""
        import re
        # 只保留字母、数字、下划线和连字符，保持原始大小写
        safe_name = re.sub(r'[^\w\-]', '_', name)
        # 移除多余的下划线
        safe_name = re.sub(r'_+', '_', safe_name)
        # 移除开头和结尾的下划线
        safe_name = safe_name.strip('_')
        return safe_name.lower()
    
    def _sanitize_dirname(self, name: str) -> str:
        """清理目录名"""
        return self._sanitize_filename(name)
    
    def _add_file_header(self, script_content: str, case_info: CaseElementInfo, 
                        file_info: Dict[str, Any]) -> str:
        """添加文件头注释"""
        header = f"""/**
 * 自动生成的测试脚本
 * 
 * 测试用例ID: {case_info.test_case.tcid}
 * 业务模块: {case_info.business_context.get("module_name", "未知")}
 * 生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
 * 文件描述: {file_info.get("description", "")}
 * 
 * 测试步骤:
 * {case_info.test_case.steps}
 *
 * 预期结果:
 * {case_info.test_case.expect_result}
 * 
 * 注意: 此文件由AI自动生成，请根据实际情况调整
 */

"""
        return header + script_content
    
    async def _save_to_database(self, script_content: str, case_info: CaseElementInfo, 
                              review_result: Dict[str, Any], generation_metadata: Dict[str, Any],
                              file_info: Dict[str, Any]) -> Dict[str, Any]:
        """保存到数据库"""
        try:
            # 准备元数据
            metadata = {
                **generation_metadata,
                "review_result": review_result,
                "file_info": file_info,
                "page_elements_used": [
                    {
                        "id": elem.id,
                        "type": elem.element_type,
                        "text": elem.text,
                        "resource_id": elem.resource_id
                    } for elem in case_info.related_elements[:10]
                ]
            }
            
            # 保存脚本
            script_record = self.script_db.save_generated_script(
                test_case_id=case_info.test_case.id,
                script_name=file_info["file_name"],
                script_content=script_content,
                metadata=metadata
            )
            
            return {
                "success": True,
                "script_id": script_record.id,
                "message": "数据库保存成功"
            }
            
        except Exception as e:
            logger.error(f"数据库保存失败: {e}")
            return {
                "success": False,
                "script_id": None,
                "message": f"数据库保存失败: {e}"
            }
    
    async def _save_to_filesystem(self, script_content: str, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """保存到文件系统"""
        try:
            file_path = file_info["full_path"]

            # 确保 file_path 是 Path 对象
            from pathlib import Path
            if isinstance(file_path, str):
                file_path = Path(file_path)

            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            # 获取文件大小
            file_size = file_path.stat().st_size
            
            logger.info(f"文件保存成功: {file_path}")
            
            return {
                "success": True,
                "file_path": str(file_path),
                "file_size": file_size,
                "message": "文件保存成功"
            }
            
        except Exception as e:
            logger.error(f"文件保存失败: {e}")
            return {
                "success": False,
                "file_path": "",
                "file_size": 0,
                "message": f"文件保存失败: {e}"
            }


async def main():
    """测试函数"""
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建文件生成智能体
    agent = FileGenerationAgent()
    
    print("脚本文件生成智能体创建成功")
    print(f"测试用例目录: {agent.testcase_dir}")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
