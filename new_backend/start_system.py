#!/usr/bin/env python3
"""
多智能体自动化脚本生成系统启动脚本
提供多种启动模式：API服务、命令行工具、调试模式
"""
import os
import sys
import asyncio
import argparse
from pathlib import Path
from loguru import logger

# 添加相对路径导入
sys.path.append(str(Path(__file__).parent))

from config import config
from main_controller import MainController
from api_interface import start_api_server


def setup_logging(debug: bool = False):
    """设置日志"""
    try:
        # 移除默认处理器
        logger.remove()
        
        # 设置日志级别
        level = "DEBUG" if debug else "INFO"
        
        # 控制台输出
        logger.add(
            sys.stdout,
            level=level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            colorize=True
        )
        
        # 文件输出
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        logger.add(
            log_dir / "system.log",
            level=level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation="10 MB",
            retention="7 days",
            compression="zip"
        )
        
        logger.info(f"日志系统初始化完成，级别: {level}")
        
    except Exception as e:
        print(f"设置日志失败: {e}")


async def run_cli_mode():
    """命令行模式"""
    try:
        logger.info("启动命令行模式...")
        
        controller = MainController()
        
        print("\n=== 多智能体自动化脚本生成系统 ===")
        print("1. 分析应用")
        print("2. 处理测试用例")
        print("3. 批量处理")
        print("4. 系统状态")
        print("5. 退出")
        
        while True:
            try:
                choice = input("\n请选择操作 (1-5): ").strip()
                
                if choice == "1":
                    await handle_app_analysis(controller)
                elif choice == "2":
                    await handle_test_case(controller)
                elif choice == "3":
                    await handle_batch_process(controller)
                elif choice == "4":
                    await handle_system_status(controller)
                elif choice == "5":
                    print("退出系统...")
                    break
                else:
                    print("无效选择，请重新输入")
                    
            except KeyboardInterrupt:
                print("\n\n用户中断，退出系统...")
                break
            except Exception as e:
                logger.error(f"命令行操作失败: {e}")
                print(f"操作失败: {e}")
        
        # 清理资源
        controller.cleanup()
        
    except Exception as e:
        logger.error(f"命令行模式失败: {e}")
        print(f"启动失败: {e}")


async def handle_app_analysis(controller):
    """处理应用分析"""
    try:
        package_name = input("请输入应用包名: ").strip()
        if not package_name:
            print("包名不能为空")
            return
        
        app_name = input("请输入应用名称 (可选): ").strip() or None
        
        print(f"开始分析应用: {package_name}")
        result = await controller.analyze_app(package_name, app_name)
        
        if result.get('success'):
            print("✓ 应用分析完成")
            print(f"  - 分析会话ID: {result.get('session_id')}")
            print(f"  - 发现页面数: {result.get('pages_count', 0)}")
            print(f"  - 发现元素数: {result.get('elements_count', 0)}")
        else:
            print(f"✗ 应用分析失败: {result.get('error')}")
            
    except Exception as e:
        logger.error(f"应用分析处理失败: {e}")
        print(f"操作失败: {e}")


async def handle_test_case(controller):
    """处理测试用例"""
    try:
        print("\n请输入测试用例信息:")
        case_data = {
            'case_id': input("用例ID: ").strip(),
            'case_name': input("用例名称: ").strip(),
            'package_name': input("应用包名: ").strip(),
            'test_steps': input("测试步骤: ").strip(),
            'expected_results': input("预期结果 (可选): ").strip() or None
        }
        
        # 验证必要字段
        if not all([case_data['case_id'], case_data['case_name'], 
                   case_data['package_name'], case_data['test_steps']]):
            print("必要字段不能为空")
            return
        
        print(f"开始处理测试用例: {case_data['case_name']}")
        result = await controller.process_test_case(case_data)
        
        if result.get('success'):
            print("✓ 测试用例处理完成")
            print(f"  - 用例ID: {result.get('test_case_id')}")
            print(f"  - 脚本生成: {'成功' if result.get('script_generated') else '失败'}")
            if result.get('script_file'):
                print(f"  - 脚本文件: {result.get('script_file')}")
        else:
            print(f"✗ 测试用例处理失败: {result.get('error')}")
            
    except Exception as e:
        logger.error(f"测试用例处理失败: {e}")
        print(f"操作失败: {e}")


async def handle_batch_process(controller):
    """处理批量处理"""
    try:
        excel_file = input("请输入Excel文件路径: ").strip()
        if not excel_file or not Path(excel_file).exists():
            print("Excel文件不存在")
            return
        
        sheet_name = input("请输入工作表名称 (可选): ").strip() or None
        
        print(f"开始批量处理: {excel_file}")
        
        # 这里需要调用批量处理功能
        # 简化处理，显示提示信息
        print("批量处理功能需要通过API模式使用")
        print("请使用: python start_system.py --mode api")
        
    except Exception as e:
        logger.error(f"批量处理失败: {e}")
        print(f"操作失败: {e}")


async def handle_system_status(controller):
    """处理系统状态"""
    try:
        print("获取系统状态...")
        status = await controller.get_system_status()
        
        print("\n=== 系统状态 ===")
        print(f"系统状态: {status.get('status', 'unknown')}")
        print(f"数据库连接: {status.get('database_connected', False)}")
        print(f"AutoGen状态: {status.get('autogen_available', False)}")
        print(f"设备连接: {status.get('device_connected', False)}")
        
        # 显示统计信息
        stats = status.get('statistics', {})
        if stats:
            print(f"\n=== 统计信息 ===")
            print(f"总应用数: {stats.get('total_apps', 0)}")
            print(f"总页面数: {stats.get('total_pages', 0)}")
            print(f"总用例数: {stats.get('total_cases', 0)}")
            print(f"总脚本数: {stats.get('total_scripts', 0)}")
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        print(f"操作失败: {e}")


def run_api_mode(host: str = "0.0.0.0", port: int = 8000):
    """API服务模式"""
    try:
        logger.info(f"启动API服务模式: http://{host}:{port}")
        start_api_server(host, port)
        
    except Exception as e:
        logger.error(f"API服务模式失败: {e}")
        print(f"启动失败: {e}")


async def run_debug_mode():
    """调试模式"""
    try:
        logger.info("启动调试模式...")
        
        # 运行调试脚本
        debug_script = Path(__file__).parent / "tmp" / "debug_system.py"
        if debug_script.exists():
            import subprocess
            result = subprocess.run([sys.executable, str(debug_script)], 
                                  capture_output=True, text=True)
            
            print("=== 调试输出 ===")
            print(result.stdout)
            
            if result.stderr:
                print("=== 错误输出 ===")
                print(result.stderr)
        else:
            print("调试脚本不存在，请先运行系统初始化")
        
    except Exception as e:
        logger.error(f"调试模式失败: {e}")
        print(f"调试失败: {e}")


def check_dependencies():
    """检查依赖"""
    try:
        logger.info("检查系统依赖...")
        
        # 检查Python版本
        if sys.version_info < (3, 8):
            logger.error("需要Python 3.8或更高版本")
            return False
        
        # 检查关键依赖
        required_packages = [
            'autogen_agentchat',
            'sqlalchemy',
            'uiautomator2',
            'pandas',
            'loguru'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            logger.error(f"缺少依赖包: {missing_packages}")
            print(f"请安装缺少的依赖: pip install {' '.join(missing_packages)}")
            return False
        
        logger.info("依赖检查通过")
        return True
        
    except Exception as e:
        logger.error(f"依赖检查失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="多智能体自动化脚本生成系统")
    parser.add_argument("--mode", choices=["cli", "api", "debug"], 
                       default="cli", help="运行模式")
    parser.add_argument("--host", default="0.0.0.0", 
                       help="API服务主机地址")
    parser.add_argument("--port", type=int, default=8000, 
                       help="API服务端口")
    parser.add_argument("--debug", action="store_true", 
                       help="启用调试模式")
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.debug)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    try:
        logger.info(f"启动系统，模式: {args.mode}")
        
        if args.mode == "cli":
            asyncio.run(run_cli_mode())
        elif args.mode == "api":
            run_api_mode(args.host, args.port)
        elif args.mode == "debug":
            asyncio.run(run_debug_mode())
        else:
            logger.error(f"未知模式: {args.mode}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("用户中断，系统退出")
    except Exception as e:
        logger.error(f"系统运行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
