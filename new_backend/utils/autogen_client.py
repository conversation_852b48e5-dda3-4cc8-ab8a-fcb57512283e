"""
AutoGen客户端管理器
提供AutoGen智能体的创建、管理和调用功能
"""
import os
import sys
import json
import asyncio
from typing import Dict, Any, Optional, List
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加相对路径导入
sys.path.append(str(Path(__file__).parent.parent))
sys.path.append(str(Path(__file__).parent.parent.parent / "example"))

from config import config

# AutoGen相关导入
AUTOGEN_AVAILABLE = False

def _try_import_autogen():
    """尝试导入AutoGen模块"""
    global AUTOGEN_AVAILABLE
    
    if AUTOGEN_AVAILABLE:
        return True
    
    try:
        from autogen_agentchat.agents import AssistantAgent
        from autogen_agentchat.messages import TextMessage, MultiModalMessage
        from autogen_agentchat.ui import Console
        from autogen_core import Image as AGImage
        from example.llms import deepseek_model_client, qwenvl_model_client
        
        AUTOGEN_AVAILABLE = True
        logger.info("AutoGen模块导入成功")
        return True
        
    except ImportError as e:
        logger.warning(f"AutoGen依赖不可用: {e}")
        logger.warning("将使用降级模式")
        AUTOGEN_AVAILABLE = False
        return False
    except Exception as e:
        logger.error(f"AutoGen导入异常: {e}")
        AUTOGEN_AVAILABLE = False
        return False


class AutoGenClientManager:
    """AutoGen客户端管理器"""
    
    def __init__(self):
        """初始化AutoGen客户端管理器"""
        self.config = config.autogen
        self.agents = {}
        self.model_clients = {}
        
        # 初始化模型客户端
        self._initialize_model_clients()
    
    def _initialize_model_clients(self):
        """初始化模型客户端"""
        try:
            if not _try_import_autogen():
                logger.warning("AutoGen不可用，跳过模型客户端初始化")
                return
            
            from example.llms import deepseek_model_client, qwenvl_model_client
            
            # 初始化文本模型客户端
            try:
                self.model_clients['text'] = deepseek_model_client()
                logger.info("文本模型客户端初始化成功")
            except Exception as e:
                logger.warning(f"文本模型客户端初始化失败: {e}")
            
            # 初始化多模态模型客户端
            try:
                self.model_clients['multimodal'] = qwenvl_model_client()
                logger.info("多模态模型客户端初始化成功")
            except Exception as e:
                logger.warning(f"多模态模型客户端初始化失败: {e}")
                
        except Exception as e:
            logger.error(f"初始化模型客户端失败: {e}")
    
    def create_text_agent(self, name: str, system_message: str) -> Optional[Any]:
        """创建文本智能体"""
        try:
            if not _try_import_autogen():
                logger.warning("AutoGen不可用，无法创建文本智能体")
                return None
            
            from autogen_agentchat.agents import AssistantAgent
            
            model_client = self.model_clients.get('text')
            if not model_client:
                logger.error("文本模型客户端不可用")
                return None
            
            agent = AssistantAgent(
                name=name,
                model_client=model_client,
                system_message=system_message
            )
            
            self.agents[name] = agent
            logger.info(f"文本智能体创建成功: {name}")
            return agent
            
        except Exception as e:
            logger.error(f"创建文本智能体失败: {e}")
            return None
    
    def create_multimodal_agent(self, name: str, system_message: str) -> Optional[Any]:
        """创建多模态智能体"""
        try:
            if not _try_import_autogen():
                logger.warning("AutoGen不可用，无法创建多模态智能体")
                return None
            
            from autogen_agentchat.agents import AssistantAgent
            
            model_client = self.model_clients.get('multimodal')
            if not model_client:
                logger.error("多模态模型客户端不可用")
                return None
            
            agent = AssistantAgent(
                name=name,
                model_client=model_client,
                system_message=system_message
            )
            
            self.agents[name] = agent
            logger.info(f"多模态智能体创建成功: {name}")
            return agent
            
        except Exception as e:
            logger.error(f"创建多模态智能体失败: {e}")
            return None
    
    async def run_text_task(self, agent: Any, content: str) -> Dict[str, Any]:
        """运行文本任务"""
        try:
            if not agent:
                return {'success': False, 'error': '智能体不可用'}
            
            from autogen_agentchat.messages import TextMessage
            
            # 创建文本消息
            message = TextMessage(content=content, source="User")
            
            # 运行任务
            result = await agent.run(task=message)
            
            if result and result.messages:
                response_content = result.messages[-1].content
                return {
                    'success': True,
                    'content': response_content,
                    'message_count': len(result.messages)
                }
            else:
                return {'success': False, 'error': '智能体返回空响应'}
                
        except Exception as e:
            logger.error(f"运行文本任务失败: {e}")
            return {'success': False, 'error': str(e)}
    
    async def run_multimodal_task(self, agent: Any, text_content: str, 
                                image_path: Optional[str] = None) -> Dict[str, Any]:
        """运行多模态任务"""
        try:
            if not agent:
                return {'success': False, 'error': '智能体不可用'}
            
            from autogen_agentchat.messages import TextMessage, MultiModalMessage
            from autogen_core import Image as AGImage
            from PIL import Image
            
            # 如果有图片，创建多模态消息
            if image_path and Path(image_path).exists():
                try:
                    # 加载图片
                    pil_image = Image.open(image_path)
                    ag_image = AGImage(pil_image)
                    
                    # 创建多模态消息
                    message = MultiModalMessage(
                        content=[text_content, ag_image],
                        source="User"
                    )
                    
                except Exception as e:
                    logger.warning(f"加载图片失败，使用文本模式: {e}")
                    message = TextMessage(content=text_content, source="User")
            else:
                # 只有文本
                message = TextMessage(content=text_content, source="User")
            
            # 运行任务
            result = await agent.run(task=message)
            
            if result and result.messages:
                response_content = result.messages[-1].content
                return {
                    'success': True,
                    'content': response_content,
                    'message_count': len(result.messages),
                    'has_image': image_path is not None
                }
            else:
                return {'success': False, 'error': '智能体返回空响应'}
                
        except Exception as e:
            logger.error(f"运行多模态任务失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_agent(self, name: str) -> Optional[Any]:
        """获取智能体"""
        return self.agents.get(name)
    
    def list_agents(self) -> List[str]:
        """列出所有智能体"""
        return list(self.agents.keys())
    
    def remove_agent(self, name: str) -> bool:
        """移除智能体"""
        try:
            if name in self.agents:
                del self.agents[name]
                logger.info(f"智能体已移除: {name}")
                return True
            else:
                logger.warning(f"智能体不存在: {name}")
                return False
                
        except Exception as e:
            logger.error(f"移除智能体失败: {e}")
            return False
    
    def is_available(self) -> bool:
        """检查AutoGen是否可用"""
        return AUTOGEN_AVAILABLE and bool(self.model_clients)
    
    def get_status(self) -> Dict[str, Any]:
        """获取客户端状态"""
        return {
            'autogen_available': AUTOGEN_AVAILABLE,
            'model_clients': list(self.model_clients.keys()),
            'agents_count': len(self.agents),
            'agents': list(self.agents.keys())
        }
    
    def create_fallback_response(self, task_type: str, content: str) -> Dict[str, Any]:
        """创建降级响应"""
        fallback_responses = {
            'semantic_analysis': {
                'semantic_description': '基于规则的元素分析',
                'expected_action': '点击或查看',
                'element_category': '界面元素',
                'confidence_score': 0.5,
                'visual_context': '无视觉分析',
                'interaction_hints': '基于元素类型推断'
            },
            'case_analysis': {
                'extracted_actions': [],
                'extracted_assertions': [],
                'analysis_confidence': 0.5,
                'analysis_notes': '基于规则的用例分析'
            },
            'script_generation': {
                'script_content': '// 降级模式生成的脚本\n// 请手动完善脚本内容',
                'quality_score': 0.5,
                'generation_notes': '降级模式生成'
            }
        }
        
        return {
            'success': True,
            'content': json.dumps(fallback_responses.get(task_type, {}), ensure_ascii=False),
            'fallback': True,
            'message': f'AutoGen不可用，使用降级模式处理{task_type}'
        }
    
    def cleanup(self):
        """清理资源"""
        try:
            self.agents.clear()
            self.model_clients.clear()
            logger.info("AutoGen客户端管理器资源清理完成")
            
        except Exception as e:
            logger.warning(f"清理AutoGen客户端资源失败: {e}")
