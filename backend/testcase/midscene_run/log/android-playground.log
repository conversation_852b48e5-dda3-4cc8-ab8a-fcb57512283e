[2025-09-01T16:57:00.626+08:00] client connected, id: Qyq3losGy1Vt3pCsAAAB, client address: ::1
[2025-09-01T16:57:00.627+08:00] Socket request to get devices list
[2025-09-01T16:57:00.627+08:00] start to get devices list
[2025-09-01T16:57:01.003+08:00] adb server started
[2025-09-01T16:57:01.003+08:00] initialize adb client
[2025-09-01T16:57:01.003+08:00] success to initialize adb client
[2025-09-01T16:57:01.003+08:00] success to get adb client, start to request devices list
[2025-09-01T16:57:01.015+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:57:01.016+08:00] send devices list to client: [ { id: 'ASALE3741B000022', name: 'CM5-OP', status: 'device' } ]
[2025-09-01T16:57:02.000+08:00] start to get devices list
[2025-09-01T16:57:02.001+08:00] use existing adb client
[2025-09-01T16:57:02.001+08:00] success to get adb client, start to request devices list
[2025-09-01T16:57:02.005+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:57:02.005+08:00] devices list changed, push to all connected clients
[2025-09-01T16:57:02.005+08:00] auto select the first online device: ASALE3741B000022
[2025-09-01T16:57:02.399+08:00] received client request to get devices list
[2025-09-01T16:57:02.400+08:00] Socket request to get devices list
[2025-09-01T16:57:02.400+08:00] start to get devices list
[2025-09-01T16:57:02.400+08:00] use existing adb client
[2025-09-01T16:57:02.400+08:00] success to get adb client, start to request devices list
[2025-09-01T16:57:02.402+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:57:02.402+08:00] send devices list to client: [ { id: 'ASALE3741B000022', name: 'CM5-OP', status: 'device' } ]
[2025-09-01T16:57:02.523+08:00] received client request to switch device: ASALE3741B000022
[2025-09-01T16:57:02.523+08:00] device switched to: ASALE3741B000022
[2025-09-01T16:57:02.905+08:00] received client request to switch device: ASALE3741B000022
[2025-09-01T16:57:02.906+08:00] device switched to: ASALE3741B000022
[2025-09-01T16:57:03.500+08:00] client connected, id: a6hItCHXSnh6hhU1AAAD, client address: ::1
[2025-09-01T16:57:03.500+08:00] Socket request to get devices list
[2025-09-01T16:57:03.500+08:00] start to get devices list
[2025-09-01T16:57:03.500+08:00] use existing adb client
[2025-09-01T16:57:03.500+08:00] success to get adb client, start to request devices list
[2025-09-01T16:57:03.502+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:57:03.502+08:00] send devices list to client: [ { id: 'ASALE3741B000022', name: 'CM5-OP', status: 'device' } ]
[2025-09-01T16:57:03.920+08:00] received device connection request, options: { maxSize: 1024 }, client id: a6hItCHXSnh6hhU1AAAD
[2025-09-01T16:57:03.921+08:00] use existing adb client
[2025-09-01T16:57:03.971+08:00] starting scrcpy service, device id: ASALE3741B000022
[2025-09-01T16:57:04.967+08:00] scrcpy service started successfully
[2025-09-01T16:57:04.967+08:00] check scrcpyClient object structure: []
[2025-09-01T16:57:04.967+08:00] videoStream exists, type: object
[2025-09-01T16:57:04.967+08:00] videoStream is a Promise, waiting for resolution...
[2025-09-01T16:57:05.014+08:00] start to get devices list
[2025-09-01T16:57:05.015+08:00] use existing adb client
[2025-09-01T16:57:05.015+08:00] success to get adb client, start to request devices list
[2025-09-01T16:57:05.020+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:57:05.096+08:00] video stream fetched successfully, metadata: {
  deviceName: 'TECNO CM5',
  codec: 1748121140,
  width: 456,
  height: 1024
}
[2025-09-01T16:57:05.096+08:00] original metadata: {
  deviceName: 'TECNO CM5',
  codec: 1748121140,
  width: 456,
  height: 1024
}
[2025-09-01T16:57:05.096+08:00] prepare to send video-metadata event to client, data: {"deviceName":"TECNO CM5","codec":1748121140,"width":456,"height":1024}
[2025-09-01T16:57:05.096+08:00] video-metadata event sent to client, id: a6hItCHXSnh6hhU1AAAD
[2025-09-01T16:57:08.014+08:00] start to get devices list
[2025-09-01T16:57:08.015+08:00] use existing adb client
[2025-09-01T16:57:08.015+08:00] success to get adb client, start to request devices list
[2025-09-01T16:57:08.018+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:57:11.023+08:00] start to get devices list
[2025-09-01T16:57:11.023+08:00] use existing adb client
[2025-09-01T16:57:11.023+08:00] success to get adb client, start to request devices list
[2025-09-01T16:57:11.027+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:57:14.027+08:00] start to get devices list
[2025-09-01T16:57:14.027+08:00] use existing adb client
[2025-09-01T16:57:14.027+08:00] success to get adb client, start to request devices list
[2025-09-01T16:57:14.029+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:57:17.037+08:00] start to get devices list
[2025-09-01T16:57:17.038+08:00] use existing adb client
[2025-09-01T16:57:17.038+08:00] success to get adb client, start to request devices list
[2025-09-01T16:57:17.042+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:57:20.045+08:00] start to get devices list
[2025-09-01T16:57:20.046+08:00] use existing adb client
[2025-09-01T16:57:20.046+08:00] success to get adb client, start to request devices list
[2025-09-01T16:57:20.049+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:57:23.053+08:00] start to get devices list
[2025-09-01T16:57:23.053+08:00] use existing adb client
[2025-09-01T16:57:23.054+08:00] success to get adb client, start to request devices list
[2025-09-01T16:57:23.059+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:57:26.056+08:00] start to get devices list
[2025-09-01T16:57:26.057+08:00] use existing adb client
[2025-09-01T16:57:26.057+08:00] success to get adb client, start to request devices list
[2025-09-01T16:57:26.060+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:57:29.064+08:00] start to get devices list
[2025-09-01T16:57:29.064+08:00] use existing adb client
[2025-09-01T16:57:29.065+08:00] success to get adb client, start to request devices list
[2025-09-01T16:57:29.067+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:57:32.076+08:00] start to get devices list
[2025-09-01T16:57:32.077+08:00] use existing adb client
[2025-09-01T16:57:32.077+08:00] success to get adb client, start to request devices list
[2025-09-01T16:57:32.080+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:57:35.077+08:00] start to get devices list
[2025-09-01T16:57:35.077+08:00] use existing adb client
[2025-09-01T16:57:35.077+08:00] success to get adb client, start to request devices list
[2025-09-01T16:57:35.078+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:57:38.085+08:00] start to get devices list
[2025-09-01T16:57:38.085+08:00] use existing adb client
[2025-09-01T16:57:38.085+08:00] success to get adb client, start to request devices list
[2025-09-01T16:57:38.087+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:57:41.091+08:00] start to get devices list
[2025-09-01T16:57:41.091+08:00] use existing adb client
[2025-09-01T16:57:41.091+08:00] success to get adb client, start to request devices list
[2025-09-01T16:57:41.093+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:57:44.091+08:00] start to get devices list
[2025-09-01T16:57:44.091+08:00] use existing adb client
[2025-09-01T16:57:44.091+08:00] success to get adb client, start to request devices list
[2025-09-01T16:57:44.092+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:57:47.094+08:00] start to get devices list
[2025-09-01T16:57:47.094+08:00] use existing adb client
[2025-09-01T16:57:47.094+08:00] success to get adb client, start to request devices list
[2025-09-01T16:57:47.097+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:57:50.104+08:00] start to get devices list
[2025-09-01T16:57:50.104+08:00] use existing adb client
[2025-09-01T16:57:50.104+08:00] success to get adb client, start to request devices list
[2025-09-01T16:57:50.107+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:57:53.113+08:00] start to get devices list
[2025-09-01T16:57:53.114+08:00] use existing adb client
[2025-09-01T16:57:53.114+08:00] success to get adb client, start to request devices list
[2025-09-01T16:57:53.123+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:57:56.121+08:00] start to get devices list
[2025-09-01T16:57:56.121+08:00] use existing adb client
[2025-09-01T16:57:56.121+08:00] success to get adb client, start to request devices list
[2025-09-01T16:57:56.125+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:57:59.124+08:00] start to get devices list
[2025-09-01T16:57:59.124+08:00] use existing adb client
[2025-09-01T16:57:59.124+08:00] success to get adb client, start to request devices list
[2025-09-01T16:57:59.127+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:58:02.138+08:00] start to get devices list
[2025-09-01T16:58:02.138+08:00] use existing adb client
[2025-09-01T16:58:02.138+08:00] success to get adb client, start to request devices list
[2025-09-01T16:58:02.141+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:58:05.140+08:00] start to get devices list
[2025-09-01T16:58:05.140+08:00] use existing adb client
[2025-09-01T16:58:05.140+08:00] success to get adb client, start to request devices list
[2025-09-01T16:58:05.142+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:58:08.146+08:00] start to get devices list
[2025-09-01T16:58:08.146+08:00] use existing adb client
[2025-09-01T16:58:08.146+08:00] success to get adb client, start to request devices list
[2025-09-01T16:58:08.148+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:58:11.157+08:00] start to get devices list
[2025-09-01T16:58:11.158+08:00] use existing adb client
[2025-09-01T16:58:11.158+08:00] success to get adb client, start to request devices list
[2025-09-01T16:58:11.162+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:58:14.158+08:00] start to get devices list
[2025-09-01T16:58:14.158+08:00] use existing adb client
[2025-09-01T16:58:14.158+08:00] success to get adb client, start to request devices list
[2025-09-01T16:58:14.162+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:58:17.172+08:00] start to get devices list
[2025-09-01T16:58:17.172+08:00] use existing adb client
[2025-09-01T16:58:17.172+08:00] success to get adb client, start to request devices list
[2025-09-01T16:58:17.175+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:58:20.177+08:00] start to get devices list
[2025-09-01T16:58:20.177+08:00] use existing adb client
[2025-09-01T16:58:20.177+08:00] success to get adb client, start to request devices list
[2025-09-01T16:58:20.178+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:58:23.178+08:00] start to get devices list
[2025-09-01T16:58:23.178+08:00] use existing adb client
[2025-09-01T16:58:23.178+08:00] success to get adb client, start to request devices list
[2025-09-01T16:58:23.182+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:58:26.185+08:00] start to get devices list
[2025-09-01T16:58:26.185+08:00] use existing adb client
[2025-09-01T16:58:26.186+08:00] success to get adb client, start to request devices list
[2025-09-01T16:58:26.190+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:58:29.192+08:00] start to get devices list
[2025-09-01T16:58:29.192+08:00] use existing adb client
[2025-09-01T16:58:29.192+08:00] success to get adb client, start to request devices list
[2025-09-01T16:58:29.194+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:58:32.196+08:00] start to get devices list
[2025-09-01T16:58:32.196+08:00] use existing adb client
[2025-09-01T16:58:32.196+08:00] success to get adb client, start to request devices list
[2025-09-01T16:58:32.199+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:58:35.207+08:00] start to get devices list
[2025-09-01T16:58:35.207+08:00] use existing adb client
[2025-09-01T16:58:35.207+08:00] success to get adb client, start to request devices list
[2025-09-01T16:58:35.210+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:58:38.221+08:00] start to get devices list
[2025-09-01T16:58:38.221+08:00] use existing adb client
[2025-09-01T16:58:38.221+08:00] success to get adb client, start to request devices list
[2025-09-01T16:58:38.224+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:58:41.230+08:00] start to get devices list
[2025-09-01T16:58:41.230+08:00] use existing adb client
[2025-09-01T16:58:41.230+08:00] success to get adb client, start to request devices list
[2025-09-01T16:58:41.233+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:58:44.245+08:00] start to get devices list
[2025-09-01T16:58:44.245+08:00] use existing adb client
[2025-09-01T16:58:44.245+08:00] success to get adb client, start to request devices list
[2025-09-01T16:58:44.252+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:58:47.248+08:00] start to get devices list
[2025-09-01T16:58:47.248+08:00] use existing adb client
[2025-09-01T16:58:47.248+08:00] success to get adb client, start to request devices list
[2025-09-01T16:58:47.258+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:58:50.252+08:00] start to get devices list
[2025-09-01T16:58:50.252+08:00] use existing adb client
[2025-09-01T16:58:50.252+08:00] success to get adb client, start to request devices list
[2025-09-01T16:58:50.254+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:58:53.263+08:00] start to get devices list
[2025-09-01T16:58:53.264+08:00] use existing adb client
[2025-09-01T16:58:53.264+08:00] success to get adb client, start to request devices list
[2025-09-01T16:58:53.270+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:58:56.265+08:00] start to get devices list
[2025-09-01T16:58:56.265+08:00] use existing adb client
[2025-09-01T16:58:56.265+08:00] success to get adb client, start to request devices list
[2025-09-01T16:58:56.267+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:58:59.267+08:00] start to get devices list
[2025-09-01T16:58:59.268+08:00] use existing adb client
[2025-09-01T16:58:59.268+08:00] success to get adb client, start to request devices list
[2025-09-01T16:58:59.269+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:59:02.276+08:00] start to get devices list
[2025-09-01T16:59:02.276+08:00] use existing adb client
[2025-09-01T16:59:02.276+08:00] success to get adb client, start to request devices list
[2025-09-01T16:59:02.278+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:59:05.287+08:00] start to get devices list
[2025-09-01T16:59:05.288+08:00] use existing adb client
[2025-09-01T16:59:05.288+08:00] success to get adb client, start to request devices list
[2025-09-01T16:59:05.292+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:59:08.288+08:00] start to get devices list
[2025-09-01T16:59:08.288+08:00] use existing adb client
[2025-09-01T16:59:08.288+08:00] success to get adb client, start to request devices list
[2025-09-01T16:59:08.291+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:59:11.300+08:00] start to get devices list
[2025-09-01T16:59:11.300+08:00] use existing adb client
[2025-09-01T16:59:11.300+08:00] success to get adb client, start to request devices list
[2025-09-01T16:59:11.303+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:59:14.304+08:00] start to get devices list
[2025-09-01T16:59:14.304+08:00] use existing adb client
[2025-09-01T16:59:14.304+08:00] success to get adb client, start to request devices list
[2025-09-01T16:59:14.308+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:59:17.318+08:00] start to get devices list
[2025-09-01T16:59:17.318+08:00] use existing adb client
[2025-09-01T16:59:17.318+08:00] success to get adb client, start to request devices list
[2025-09-01T16:59:17.321+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:59:20.327+08:00] start to get devices list
[2025-09-01T16:59:20.327+08:00] use existing adb client
[2025-09-01T16:59:20.327+08:00] success to get adb client, start to request devices list
[2025-09-01T16:59:20.332+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:59:23.334+08:00] start to get devices list
[2025-09-01T16:59:23.334+08:00] use existing adb client
[2025-09-01T16:59:23.334+08:00] success to get adb client, start to request devices list
[2025-09-01T16:59:23.336+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:59:26.347+08:00] start to get devices list
[2025-09-01T16:59:26.348+08:00] use existing adb client
[2025-09-01T16:59:26.348+08:00] success to get adb client, start to request devices list
[2025-09-01T16:59:26.350+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:59:29.349+08:00] start to get devices list
[2025-09-01T16:59:29.349+08:00] use existing adb client
[2025-09-01T16:59:29.349+08:00] success to get adb client, start to request devices list
[2025-09-01T16:59:29.350+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:59:32.360+08:00] start to get devices list
[2025-09-01T16:59:32.361+08:00] use existing adb client
[2025-09-01T16:59:32.361+08:00] success to get adb client, start to request devices list
[2025-09-01T16:59:32.364+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:59:35.364+08:00] start to get devices list
[2025-09-01T16:59:35.364+08:00] use existing adb client
[2025-09-01T16:59:35.364+08:00] success to get adb client, start to request devices list
[2025-09-01T16:59:35.366+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:59:38.375+08:00] start to get devices list
[2025-09-01T16:59:38.375+08:00] use existing adb client
[2025-09-01T16:59:38.375+08:00] success to get adb client, start to request devices list
[2025-09-01T16:59:38.378+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:59:41.388+08:00] start to get devices list
[2025-09-01T16:59:41.388+08:00] use existing adb client
[2025-09-01T16:59:41.389+08:00] success to get adb client, start to request devices list
[2025-09-01T16:59:41.390+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:59:44.389+08:00] start to get devices list
[2025-09-01T16:59:44.389+08:00] use existing adb client
[2025-09-01T16:59:44.389+08:00] success to get adb client, start to request devices list
[2025-09-01T16:59:44.391+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:59:47.400+08:00] start to get devices list
[2025-09-01T16:59:47.400+08:00] use existing adb client
[2025-09-01T16:59:47.400+08:00] success to get adb client, start to request devices list
[2025-09-01T16:59:47.403+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:59:50.413+08:00] start to get devices list
[2025-09-01T16:59:50.414+08:00] use existing adb client
[2025-09-01T16:59:50.414+08:00] success to get adb client, start to request devices list
[2025-09-01T16:59:50.418+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:59:53.421+08:00] start to get devices list
[2025-09-01T16:59:53.422+08:00] use existing adb client
[2025-09-01T16:59:53.422+08:00] success to get adb client, start to request devices list
[2025-09-01T16:59:53.424+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:59:56.436+08:00] start to get devices list
[2025-09-01T16:59:56.436+08:00] use existing adb client
[2025-09-01T16:59:56.436+08:00] success to get adb client, start to request devices list
[2025-09-01T16:59:56.438+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T16:59:59.441+08:00] start to get devices list
[2025-09-01T16:59:59.441+08:00] use existing adb client
[2025-09-01T16:59:59.441+08:00] success to get adb client, start to request devices list
[2025-09-01T16:59:59.444+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:00:02.453+08:00] start to get devices list
[2025-09-01T17:00:02.454+08:00] use existing adb client
[2025-09-01T17:00:02.454+08:00] success to get adb client, start to request devices list
[2025-09-01T17:00:02.456+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:00:05.466+08:00] start to get devices list
[2025-09-01T17:00:05.466+08:00] use existing adb client
[2025-09-01T17:00:05.466+08:00] success to get adb client, start to request devices list
[2025-09-01T17:00:05.469+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:00:08.468+08:00] start to get devices list
[2025-09-01T17:00:08.468+08:00] use existing adb client
[2025-09-01T17:00:08.468+08:00] success to get adb client, start to request devices list
[2025-09-01T17:00:08.469+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:00:11.473+08:00] start to get devices list
[2025-09-01T17:00:11.473+08:00] use existing adb client
[2025-09-01T17:00:11.473+08:00] success to get adb client, start to request devices list
[2025-09-01T17:00:11.476+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:00:14.482+08:00] start to get devices list
[2025-09-01T17:00:14.482+08:00] use existing adb client
[2025-09-01T17:00:14.482+08:00] success to get adb client, start to request devices list
[2025-09-01T17:00:14.485+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:00:17.484+08:00] start to get devices list
[2025-09-01T17:00:17.485+08:00] use existing adb client
[2025-09-01T17:00:17.485+08:00] success to get adb client, start to request devices list
[2025-09-01T17:00:17.488+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:00:20.493+08:00] start to get devices list
[2025-09-01T17:00:20.493+08:00] use existing adb client
[2025-09-01T17:00:20.493+08:00] success to get adb client, start to request devices list
[2025-09-01T17:00:20.495+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:00:23.506+08:00] start to get devices list
[2025-09-01T17:00:23.506+08:00] use existing adb client
[2025-09-01T17:00:23.506+08:00] success to get adb client, start to request devices list
[2025-09-01T17:00:23.508+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:00:26.521+08:00] start to get devices list
[2025-09-01T17:00:26.521+08:00] use existing adb client
[2025-09-01T17:00:26.521+08:00] success to get adb client, start to request devices list
[2025-09-01T17:00:26.524+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:00:29.527+08:00] start to get devices list
[2025-09-01T17:00:29.527+08:00] use existing adb client
[2025-09-01T17:00:29.527+08:00] success to get adb client, start to request devices list
[2025-09-01T17:00:29.531+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:00:32.539+08:00] start to get devices list
[2025-09-01T17:00:32.540+08:00] use existing adb client
[2025-09-01T17:00:32.540+08:00] success to get adb client, start to request devices list
[2025-09-01T17:00:32.545+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:00:35.548+08:00] start to get devices list
[2025-09-01T17:00:35.549+08:00] use existing adb client
[2025-09-01T17:00:35.549+08:00] success to get adb client, start to request devices list
[2025-09-01T17:00:35.552+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:00:38.560+08:00] start to get devices list
[2025-09-01T17:00:38.560+08:00] use existing adb client
[2025-09-01T17:00:38.560+08:00] success to get adb client, start to request devices list
[2025-09-01T17:00:38.563+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:00:41.575+08:00] start to get devices list
[2025-09-01T17:00:41.575+08:00] use existing adb client
[2025-09-01T17:00:41.577+08:00] success to get adb client, start to request devices list
[2025-09-01T17:00:41.582+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:00:44.575+08:00] start to get devices list
[2025-09-01T17:00:44.575+08:00] use existing adb client
[2025-09-01T17:00:44.575+08:00] success to get adb client, start to request devices list
[2025-09-01T17:00:44.576+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:00:47.587+08:00] start to get devices list
[2025-09-01T17:00:47.587+08:00] use existing adb client
[2025-09-01T17:00:47.587+08:00] success to get adb client, start to request devices list
[2025-09-01T17:00:47.589+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:00:50.593+08:00] start to get devices list
[2025-09-01T17:00:50.593+08:00] use existing adb client
[2025-09-01T17:00:50.593+08:00] success to get adb client, start to request devices list
[2025-09-01T17:00:50.595+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:00:53.603+08:00] start to get devices list
[2025-09-01T17:00:53.603+08:00] use existing adb client
[2025-09-01T17:00:53.603+08:00] success to get adb client, start to request devices list
[2025-09-01T17:00:53.605+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:00:56.612+08:00] start to get devices list
[2025-09-01T17:00:56.612+08:00] use existing adb client
[2025-09-01T17:00:56.612+08:00] success to get adb client, start to request devices list
[2025-09-01T17:00:56.616+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:00:59.620+08:00] start to get devices list
[2025-09-01T17:00:59.621+08:00] use existing adb client
[2025-09-01T17:00:59.621+08:00] success to get adb client, start to request devices list
[2025-09-01T17:00:59.623+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:01:02.621+08:00] start to get devices list
[2025-09-01T17:01:02.621+08:00] use existing adb client
[2025-09-01T17:01:02.621+08:00] success to get adb client, start to request devices list
[2025-09-01T17:01:02.623+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:01:05.624+08:00] start to get devices list
[2025-09-01T17:01:05.624+08:00] use existing adb client
[2025-09-01T17:01:05.624+08:00] success to get adb client, start to request devices list
[2025-09-01T17:01:05.627+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:01:08.636+08:00] start to get devices list
[2025-09-01T17:01:08.636+08:00] use existing adb client
[2025-09-01T17:01:08.636+08:00] success to get adb client, start to request devices list
[2025-09-01T17:01:08.641+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:01:11.646+08:00] start to get devices list
[2025-09-01T17:01:11.646+08:00] use existing adb client
[2025-09-01T17:01:11.646+08:00] success to get adb client, start to request devices list
[2025-09-01T17:01:11.648+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:01:14.659+08:00] start to get devices list
[2025-09-01T17:01:14.660+08:00] use existing adb client
[2025-09-01T17:01:14.660+08:00] success to get adb client, start to request devices list
[2025-09-01T17:01:14.663+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:01:17.661+08:00] start to get devices list
[2025-09-01T17:01:17.661+08:00] use existing adb client
[2025-09-01T17:01:17.661+08:00] success to get adb client, start to request devices list
[2025-09-01T17:01:17.664+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:01:20.672+08:00] start to get devices list
[2025-09-01T17:01:20.672+08:00] use existing adb client
[2025-09-01T17:01:20.672+08:00] success to get adb client, start to request devices list
[2025-09-01T17:01:20.677+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:01:23.685+08:00] start to get devices list
[2025-09-01T17:01:23.685+08:00] use existing adb client
[2025-09-01T17:01:23.685+08:00] success to get adb client, start to request devices list
[2025-09-01T17:01:23.691+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:01:26.694+08:00] start to get devices list
[2025-09-01T17:01:26.694+08:00] use existing adb client
[2025-09-01T17:01:26.694+08:00] success to get adb client, start to request devices list
[2025-09-01T17:01:26.698+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:01:29.705+08:00] start to get devices list
[2025-09-01T17:01:29.705+08:00] use existing adb client
[2025-09-01T17:01:29.706+08:00] success to get adb client, start to request devices list
[2025-09-01T17:01:29.708+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:01:32.708+08:00] start to get devices list
[2025-09-01T17:01:32.709+08:00] use existing adb client
[2025-09-01T17:01:32.709+08:00] success to get adb client, start to request devices list
[2025-09-01T17:01:32.711+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:01:35.718+08:00] start to get devices list
[2025-09-01T17:01:35.719+08:00] use existing adb client
[2025-09-01T17:01:35.719+08:00] success to get adb client, start to request devices list
[2025-09-01T17:01:35.721+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:01:38.732+08:00] start to get devices list
[2025-09-01T17:01:38.732+08:00] use existing adb client
[2025-09-01T17:01:38.732+08:00] success to get adb client, start to request devices list
[2025-09-01T17:01:38.735+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:01:41.746+08:00] start to get devices list
[2025-09-01T17:01:41.746+08:00] use existing adb client
[2025-09-01T17:01:41.746+08:00] success to get adb client, start to request devices list
[2025-09-01T17:01:41.748+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:01:44.747+08:00] start to get devices list
[2025-09-01T17:01:44.747+08:00] use existing adb client
[2025-09-01T17:01:44.747+08:00] success to get adb client, start to request devices list
[2025-09-01T17:01:44.750+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:01:47.752+08:00] start to get devices list
[2025-09-01T17:01:47.752+08:00] use existing adb client
[2025-09-01T17:01:47.752+08:00] success to get adb client, start to request devices list
[2025-09-01T17:01:47.755+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:01:50.753+08:00] start to get devices list
[2025-09-01T17:01:50.753+08:00] use existing adb client
[2025-09-01T17:01:50.753+08:00] success to get adb client, start to request devices list
[2025-09-01T17:01:50.757+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:01:53.767+08:00] start to get devices list
[2025-09-01T17:01:53.767+08:00] use existing adb client
[2025-09-01T17:01:53.768+08:00] success to get adb client, start to request devices list
[2025-09-01T17:01:53.772+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:01:56.771+08:00] start to get devices list
[2025-09-01T17:01:56.772+08:00] use existing adb client
[2025-09-01T17:01:56.772+08:00] success to get adb client, start to request devices list
[2025-09-01T17:01:56.776+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:01:59.775+08:00] start to get devices list
[2025-09-01T17:01:59.775+08:00] use existing adb client
[2025-09-01T17:01:59.776+08:00] success to get adb client, start to request devices list
[2025-09-01T17:01:59.778+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:02:02.780+08:00] start to get devices list
[2025-09-01T17:02:02.780+08:00] use existing adb client
[2025-09-01T17:02:02.781+08:00] success to get adb client, start to request devices list
[2025-09-01T17:02:02.783+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:02:05.784+08:00] start to get devices list
[2025-09-01T17:02:05.784+08:00] use existing adb client
[2025-09-01T17:02:05.784+08:00] success to get adb client, start to request devices list
[2025-09-01T17:02:05.786+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:02:08.791+08:00] start to get devices list
[2025-09-01T17:02:08.791+08:00] use existing adb client
[2025-09-01T17:02:08.791+08:00] success to get adb client, start to request devices list
[2025-09-01T17:02:08.793+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:02:11.799+08:00] start to get devices list
[2025-09-01T17:02:11.799+08:00] use existing adb client
[2025-09-01T17:02:11.799+08:00] success to get adb client, start to request devices list
[2025-09-01T17:02:11.802+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:02:14.814+08:00] start to get devices list
[2025-09-01T17:02:14.814+08:00] use existing adb client
[2025-09-01T17:02:14.814+08:00] success to get adb client, start to request devices list
[2025-09-01T17:02:14.816+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:02:17.828+08:00] start to get devices list
[2025-09-01T17:02:17.828+08:00] use existing adb client
[2025-09-01T17:02:17.828+08:00] success to get adb client, start to request devices list
[2025-09-01T17:02:17.836+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:02:20.830+08:00] start to get devices list
[2025-09-01T17:02:20.830+08:00] use existing adb client
[2025-09-01T17:02:20.831+08:00] success to get adb client, start to request devices list
[2025-09-01T17:02:20.834+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:02:23.839+08:00] start to get devices list
[2025-09-01T17:02:23.839+08:00] use existing adb client
[2025-09-01T17:02:23.839+08:00] success to get adb client, start to request devices list
[2025-09-01T17:02:23.843+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:02:26.844+08:00] start to get devices list
[2025-09-01T17:02:26.844+08:00] use existing adb client
[2025-09-01T17:02:26.844+08:00] success to get adb client, start to request devices list
[2025-09-01T17:02:26.848+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:02:29.846+08:00] start to get devices list
[2025-09-01T17:02:29.846+08:00] use existing adb client
[2025-09-01T17:02:29.846+08:00] success to get adb client, start to request devices list
[2025-09-01T17:02:29.848+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:02:32.859+08:00] start to get devices list
[2025-09-01T17:02:32.859+08:00] use existing adb client
[2025-09-01T17:02:32.859+08:00] success to get adb client, start to request devices list
[2025-09-01T17:02:32.863+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:02:35.868+08:00] start to get devices list
[2025-09-01T17:02:35.869+08:00] use existing adb client
[2025-09-01T17:02:35.869+08:00] success to get adb client, start to request devices list
[2025-09-01T17:02:35.871+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:02:38.882+08:00] start to get devices list
[2025-09-01T17:02:38.882+08:00] use existing adb client
[2025-09-01T17:02:38.882+08:00] success to get adb client, start to request devices list
[2025-09-01T17:02:38.885+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:02:41.895+08:00] start to get devices list
[2025-09-01T17:02:41.895+08:00] use existing adb client
[2025-09-01T17:02:41.895+08:00] success to get adb client, start to request devices list
[2025-09-01T17:02:41.899+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:02:44.896+08:00] start to get devices list
[2025-09-01T17:02:44.896+08:00] use existing adb client
[2025-09-01T17:02:44.896+08:00] success to get adb client, start to request devices list
[2025-09-01T17:02:44.898+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:02:47.897+08:00] start to get devices list
[2025-09-01T17:02:47.897+08:00] use existing adb client
[2025-09-01T17:02:47.897+08:00] success to get adb client, start to request devices list
[2025-09-01T17:02:47.901+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:02:50.909+08:00] start to get devices list
[2025-09-01T17:02:50.909+08:00] use existing adb client
[2025-09-01T17:02:50.909+08:00] success to get adb client, start to request devices list
[2025-09-01T17:02:50.911+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:02:53.917+08:00] start to get devices list
[2025-09-01T17:02:53.917+08:00] use existing adb client
[2025-09-01T17:02:53.917+08:00] success to get adb client, start to request devices list
[2025-09-01T17:02:53.924+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:02:56.920+08:00] start to get devices list
[2025-09-01T17:02:56.920+08:00] use existing adb client
[2025-09-01T17:02:56.920+08:00] success to get adb client, start to request devices list
[2025-09-01T17:02:56.924+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:02:59.922+08:00] start to get devices list
[2025-09-01T17:02:59.922+08:00] use existing adb client
[2025-09-01T17:02:59.922+08:00] success to get adb client, start to request devices list
[2025-09-01T17:02:59.923+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:03:02.934+08:00] start to get devices list
[2025-09-01T17:03:02.934+08:00] use existing adb client
[2025-09-01T17:03:02.934+08:00] success to get adb client, start to request devices list
[2025-09-01T17:03:02.937+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:03:05.948+08:00] start to get devices list
[2025-09-01T17:03:05.948+08:00] use existing adb client
[2025-09-01T17:03:05.948+08:00] success to get adb client, start to request devices list
[2025-09-01T17:03:05.950+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:03:08.963+08:00] start to get devices list
[2025-09-01T17:03:08.964+08:00] use existing adb client
[2025-09-01T17:03:08.964+08:00] success to get adb client, start to request devices list
[2025-09-01T17:03:08.972+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:03:11.966+08:00] start to get devices list
[2025-09-01T17:03:11.966+08:00] use existing adb client
[2025-09-01T17:03:11.966+08:00] success to get adb client, start to request devices list
[2025-09-01T17:03:11.970+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:03:14.975+08:00] start to get devices list
[2025-09-01T17:03:14.976+08:00] use existing adb client
[2025-09-01T17:03:14.976+08:00] success to get adb client, start to request devices list
[2025-09-01T17:03:14.981+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:03:17.987+08:00] start to get devices list
[2025-09-01T17:03:17.987+08:00] use existing adb client
[2025-09-01T17:03:17.987+08:00] success to get adb client, start to request devices list
[2025-09-01T17:03:17.989+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:03:20.992+08:00] start to get devices list
[2025-09-01T17:03:20.992+08:00] use existing adb client
[2025-09-01T17:03:20.992+08:00] success to get adb client, start to request devices list
[2025-09-01T17:03:20.996+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:03:23.999+08:00] start to get devices list
[2025-09-01T17:03:23.999+08:00] use existing adb client
[2025-09-01T17:03:24.000+08:00] success to get adb client, start to request devices list
[2025-09-01T17:03:24.005+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:03:27.003+08:00] start to get devices list
[2025-09-01T17:03:27.004+08:00] use existing adb client
[2025-09-01T17:03:27.004+08:00] success to get adb client, start to request devices list
[2025-09-01T17:03:27.006+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:03:30.004+08:00] start to get devices list
[2025-09-01T17:03:30.004+08:00] use existing adb client
[2025-09-01T17:03:30.004+08:00] success to get adb client, start to request devices list
[2025-09-01T17:03:30.007+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:03:33.007+08:00] start to get devices list
[2025-09-01T17:03:33.007+08:00] use existing adb client
[2025-09-01T17:03:33.007+08:00] success to get adb client, start to request devices list
[2025-09-01T17:03:33.011+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:03:36.008+08:00] start to get devices list
[2025-09-01T17:03:36.008+08:00] use existing adb client
[2025-09-01T17:03:36.009+08:00] success to get adb client, start to request devices list
[2025-09-01T17:03:36.010+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:03:39.021+08:00] start to get devices list
[2025-09-01T17:03:39.021+08:00] use existing adb client
[2025-09-01T17:03:39.021+08:00] success to get adb client, start to request devices list
[2025-09-01T17:03:39.024+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:03:42.025+08:00] start to get devices list
[2025-09-01T17:03:42.025+08:00] use existing adb client
[2025-09-01T17:03:42.025+08:00] success to get adb client, start to request devices list
[2025-09-01T17:03:42.027+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:03:45.026+08:00] start to get devices list
[2025-09-01T17:03:45.026+08:00] use existing adb client
[2025-09-01T17:03:45.026+08:00] success to get adb client, start to request devices list
[2025-09-01T17:03:45.029+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:03:48.037+08:00] start to get devices list
[2025-09-01T17:03:48.038+08:00] use existing adb client
[2025-09-01T17:03:48.038+08:00] success to get adb client, start to request devices list
[2025-09-01T17:03:48.042+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:03:51.040+08:00] start to get devices list
[2025-09-01T17:03:51.040+08:00] use existing adb client
[2025-09-01T17:03:51.040+08:00] success to get adb client, start to request devices list
[2025-09-01T17:03:51.042+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:03:54.040+08:00] start to get devices list
[2025-09-01T17:03:54.040+08:00] use existing adb client
[2025-09-01T17:03:54.040+08:00] success to get adb client, start to request devices list
[2025-09-01T17:03:54.042+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:03:57.047+08:00] start to get devices list
[2025-09-01T17:03:57.048+08:00] use existing adb client
[2025-09-01T17:03:57.048+08:00] success to get adb client, start to request devices list
[2025-09-01T17:03:57.053+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:04:00.051+08:00] start to get devices list
[2025-09-01T17:04:00.051+08:00] use existing adb client
[2025-09-01T17:04:00.051+08:00] success to get adb client, start to request devices list
[2025-09-01T17:04:00.052+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:04:03.058+08:00] start to get devices list
[2025-09-01T17:04:03.058+08:00] use existing adb client
[2025-09-01T17:04:03.058+08:00] success to get adb client, start to request devices list
[2025-09-01T17:04:03.063+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:04:06.063+08:00] start to get devices list
[2025-09-01T17:04:06.064+08:00] use existing adb client
[2025-09-01T17:04:06.064+08:00] success to get adb client, start to request devices list
[2025-09-01T17:04:06.066+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:04:09.078+08:00] start to get devices list
[2025-09-01T17:04:09.078+08:00] use existing adb client
[2025-09-01T17:04:09.078+08:00] success to get adb client, start to request devices list
[2025-09-01T17:04:09.081+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:04:12.081+08:00] start to get devices list
[2025-09-01T17:04:12.081+08:00] use existing adb client
[2025-09-01T17:04:12.081+08:00] success to get adb client, start to request devices list
[2025-09-01T17:04:12.086+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:04:15.096+08:00] start to get devices list
[2025-09-01T17:04:15.096+08:00] use existing adb client
[2025-09-01T17:04:15.096+08:00] success to get adb client, start to request devices list
[2025-09-01T17:04:15.100+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:04:18.099+08:00] start to get devices list
[2025-09-01T17:04:18.099+08:00] use existing adb client
[2025-09-01T17:04:18.099+08:00] success to get adb client, start to request devices list
[2025-09-01T17:04:18.101+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:04:21.101+08:00] start to get devices list
[2025-09-01T17:04:21.102+08:00] use existing adb client
[2025-09-01T17:04:21.102+08:00] success to get adb client, start to request devices list
[2025-09-01T17:04:21.104+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:04:24.105+08:00] start to get devices list
[2025-09-01T17:04:24.105+08:00] use existing adb client
[2025-09-01T17:04:24.105+08:00] success to get adb client, start to request devices list
[2025-09-01T17:04:24.108+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:04:27.111+08:00] start to get devices list
[2025-09-01T17:04:27.111+08:00] use existing adb client
[2025-09-01T17:04:27.111+08:00] success to get adb client, start to request devices list
[2025-09-01T17:04:27.113+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:04:30.114+08:00] start to get devices list
[2025-09-01T17:04:30.115+08:00] use existing adb client
[2025-09-01T17:04:30.115+08:00] success to get adb client, start to request devices list
[2025-09-01T17:04:30.117+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:04:33.125+08:00] start to get devices list
[2025-09-01T17:04:33.126+08:00] use existing adb client
[2025-09-01T17:04:33.126+08:00] success to get adb client, start to request devices list
[2025-09-01T17:04:33.128+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:04:36.131+08:00] start to get devices list
[2025-09-01T17:04:36.132+08:00] use existing adb client
[2025-09-01T17:04:36.132+08:00] success to get adb client, start to request devices list
[2025-09-01T17:04:36.134+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:04:39.146+08:00] start to get devices list
[2025-09-01T17:04:39.147+08:00] use existing adb client
[2025-09-01T17:04:39.147+08:00] success to get adb client, start to request devices list
[2025-09-01T17:04:39.149+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:04:42.152+08:00] start to get devices list
[2025-09-01T17:04:42.152+08:00] use existing adb client
[2025-09-01T17:04:42.152+08:00] success to get adb client, start to request devices list
[2025-09-01T17:04:42.156+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:04:45.153+08:00] start to get devices list
[2025-09-01T17:04:45.153+08:00] use existing adb client
[2025-09-01T17:04:45.154+08:00] success to get adb client, start to request devices list
[2025-09-01T17:04:45.162+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:04:48.167+08:00] start to get devices list
[2025-09-01T17:04:48.167+08:00] use existing adb client
[2025-09-01T17:04:48.167+08:00] success to get adb client, start to request devices list
[2025-09-01T17:04:48.169+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:04:51.168+08:00] start to get devices list
[2025-09-01T17:04:51.168+08:00] use existing adb client
[2025-09-01T17:04:51.169+08:00] success to get adb client, start to request devices list
[2025-09-01T17:04:51.171+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:04:54.168+08:00] start to get devices list
[2025-09-01T17:04:54.168+08:00] use existing adb client
[2025-09-01T17:04:54.169+08:00] success to get adb client, start to request devices list
[2025-09-01T17:04:54.171+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:04:57.172+08:00] start to get devices list
[2025-09-01T17:04:57.172+08:00] use existing adb client
[2025-09-01T17:04:57.172+08:00] success to get adb client, start to request devices list
[2025-09-01T17:04:57.175+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:05:00.187+08:00] start to get devices list
[2025-09-01T17:05:00.187+08:00] use existing adb client
[2025-09-01T17:05:00.187+08:00] success to get adb client, start to request devices list
[2025-09-01T17:05:00.193+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:05:03.200+08:00] start to get devices list
[2025-09-01T17:05:03.201+08:00] use existing adb client
[2025-09-01T17:05:03.201+08:00] success to get adb client, start to request devices list
[2025-09-01T17:05:03.205+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:05:06.210+08:00] start to get devices list
[2025-09-01T17:05:06.210+08:00] use existing adb client
[2025-09-01T17:05:06.211+08:00] success to get adb client, start to request devices list
[2025-09-01T17:05:06.214+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:05:09.211+08:00] start to get devices list
[2025-09-01T17:05:09.211+08:00] use existing adb client
[2025-09-01T17:05:09.211+08:00] success to get adb client, start to request devices list
[2025-09-01T17:05:09.213+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:05:12.226+08:00] start to get devices list
[2025-09-01T17:05:12.227+08:00] use existing adb client
[2025-09-01T17:05:12.227+08:00] success to get adb client, start to request devices list
[2025-09-01T17:05:12.234+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:05:15.238+08:00] start to get devices list
[2025-09-01T17:05:15.238+08:00] use existing adb client
[2025-09-01T17:05:15.238+08:00] success to get adb client, start to request devices list
[2025-09-01T17:05:15.242+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:05:18.251+08:00] start to get devices list
[2025-09-01T17:05:18.252+08:00] use existing adb client
[2025-09-01T17:05:18.252+08:00] success to get adb client, start to request devices list
[2025-09-01T17:05:18.254+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:05:21.254+08:00] start to get devices list
[2025-09-01T17:05:21.255+08:00] use existing adb client
[2025-09-01T17:05:21.255+08:00] success to get adb client, start to request devices list
[2025-09-01T17:05:21.257+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:05:24.263+08:00] start to get devices list
[2025-09-01T17:05:24.263+08:00] use existing adb client
[2025-09-01T17:05:24.263+08:00] success to get adb client, start to request devices list
[2025-09-01T17:05:24.266+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:05:27.279+08:00] start to get devices list
[2025-09-01T17:05:27.279+08:00] use existing adb client
[2025-09-01T17:05:27.279+08:00] success to get adb client, start to request devices list
[2025-09-01T17:05:27.281+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:05:30.282+08:00] start to get devices list
[2025-09-01T17:05:30.282+08:00] use existing adb client
[2025-09-01T17:05:30.282+08:00] success to get adb client, start to request devices list
[2025-09-01T17:05:30.285+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:05:33.286+08:00] start to get devices list
[2025-09-01T17:05:33.286+08:00] use existing adb client
[2025-09-01T17:05:33.286+08:00] success to get adb client, start to request devices list
[2025-09-01T17:05:33.289+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:05:36.288+08:00] start to get devices list
[2025-09-01T17:05:36.288+08:00] use existing adb client
[2025-09-01T17:05:36.288+08:00] success to get adb client, start to request devices list
[2025-09-01T17:05:36.290+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:05:39.302+08:00] start to get devices list
[2025-09-01T17:05:39.302+08:00] use existing adb client
[2025-09-01T17:05:39.302+08:00] success to get adb client, start to request devices list
[2025-09-01T17:05:39.305+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:05:42.312+08:00] start to get devices list
[2025-09-01T17:05:42.312+08:00] use existing adb client
[2025-09-01T17:05:42.312+08:00] success to get adb client, start to request devices list
[2025-09-01T17:05:42.315+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:05:45.323+08:00] start to get devices list
[2025-09-01T17:05:45.324+08:00] use existing adb client
[2025-09-01T17:05:45.324+08:00] success to get adb client, start to request devices list
[2025-09-01T17:05:45.329+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:05:48.327+08:00] start to get devices list
[2025-09-01T17:05:48.328+08:00] use existing adb client
[2025-09-01T17:05:48.328+08:00] success to get adb client, start to request devices list
[2025-09-01T17:05:48.330+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:05:51.330+08:00] start to get devices list
[2025-09-01T17:05:51.330+08:00] use existing adb client
[2025-09-01T17:05:51.330+08:00] success to get adb client, start to request devices list
[2025-09-01T17:05:51.332+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:05:54.331+08:00] start to get devices list
[2025-09-01T17:05:54.331+08:00] use existing adb client
[2025-09-01T17:05:54.331+08:00] success to get adb client, start to request devices list
[2025-09-01T17:05:54.333+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:05:57.339+08:00] start to get devices list
[2025-09-01T17:05:57.340+08:00] use existing adb client
[2025-09-01T17:05:57.340+08:00] success to get adb client, start to request devices list
[2025-09-01T17:05:57.344+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:06:00.347+08:00] start to get devices list
[2025-09-01T17:06:00.347+08:00] use existing adb client
[2025-09-01T17:06:00.347+08:00] success to get adb client, start to request devices list
[2025-09-01T17:06:00.350+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:06:03.357+08:00] start to get devices list
[2025-09-01T17:06:03.358+08:00] use existing adb client
[2025-09-01T17:06:03.358+08:00] success to get adb client, start to request devices list
[2025-09-01T17:06:03.360+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:06:06.364+08:00] start to get devices list
[2025-09-01T17:06:06.364+08:00] use existing adb client
[2025-09-01T17:06:06.364+08:00] success to get adb client, start to request devices list
[2025-09-01T17:06:06.368+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:06:09.366+08:00] start to get devices list
[2025-09-01T17:06:09.367+08:00] use existing adb client
[2025-09-01T17:06:09.367+08:00] success to get adb client, start to request devices list
[2025-09-01T17:06:09.369+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:06:12.371+08:00] start to get devices list
[2025-09-01T17:06:12.371+08:00] use existing adb client
[2025-09-01T17:06:12.371+08:00] success to get adb client, start to request devices list
[2025-09-01T17:06:12.374+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:06:15.387+08:00] start to get devices list
[2025-09-01T17:06:15.387+08:00] use existing adb client
[2025-09-01T17:06:15.387+08:00] success to get adb client, start to request devices list
[2025-09-01T17:06:15.393+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:06:18.394+08:00] start to get devices list
[2025-09-01T17:06:18.395+08:00] use existing adb client
[2025-09-01T17:06:18.395+08:00] success to get adb client, start to request devices list
[2025-09-01T17:06:18.398+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:06:21.404+08:00] start to get devices list
[2025-09-01T17:06:21.404+08:00] use existing adb client
[2025-09-01T17:06:21.404+08:00] success to get adb client, start to request devices list
[2025-09-01T17:06:21.407+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:06:24.418+08:00] start to get devices list
[2025-09-01T17:06:24.418+08:00] use existing adb client
[2025-09-01T17:06:24.418+08:00] success to get adb client, start to request devices list
[2025-09-01T17:06:24.423+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:06:27.423+08:00] start to get devices list
[2025-09-01T17:06:27.424+08:00] use existing adb client
[2025-09-01T17:06:27.424+08:00] success to get adb client, start to request devices list
[2025-09-01T17:06:27.429+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:06:30.433+08:00] start to get devices list
[2025-09-01T17:06:30.433+08:00] use existing adb client
[2025-09-01T17:06:30.433+08:00] success to get adb client, start to request devices list
[2025-09-01T17:06:30.435+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:06:33.438+08:00] start to get devices list
[2025-09-01T17:06:33.439+08:00] use existing adb client
[2025-09-01T17:06:33.440+08:00] success to get adb client, start to request devices list
[2025-09-01T17:06:33.444+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:06:36.452+08:00] start to get devices list
[2025-09-01T17:06:36.452+08:00] use existing adb client
[2025-09-01T17:06:36.452+08:00] success to get adb client, start to request devices list
[2025-09-01T17:06:36.455+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:06:39.452+08:00] start to get devices list
[2025-09-01T17:06:39.452+08:00] use existing adb client
[2025-09-01T17:06:39.452+08:00] success to get adb client, start to request devices list
[2025-09-01T17:06:39.454+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:06:42.460+08:00] start to get devices list
[2025-09-01T17:06:42.460+08:00] use existing adb client
[2025-09-01T17:06:42.460+08:00] success to get adb client, start to request devices list
[2025-09-01T17:06:42.464+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:06:45.468+08:00] start to get devices list
[2025-09-01T17:06:45.469+08:00] use existing adb client
[2025-09-01T17:06:45.470+08:00] success to get adb client, start to request devices list
[2025-09-01T17:06:45.475+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:06:48.470+08:00] start to get devices list
[2025-09-01T17:06:48.470+08:00] use existing adb client
[2025-09-01T17:06:48.470+08:00] success to get adb client, start to request devices list
[2025-09-01T17:06:48.473+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:06:51.480+08:00] start to get devices list
[2025-09-01T17:06:51.480+08:00] use existing adb client
[2025-09-01T17:06:51.480+08:00] success to get adb client, start to request devices list
[2025-09-01T17:06:51.485+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:06:54.488+08:00] start to get devices list
[2025-09-01T17:06:54.488+08:00] use existing adb client
[2025-09-01T17:06:54.488+08:00] success to get adb client, start to request devices list
[2025-09-01T17:06:54.490+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:06:57.492+08:00] start to get devices list
[2025-09-01T17:06:57.492+08:00] use existing adb client
[2025-09-01T17:06:57.492+08:00] success to get adb client, start to request devices list
[2025-09-01T17:06:57.495+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:07:00.503+08:00] start to get devices list
[2025-09-01T17:07:00.504+08:00] use existing adb client
[2025-09-01T17:07:00.504+08:00] success to get adb client, start to request devices list
[2025-09-01T17:07:00.507+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:07:03.514+08:00] start to get devices list
[2025-09-01T17:07:03.515+08:00] use existing adb client
[2025-09-01T17:07:03.515+08:00] success to get adb client, start to request devices list
[2025-09-01T17:07:03.521+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:07:06.517+08:00] start to get devices list
[2025-09-01T17:07:06.518+08:00] use existing adb client
[2025-09-01T17:07:06.518+08:00] success to get adb client, start to request devices list
[2025-09-01T17:07:06.522+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:07:09.529+08:00] start to get devices list
[2025-09-01T17:07:09.529+08:00] use existing adb client
[2025-09-01T17:07:09.529+08:00] success to get adb client, start to request devices list
[2025-09-01T17:07:09.531+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:07:12.540+08:00] start to get devices list
[2025-09-01T17:07:12.540+08:00] use existing adb client
[2025-09-01T17:07:12.540+08:00] success to get adb client, start to request devices list
[2025-09-01T17:07:12.543+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:07:15.540+08:00] start to get devices list
[2025-09-01T17:07:15.540+08:00] use existing adb client
[2025-09-01T17:07:15.540+08:00] success to get adb client, start to request devices list
[2025-09-01T17:07:15.544+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:07:18.552+08:00] start to get devices list
[2025-09-01T17:07:18.553+08:00] use existing adb client
[2025-09-01T17:07:18.553+08:00] success to get adb client, start to request devices list
[2025-09-01T17:07:18.555+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:07:21.564+08:00] start to get devices list
[2025-09-01T17:07:21.564+08:00] use existing adb client
[2025-09-01T17:07:21.565+08:00] success to get adb client, start to request devices list
[2025-09-01T17:07:21.567+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:07:24.573+08:00] start to get devices list
[2025-09-01T17:07:24.573+08:00] use existing adb client
[2025-09-01T17:07:24.573+08:00] success to get adb client, start to request devices list
[2025-09-01T17:07:24.576+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:07:27.574+08:00] start to get devices list
[2025-09-01T17:07:27.575+08:00] use existing adb client
[2025-09-01T17:07:27.575+08:00] success to get adb client, start to request devices list
[2025-09-01T17:07:27.577+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:07:30.576+08:00] start to get devices list
[2025-09-01T17:07:30.577+08:00] use existing adb client
[2025-09-01T17:07:30.577+08:00] success to get adb client, start to request devices list
[2025-09-01T17:07:30.578+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:07:33.588+08:00] start to get devices list
[2025-09-01T17:07:33.588+08:00] use existing adb client
[2025-09-01T17:07:33.588+08:00] success to get adb client, start to request devices list
[2025-09-01T17:07:33.591+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:07:36.600+08:00] start to get devices list
[2025-09-01T17:07:36.600+08:00] use existing adb client
[2025-09-01T17:07:36.600+08:00] success to get adb client, start to request devices list
[2025-09-01T17:07:36.602+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:07:39.608+08:00] start to get devices list
[2025-09-01T17:07:39.609+08:00] use existing adb client
[2025-09-01T17:07:39.609+08:00] success to get adb client, start to request devices list
[2025-09-01T17:07:39.611+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:07:42.622+08:00] start to get devices list
[2025-09-01T17:07:42.622+08:00] use existing adb client
[2025-09-01T17:07:42.623+08:00] success to get adb client, start to request devices list
[2025-09-01T17:07:42.625+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:07:45.630+08:00] start to get devices list
[2025-09-01T17:07:45.630+08:00] use existing adb client
[2025-09-01T17:07:45.630+08:00] success to get adb client, start to request devices list
[2025-09-01T17:07:45.633+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:07:48.640+08:00] start to get devices list
[2025-09-01T17:07:48.641+08:00] use existing adb client
[2025-09-01T17:07:48.641+08:00] success to get adb client, start to request devices list
[2025-09-01T17:07:48.642+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:07:51.647+08:00] start to get devices list
[2025-09-01T17:07:51.647+08:00] use existing adb client
[2025-09-01T17:07:51.647+08:00] success to get adb client, start to request devices list
[2025-09-01T17:07:51.649+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:07:54.655+08:00] start to get devices list
[2025-09-01T17:07:54.655+08:00] use existing adb client
[2025-09-01T17:07:54.655+08:00] success to get adb client, start to request devices list
[2025-09-01T17:07:54.658+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:07:57.656+08:00] start to get devices list
[2025-09-01T17:07:57.656+08:00] use existing adb client
[2025-09-01T17:07:57.656+08:00] success to get adb client, start to request devices list
[2025-09-01T17:07:57.659+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:08:00.660+08:00] start to get devices list
[2025-09-01T17:08:00.660+08:00] use existing adb client
[2025-09-01T17:08:00.660+08:00] success to get adb client, start to request devices list
[2025-09-01T17:08:00.662+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:08:03.672+08:00] start to get devices list
[2025-09-01T17:08:03.672+08:00] use existing adb client
[2025-09-01T17:08:03.672+08:00] success to get adb client, start to request devices list
[2025-09-01T17:08:03.674+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:08:06.686+08:00] start to get devices list
[2025-09-01T17:08:06.686+08:00] use existing adb client
[2025-09-01T17:08:06.686+08:00] success to get adb client, start to request devices list
[2025-09-01T17:08:06.690+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:08:09.694+08:00] start to get devices list
[2025-09-01T17:08:09.694+08:00] use existing adb client
[2025-09-01T17:08:09.694+08:00] success to get adb client, start to request devices list
[2025-09-01T17:08:09.697+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:08:12.701+08:00] start to get devices list
[2025-09-01T17:08:12.701+08:00] use existing adb client
[2025-09-01T17:08:12.701+08:00] success to get adb client, start to request devices list
[2025-09-01T17:08:12.704+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:08:15.713+08:00] start to get devices list
[2025-09-01T17:08:15.714+08:00] use existing adb client
[2025-09-01T17:08:15.714+08:00] success to get adb client, start to request devices list
[2025-09-01T17:08:15.718+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:08:18.721+08:00] start to get devices list
[2025-09-01T17:08:18.721+08:00] use existing adb client
[2025-09-01T17:08:18.721+08:00] success to get adb client, start to request devices list
[2025-09-01T17:08:18.724+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:08:21.724+08:00] start to get devices list
[2025-09-01T17:08:21.725+08:00] use existing adb client
[2025-09-01T17:08:21.725+08:00] success to get adb client, start to request devices list
[2025-09-01T17:08:21.728+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:08:24.729+08:00] start to get devices list
[2025-09-01T17:08:24.729+08:00] use existing adb client
[2025-09-01T17:08:24.729+08:00] success to get adb client, start to request devices list
[2025-09-01T17:08:24.735+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:08:27.740+08:00] start to get devices list
[2025-09-01T17:08:27.741+08:00] use existing adb client
[2025-09-01T17:08:27.741+08:00] success to get adb client, start to request devices list
[2025-09-01T17:08:27.743+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:08:30.741+08:00] start to get devices list
[2025-09-01T17:08:30.741+08:00] use existing adb client
[2025-09-01T17:08:30.741+08:00] success to get adb client, start to request devices list
[2025-09-01T17:08:30.743+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:08:33.750+08:00] start to get devices list
[2025-09-01T17:08:33.751+08:00] use existing adb client
[2025-09-01T17:08:33.751+08:00] success to get adb client, start to request devices list
[2025-09-01T17:08:33.754+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:08:36.763+08:00] start to get devices list
[2025-09-01T17:08:36.763+08:00] use existing adb client
[2025-09-01T17:08:36.763+08:00] success to get adb client, start to request devices list
[2025-09-01T17:08:36.766+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:08:39.765+08:00] start to get devices list
[2025-09-01T17:08:39.766+08:00] use existing adb client
[2025-09-01T17:08:39.766+08:00] success to get adb client, start to request devices list
[2025-09-01T17:08:39.770+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:08:42.770+08:00] start to get devices list
[2025-09-01T17:08:42.771+08:00] use existing adb client
[2025-09-01T17:08:42.771+08:00] success to get adb client, start to request devices list
[2025-09-01T17:08:42.776+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:08:45.781+08:00] start to get devices list
[2025-09-01T17:08:45.781+08:00] use existing adb client
[2025-09-01T17:08:45.781+08:00] success to get adb client, start to request devices list
[2025-09-01T17:08:45.784+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:08:48.785+08:00] start to get devices list
[2025-09-01T17:08:48.786+08:00] use existing adb client
[2025-09-01T17:08:48.786+08:00] success to get adb client, start to request devices list
[2025-09-01T17:08:48.788+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:08:51.790+08:00] start to get devices list
[2025-09-01T17:08:51.791+08:00] use existing adb client
[2025-09-01T17:08:51.791+08:00] success to get adb client, start to request devices list
[2025-09-01T17:08:51.795+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:08:54.800+08:00] start to get devices list
[2025-09-01T17:08:54.801+08:00] use existing adb client
[2025-09-01T17:08:54.801+08:00] success to get adb client, start to request devices list
[2025-09-01T17:08:54.804+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:08:57.807+08:00] start to get devices list
[2025-09-01T17:08:57.807+08:00] use existing adb client
[2025-09-01T17:08:57.808+08:00] success to get adb client, start to request devices list
[2025-09-01T17:08:57.811+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:09:00.820+08:00] start to get devices list
[2025-09-01T17:09:00.821+08:00] use existing adb client
[2025-09-01T17:09:00.821+08:00] success to get adb client, start to request devices list
[2025-09-01T17:09:00.823+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:09:03.831+08:00] start to get devices list
[2025-09-01T17:09:03.832+08:00] use existing adb client
[2025-09-01T17:09:03.832+08:00] success to get adb client, start to request devices list
[2025-09-01T17:09:03.834+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:09:06.840+08:00] start to get devices list
[2025-09-01T17:09:06.841+08:00] use existing adb client
[2025-09-01T17:09:06.841+08:00] success to get adb client, start to request devices list
[2025-09-01T17:09:06.843+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:09:09.841+08:00] start to get devices list
[2025-09-01T17:09:09.841+08:00] use existing adb client
[2025-09-01T17:09:09.841+08:00] success to get adb client, start to request devices list
[2025-09-01T17:09:09.844+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:09:12.847+08:00] start to get devices list
[2025-09-01T17:09:12.847+08:00] use existing adb client
[2025-09-01T17:09:12.847+08:00] success to get adb client, start to request devices list
[2025-09-01T17:09:12.850+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:09:15.857+08:00] start to get devices list
[2025-09-01T17:09:15.857+08:00] use existing adb client
[2025-09-01T17:09:15.857+08:00] success to get adb client, start to request devices list
[2025-09-01T17:09:15.860+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:09:18.858+08:00] start to get devices list
[2025-09-01T17:09:18.858+08:00] use existing adb client
[2025-09-01T17:09:18.858+08:00] success to get adb client, start to request devices list
[2025-09-01T17:09:18.862+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:09:21.870+08:00] start to get devices list
[2025-09-01T17:09:21.870+08:00] use existing adb client
[2025-09-01T17:09:21.870+08:00] success to get adb client, start to request devices list
[2025-09-01T17:09:21.872+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:09:24.880+08:00] start to get devices list
[2025-09-01T17:09:24.880+08:00] use existing adb client
[2025-09-01T17:09:24.881+08:00] success to get adb client, start to request devices list
[2025-09-01T17:09:24.884+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:09:27.892+08:00] start to get devices list
[2025-09-01T17:09:27.892+08:00] use existing adb client
[2025-09-01T17:09:27.892+08:00] success to get adb client, start to request devices list
[2025-09-01T17:09:27.897+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:09:30.894+08:00] start to get devices list
[2025-09-01T17:09:30.895+08:00] use existing adb client
[2025-09-01T17:09:30.895+08:00] success to get adb client, start to request devices list
[2025-09-01T17:09:30.898+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:09:33.899+08:00] start to get devices list
[2025-09-01T17:09:33.899+08:00] use existing adb client
[2025-09-01T17:09:33.899+08:00] success to get adb client, start to request devices list
[2025-09-01T17:09:33.902+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:09:36.909+08:00] start to get devices list
[2025-09-01T17:09:36.910+08:00] use existing adb client
[2025-09-01T17:09:36.910+08:00] success to get adb client, start to request devices list
[2025-09-01T17:09:36.913+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:09:39.914+08:00] start to get devices list
[2025-09-01T17:09:39.914+08:00] use existing adb client
[2025-09-01T17:09:39.914+08:00] success to get adb client, start to request devices list
[2025-09-01T17:09:39.917+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:09:42.927+08:00] start to get devices list
[2025-09-01T17:09:42.927+08:00] use existing adb client
[2025-09-01T17:09:42.927+08:00] success to get adb client, start to request devices list
[2025-09-01T17:09:42.929+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:09:45.931+08:00] start to get devices list
[2025-09-01T17:09:45.931+08:00] use existing adb client
[2025-09-01T17:09:45.931+08:00] success to get adb client, start to request devices list
[2025-09-01T17:09:45.934+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:09:48.935+08:00] start to get devices list
[2025-09-01T17:09:48.935+08:00] use existing adb client
[2025-09-01T17:09:48.935+08:00] success to get adb client, start to request devices list
[2025-09-01T17:09:48.938+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:09:51.951+08:00] start to get devices list
[2025-09-01T17:09:51.951+08:00] use existing adb client
[2025-09-01T17:09:51.951+08:00] success to get adb client, start to request devices list
[2025-09-01T17:09:51.954+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:09:54.957+08:00] start to get devices list
[2025-09-01T17:09:54.957+08:00] use existing adb client
[2025-09-01T17:09:54.957+08:00] success to get adb client, start to request devices list
[2025-09-01T17:09:54.959+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:09:57.966+08:00] start to get devices list
[2025-09-01T17:09:57.966+08:00] use existing adb client
[2025-09-01T17:09:57.966+08:00] success to get adb client, start to request devices list
[2025-09-01T17:09:57.969+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:10:00.980+08:00] start to get devices list
[2025-09-01T17:10:00.980+08:00] use existing adb client
[2025-09-01T17:10:00.980+08:00] success to get adb client, start to request devices list
[2025-09-01T17:10:00.982+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:10:03.992+08:00] start to get devices list
[2025-09-01T17:10:03.992+08:00] use existing adb client
[2025-09-01T17:10:03.992+08:00] success to get adb client, start to request devices list
[2025-09-01T17:10:03.994+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:10:06.996+08:00] start to get devices list
[2025-09-01T17:10:06.997+08:00] use existing adb client
[2025-09-01T17:10:06.997+08:00] success to get adb client, start to request devices list
[2025-09-01T17:10:06.999+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:10:10.006+08:00] start to get devices list
[2025-09-01T17:10:10.007+08:00] use existing adb client
[2025-09-01T17:10:10.007+08:00] success to get adb client, start to request devices list
[2025-09-01T17:10:10.011+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:10:13.021+08:00] start to get devices list
[2025-09-01T17:10:13.021+08:00] use existing adb client
[2025-09-01T17:10:13.021+08:00] success to get adb client, start to request devices list
[2025-09-01T17:10:13.023+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:10:16.026+08:00] start to get devices list
[2025-09-01T17:10:16.026+08:00] use existing adb client
[2025-09-01T17:10:16.026+08:00] success to get adb client, start to request devices list
[2025-09-01T17:10:16.028+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:10:19.034+08:00] start to get devices list
[2025-09-01T17:10:19.034+08:00] use existing adb client
[2025-09-01T17:10:19.034+08:00] success to get adb client, start to request devices list
[2025-09-01T17:10:19.037+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:10:22.042+08:00] start to get devices list
[2025-09-01T17:10:22.042+08:00] use existing adb client
[2025-09-01T17:10:22.042+08:00] success to get adb client, start to request devices list
[2025-09-01T17:10:22.045+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:10:25.054+08:00] start to get devices list
[2025-09-01T17:10:25.054+08:00] use existing adb client
[2025-09-01T17:10:25.054+08:00] success to get adb client, start to request devices list
[2025-09-01T17:10:25.056+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:10:28.058+08:00] start to get devices list
[2025-09-01T17:10:28.058+08:00] use existing adb client
[2025-09-01T17:10:28.058+08:00] success to get adb client, start to request devices list
[2025-09-01T17:10:28.062+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:10:31.069+08:00] start to get devices list
[2025-09-01T17:10:31.070+08:00] use existing adb client
[2025-09-01T17:10:31.070+08:00] success to get adb client, start to request devices list
[2025-09-01T17:10:31.077+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:10:34.077+08:00] start to get devices list
[2025-09-01T17:10:34.078+08:00] use existing adb client
[2025-09-01T17:10:34.078+08:00] success to get adb client, start to request devices list
[2025-09-01T17:10:34.082+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:10:37.080+08:00] start to get devices list
[2025-09-01T17:10:37.080+08:00] use existing adb client
[2025-09-01T17:10:37.080+08:00] success to get adb client, start to request devices list
[2025-09-01T17:10:37.082+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:10:40.082+08:00] start to get devices list
[2025-09-01T17:10:40.082+08:00] use existing adb client
[2025-09-01T17:10:40.082+08:00] success to get adb client, start to request devices list
[2025-09-01T17:10:40.084+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:10:43.087+08:00] start to get devices list
[2025-09-01T17:10:43.087+08:00] use existing adb client
[2025-09-01T17:10:43.087+08:00] success to get adb client, start to request devices list
[2025-09-01T17:10:43.107+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:10:46.092+08:00] start to get devices list
[2025-09-01T17:10:46.092+08:00] use existing adb client
[2025-09-01T17:10:46.092+08:00] success to get adb client, start to request devices list
[2025-09-01T17:10:46.093+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:10:49.108+08:00] start to get devices list
[2025-09-01T17:10:49.108+08:00] use existing adb client
[2025-09-01T17:10:49.108+08:00] success to get adb client, start to request devices list
[2025-09-01T17:10:49.110+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:10:52.108+08:00] start to get devices list
[2025-09-01T17:10:52.108+08:00] use existing adb client
[2025-09-01T17:10:52.108+08:00] success to get adb client, start to request devices list
[2025-09-01T17:10:52.110+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:10:55.124+08:00] start to get devices list
[2025-09-01T17:10:55.124+08:00] use existing adb client
[2025-09-01T17:10:55.124+08:00] success to get adb client, start to request devices list
[2025-09-01T17:10:55.127+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:10:58.128+08:00] start to get devices list
[2025-09-01T17:10:58.128+08:00] use existing adb client
[2025-09-01T17:10:58.128+08:00] success to get adb client, start to request devices list
[2025-09-01T17:10:58.130+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:11:01.139+08:00] start to get devices list
[2025-09-01T17:11:01.139+08:00] use existing adb client
[2025-09-01T17:11:01.139+08:00] success to get adb client, start to request devices list
[2025-09-01T17:11:01.141+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:11:04.150+08:00] start to get devices list
[2025-09-01T17:11:04.151+08:00] use existing adb client
[2025-09-01T17:11:04.151+08:00] success to get adb client, start to request devices list
[2025-09-01T17:11:04.153+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:11:07.165+08:00] start to get devices list
[2025-09-01T17:11:07.165+08:00] use existing adb client
[2025-09-01T17:11:07.165+08:00] success to get adb client, start to request devices list
[2025-09-01T17:11:07.169+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:11:10.170+08:00] start to get devices list
[2025-09-01T17:11:10.170+08:00] use existing adb client
[2025-09-01T17:11:10.170+08:00] success to get adb client, start to request devices list
[2025-09-01T17:11:10.172+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:11:13.180+08:00] start to get devices list
[2025-09-01T17:11:13.180+08:00] use existing adb client
[2025-09-01T17:11:13.180+08:00] success to get adb client, start to request devices list
[2025-09-01T17:11:13.187+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:11:16.189+08:00] start to get devices list
[2025-09-01T17:11:16.189+08:00] use existing adb client
[2025-09-01T17:11:16.189+08:00] success to get adb client, start to request devices list
[2025-09-01T17:11:16.191+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:11:19.199+08:00] start to get devices list
[2025-09-01T17:11:19.199+08:00] use existing adb client
[2025-09-01T17:11:19.200+08:00] success to get adb client, start to request devices list
[2025-09-01T17:11:19.204+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:11:22.204+08:00] start to get devices list
[2025-09-01T17:11:22.205+08:00] use existing adb client
[2025-09-01T17:11:22.205+08:00] success to get adb client, start to request devices list
[2025-09-01T17:11:22.208+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:11:25.205+08:00] start to get devices list
[2025-09-01T17:11:25.205+08:00] use existing adb client
[2025-09-01T17:11:25.205+08:00] success to get adb client, start to request devices list
[2025-09-01T17:11:25.208+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:11:28.216+08:00] start to get devices list
[2025-09-01T17:11:28.216+08:00] use existing adb client
[2025-09-01T17:11:28.216+08:00] success to get adb client, start to request devices list
[2025-09-01T17:11:28.219+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:11:31.228+08:00] start to get devices list
[2025-09-01T17:11:31.228+08:00] use existing adb client
[2025-09-01T17:11:31.228+08:00] success to get adb client, start to request devices list
[2025-09-01T17:11:31.230+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:11:34.235+08:00] start to get devices list
[2025-09-01T17:11:34.236+08:00] use existing adb client
[2025-09-01T17:11:34.236+08:00] success to get adb client, start to request devices list
[2025-09-01T17:11:34.237+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:11:37.250+08:00] start to get devices list
[2025-09-01T17:11:37.250+08:00] use existing adb client
[2025-09-01T17:11:37.250+08:00] success to get adb client, start to request devices list
[2025-09-01T17:11:37.255+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:11:40.264+08:00] start to get devices list
[2025-09-01T17:11:40.265+08:00] use existing adb client
[2025-09-01T17:11:40.265+08:00] success to get adb client, start to request devices list
[2025-09-01T17:11:40.268+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:11:43.273+08:00] start to get devices list
[2025-09-01T17:11:43.274+08:00] use existing adb client
[2025-09-01T17:11:43.274+08:00] success to get adb client, start to request devices list
[2025-09-01T17:11:43.281+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:11:46.281+08:00] start to get devices list
[2025-09-01T17:11:46.281+08:00] use existing adb client
[2025-09-01T17:11:46.281+08:00] success to get adb client, start to request devices list
[2025-09-01T17:11:46.285+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:11:49.282+08:00] start to get devices list
[2025-09-01T17:11:49.282+08:00] use existing adb client
[2025-09-01T17:11:49.282+08:00] success to get adb client, start to request devices list
[2025-09-01T17:11:49.286+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:11:52.289+08:00] start to get devices list
[2025-09-01T17:11:52.289+08:00] use existing adb client
[2025-09-01T17:11:52.289+08:00] success to get adb client, start to request devices list
[2025-09-01T17:11:52.294+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:11:55.292+08:00] start to get devices list
[2025-09-01T17:11:55.292+08:00] use existing adb client
[2025-09-01T17:11:55.292+08:00] success to get adb client, start to request devices list
[2025-09-01T17:11:55.295+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:11:58.305+08:00] start to get devices list
[2025-09-01T17:11:58.306+08:00] use existing adb client
[2025-09-01T17:11:58.306+08:00] success to get adb client, start to request devices list
[2025-09-01T17:11:58.309+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:12:01.313+08:00] start to get devices list
[2025-09-01T17:12:01.313+08:00] use existing adb client
[2025-09-01T17:12:01.313+08:00] success to get adb client, start to request devices list
[2025-09-01T17:12:01.317+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:12:04.325+08:00] start to get devices list
[2025-09-01T17:12:04.325+08:00] use existing adb client
[2025-09-01T17:12:04.325+08:00] success to get adb client, start to request devices list
[2025-09-01T17:12:04.330+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:12:07.334+08:00] start to get devices list
[2025-09-01T17:12:07.334+08:00] use existing adb client
[2025-09-01T17:12:07.334+08:00] success to get adb client, start to request devices list
[2025-09-01T17:12:07.337+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:12:10.336+08:00] start to get devices list
[2025-09-01T17:12:10.336+08:00] use existing adb client
[2025-09-01T17:12:10.336+08:00] success to get adb client, start to request devices list
[2025-09-01T17:12:10.340+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:12:13.341+08:00] start to get devices list
[2025-09-01T17:12:13.341+08:00] use existing adb client
[2025-09-01T17:12:13.341+08:00] success to get adb client, start to request devices list
[2025-09-01T17:12:13.345+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:12:16.350+08:00] start to get devices list
[2025-09-01T17:12:16.350+08:00] use existing adb client
[2025-09-01T17:12:16.350+08:00] success to get adb client, start to request devices list
[2025-09-01T17:12:16.353+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:12:19.350+08:00] start to get devices list
[2025-09-01T17:12:19.351+08:00] use existing adb client
[2025-09-01T17:12:19.351+08:00] success to get adb client, start to request devices list
[2025-09-01T17:12:19.352+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:12:22.361+08:00] start to get devices list
[2025-09-01T17:12:22.361+08:00] use existing adb client
[2025-09-01T17:12:22.361+08:00] success to get adb client, start to request devices list
[2025-09-01T17:12:22.364+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:12:25.363+08:00] start to get devices list
[2025-09-01T17:12:25.363+08:00] use existing adb client
[2025-09-01T17:12:25.363+08:00] success to get adb client, start to request devices list
[2025-09-01T17:12:25.373+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:12:28.373+08:00] start to get devices list
[2025-09-01T17:12:28.373+08:00] use existing adb client
[2025-09-01T17:12:28.373+08:00] success to get adb client, start to request devices list
[2025-09-01T17:12:28.375+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:12:31.375+08:00] start to get devices list
[2025-09-01T17:12:31.375+08:00] use existing adb client
[2025-09-01T17:12:31.375+08:00] success to get adb client, start to request devices list
[2025-09-01T17:12:31.378+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:12:34.379+08:00] start to get devices list
[2025-09-01T17:12:34.379+08:00] use existing adb client
[2025-09-01T17:12:34.379+08:00] success to get adb client, start to request devices list
[2025-09-01T17:12:34.381+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:12:37.388+08:00] start to get devices list
[2025-09-01T17:12:37.389+08:00] use existing adb client
[2025-09-01T17:12:37.389+08:00] success to get adb client, start to request devices list
[2025-09-01T17:12:37.393+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:12:40.400+08:00] start to get devices list
[2025-09-01T17:12:40.401+08:00] use existing adb client
[2025-09-01T17:12:40.401+08:00] success to get adb client, start to request devices list
[2025-09-01T17:12:40.404+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:12:43.414+08:00] start to get devices list
[2025-09-01T17:12:43.415+08:00] use existing adb client
[2025-09-01T17:12:43.416+08:00] success to get adb client, start to request devices list
[2025-09-01T17:12:43.423+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:12:46.417+08:00] start to get devices list
[2025-09-01T17:12:46.417+08:00] use existing adb client
[2025-09-01T17:12:46.417+08:00] success to get adb client, start to request devices list
[2025-09-01T17:12:46.420+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:12:49.420+08:00] start to get devices list
[2025-09-01T17:12:49.420+08:00] use existing adb client
[2025-09-01T17:12:49.420+08:00] success to get adb client, start to request devices list
[2025-09-01T17:12:49.423+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:12:52.430+08:00] start to get devices list
[2025-09-01T17:12:52.430+08:00] use existing adb client
[2025-09-01T17:12:52.430+08:00] success to get adb client, start to request devices list
[2025-09-01T17:12:52.433+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:12:55.437+08:00] start to get devices list
[2025-09-01T17:12:55.438+08:00] use existing adb client
[2025-09-01T17:12:55.438+08:00] success to get adb client, start to request devices list
[2025-09-01T17:12:55.440+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:12:58.440+08:00] start to get devices list
[2025-09-01T17:12:58.440+08:00] use existing adb client
[2025-09-01T17:12:58.440+08:00] success to get adb client, start to request devices list
[2025-09-01T17:12:58.442+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
[2025-09-01T17:13:01.449+08:00] start to get devices list
[2025-09-01T17:13:01.449+08:00] use existing adb client
[2025-09-01T17:13:01.449+08:00] success to get adb client, start to request devices list
[2025-09-01T17:13:01.453+08:00] original devices list: [
  {
    serial: 'ASALE3741B000022',
    authenticating: false,
    product: 'CM5-OP',
    model: 'TECNO_CM5',
    device: 'TECNO-CM5',
    transportId: 9n
  }
]
