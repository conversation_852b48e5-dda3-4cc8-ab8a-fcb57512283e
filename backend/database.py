"""
数据库管理模块
实现MySQL数据库设计、数据清洗和存储功能
"""
import json
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from sqlalchemy import create_engine, Column, Integer, String, Text, Boolean, Float, DateTime, JSON, ForeignKey
from sqlalchemy.orm import declarative_base, sessionmaker, Session, relationship
from sqlalchemy.exc import SQLAlchemyError
from loguru import logger

from backend.config import config
from backend.models.app_model import AppInfo, AnalysisSession
from backend.models.page_model import UIPage, PageNavigation
from backend.models.element_model import UIElement

Base = declarative_base()


class AppTable(Base):
    """应用信息表"""
    __tablename__ = 'app_info'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    package_name = Column(String(255), unique=True, nullable=False, index=True)
    app_name = Column(String(255), nullable=True)
    version_name = Column(String(100), nullable=True)
    version_code = Column(String(50), nullable=True)
    main_activity = Column(String(255), nullable=True)
    target_sdk = Column(Integer, nullable=True)
    min_sdk = Column(Integer, nullable=True)
    install_time = Column(DateTime, nullable=True)
    last_update_time = Column(DateTime, nullable=True)
    app_size = Column(Integer, nullable=True)
    permissions = Column(JSON, nullable=True)
    total_analysis_count = Column(Integer, default=0)
    last_analysis_time = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 关系
    sessions = relationship("AnalysisSessionTable", back_populates="app")
    pages = relationship("PageTable", back_populates="app")


class AnalysisSessionTable(Base):
    """分析会话表"""
    __tablename__ = 'analysis_sessions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(100), unique=True, nullable=False, index=True)
    app_id = Column(Integer, ForeignKey('app_info.id'), nullable=False)
    device_id = Column(String(100), nullable=True)
    max_depth = Column(Integer, default=20)
    max_pages = Column(Integer, default=100)
    enable_ai_enhancement = Column(Boolean, default=True)
    enable_screenshots = Column(Boolean, default=True)
    status = Column(String(50), nullable=False)
    current_page_id = Column(String(100), nullable=True)
    pages_discovered = Column(JSON, nullable=True)
    pages_analyzed = Column(JSON, nullable=True)
    pages_failed = Column(JSON, nullable=True)
    total_elements_found = Column(Integer, default=0)
    total_actionable_elements = Column(Integer, default=0)
    total_navigations = Column(Integer, default=0)
    ai_enhanced_elements = Column(Integer, default=0)
    start_time = Column(DateTime, nullable=True)
    end_time = Column(DateTime, nullable=True)
    error_message = Column(Text, nullable=True)
    error_details = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    
    # 关系
    app = relationship("AppTable", back_populates="sessions")
    pages = relationship("PageTable", back_populates="session")


class PageTable(Base):
    """页面信息表"""
    __tablename__ = 'app_pages'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    page_id = Column(String(100), nullable=False, index=True)
    session_id = Column(Integer, ForeignKey('analysis_sessions.id'), nullable=False)
    app_id = Column(Integer, ForeignKey('app_info.id'), nullable=False)
    page_name = Column(String(255), nullable=True)
    activity_name = Column(String(255), nullable=True)
    package_name = Column(String(255), nullable=True)
    screenshot_path = Column(Text, nullable=True)
    xml_source_path = Column(Text, nullable=True)
    content_hash = Column(String(64), nullable=True, index=True)
    element_count = Column(Integer, default=0)
    actionable_element_count = Column(Integer, default=0)
    analysis_status = Column(String(50), default='pending')
    analysis_error = Column(Text, nullable=True)
    parent_pages = Column(JSON, nullable=True)
    child_pages = Column(JSON, nullable=True)
    visit_count = Column(Integer, default=0)
    last_visited_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 关系
    app = relationship("AppTable", back_populates="pages")
    session = relationship("AnalysisSessionTable", back_populates="pages")
    elements = relationship("ElementTable", back_populates="page")
    source_navigations = relationship("NavigationTable", foreign_keys="NavigationTable.source_page_id", back_populates="source_page")
    target_navigations = relationship("NavigationTable", foreign_keys="NavigationTable.target_page_id", back_populates="target_page")


class ElementTable(Base):
    """页面元素表"""
    __tablename__ = 'page_elements'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    page_id = Column(Integer, ForeignKey('app_pages.id'), nullable=False)
    element_id = Column(String(100), nullable=False, index=True)
    element_type = Column(String(100), nullable=True)
    text = Column(Text, nullable=True)
    resource_id = Column(String(255), nullable=True, index=True)
    content_desc = Column(Text, nullable=True)
    bounds = Column(JSON, nullable=True)
    position = Column(JSON, nullable=True)
    attributes = Column(JSON, nullable=True)
    hierarchy_path = Column(Text, nullable=True)
    xpath = Column(Text, nullable=True)
    package_name = Column(String(255), nullable=True)
    index = Column(Integer, default=0)
    semantic_description = Column(Text, nullable=True)
    expected_action = Column(Text, nullable=True)
    element_category = Column(String(100), nullable=True)
    confidence_score = Column(Float, default=0.0)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 关系
    page = relationship("PageTable", back_populates="elements")
    navigations = relationship("NavigationTable", back_populates="via_element")


class NavigationTable(Base):
    """页面导航关系表"""
    __tablename__ = 'page_navigations'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    source_page_id = Column(Integer, ForeignKey('app_pages.id'), nullable=False)
    target_page_id = Column(Integer, ForeignKey('app_pages.id'), nullable=False)
    via_element_id = Column(Integer, ForeignKey('page_elements.id'), nullable=False)
    action_type = Column(String(50), default='click')
    success_rate = Column(Float, default=1.0)
    avg_load_time = Column(Float, default=0.0)
    created_at = Column(DateTime, default=datetime.now)
    
    # 关系
    source_page = relationship("PageTable", foreign_keys=[source_page_id], back_populates="source_navigations")
    target_page = relationship("PageTable", foreign_keys=[target_page_id], back_populates="target_navigations")
    via_element = relationship("ElementTable", back_populates="navigations")


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        """初始化数据库管理器"""
        self.db_config = config.database
        self.engine = None
        self.SessionLocal = None
        
    def _create_database_if_not_exists(self) -> bool:
        """
        如果数据库不存在则创建数据库

        Returns:
            bool: 创建是否成功
        """
        try:
            # 构建不包含数据库名的连接URL
            base_url = f"mysql+pymysql://{self.db_config.username}:{self.db_config.password}@{self.db_config.host}:{self.db_config.port}/?charset={self.db_config.charset}"
            logger.info(f"基础连接URL: {base_url}")
            # 创建临时引擎连接到MySQL服务器
            temp_engine = create_engine(base_url)

            with temp_engine.connect() as conn:
                # 检查数据库是否存在
                from sqlalchemy import text
                result = conn.execute(text(f"SHOW DATABASES LIKE '{self.db_config.database}'"))
                if result.fetchone() is None:
                    # 数据库不存在，创建它
                    logger.info(f"数据库 '{self.db_config.database}' 不存在，正在创建...")
                    conn.execute(text(f"CREATE DATABASE {self.db_config.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"))
                    conn.commit()
                    logger.info(f"数据库 '{self.db_config.database}' 创建成功")
                else:
                    logger.info(f"数据库 '{self.db_config.database}' 已存在")

            temp_engine.dispose()
            return True

        except Exception as e:
            logger.error(f"创建数据库失败: {e}")
            return False

    def initialize_database(self) -> bool:
        """
        初始化数据库连接

        Returns:
            bool: 初始化是否成功
        """
        try:
            logger.info("初始化数据库连接...")

            # 首先尝试创建数据库（如果不存在）
            if not self._create_database_if_not_exists():
                logger.warning("无法创建数据库，尝试直接连接...")

            # 创建数据库引擎，添加更多连接选项
            connection_args = {
                "charset": "utf8mb4",
                "autocommit": True,
                "connect_timeout": 60,
                "read_timeout": 60,
                "write_timeout": 60
            }

            self.engine = create_engine(
                self.db_config.connection_url,
                echo=False,  # 设置为True可以看到SQL语句
                pool_pre_ping=True,
                pool_recycle=3600,
                pool_timeout=20,
                max_overflow=0,
                connect_args=connection_args
            )

            # 测试数据库连接
            with self.engine.connect() as conn:
                from sqlalchemy import text
                conn.execute(text("SELECT 1"))
                logger.info("数据库连接测试成功")

            # 创建会话工厂
            self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)

            # 创建所有表
            Base.metadata.create_all(bind=self.engine)
            logger.info("数据库表创建完成")

            logger.info("数据库初始化成功")
            return True

        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            self._provide_database_setup_help()
            return False

    def _provide_database_setup_help(self):
        """提供数据库设置帮助信息"""
        logger.error("请检查以下配置:")
        logger.error(f"  - 数据库主机: {self.db_config.host}:{self.db_config.port}")
        logger.error(f"  - 数据库名称: {self.db_config.database}")
        logger.error(f"  - 用户名: {self.db_config.username}")
        logger.error("  - 确保MySQL服务正在运行")
        logger.error("  - 确保已安装cryptography包: pip install cryptography")
        logger.error("")
        logger.error("🔧 快速修复步骤:")
        logger.error("1. 手动创建数据库:")
        logger.error("   mysql -u root -p")
        logger.error(f"   CREATE DATABASE {self.db_config.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;")
        logger.error("")
        logger.error("2. 或者运行数据库检查脚本:")
        logger.error("   python temp/check_database_setup.py")
    
    def get_session(self) -> Session:
        """
        获取数据库会话
        
        Returns:
            Session: 数据库会话
        """
        if not self.SessionLocal:
            raise Exception("数据库未初始化")
        return self.SessionLocal()
    
    def save_app_info(self, app_info: AppInfo) -> Optional[int]:
        """
        保存应用信息
        
        Args:
            app_info: 应用信息对象
            
        Returns:
            Optional[int]: 应用ID，失败时返回None
        """
        try:
            with self.get_session() as session:
                # 检查应用是否已存在
                existing_app = session.query(AppTable).filter_by(package_name=app_info.package_name).first()
                
                if existing_app:
                    # 更新现有应用信息
                    existing_app.app_name = app_info.app_name
                    existing_app.version_name = app_info.version_name
                    existing_app.version_code = app_info.version_code
                    existing_app.main_activity = app_info.main_activity
                    existing_app.target_sdk = app_info.target_sdk
                    existing_app.min_sdk = app_info.min_sdk
                    existing_app.install_time = app_info.install_time
                    existing_app.last_update_time = app_info.last_update_time
                    existing_app.app_size = app_info.app_size
                    existing_app.permissions = app_info.permissions
                    existing_app.total_analysis_count = app_info.total_analysis_count
                    existing_app.last_analysis_time = app_info.last_analysis_time
                    existing_app.updated_at = datetime.now()
                    
                    session.commit()
                    logger.info(f"更新应用信息: {app_info.package_name}")
                    return existing_app.id
                else:
                    # 创建新应用记录
                    app_table = AppTable(
                        package_name=app_info.package_name,
                        app_name=app_info.app_name,
                        version_name=app_info.version_name,
                        version_code=app_info.version_code,
                        main_activity=app_info.main_activity,
                        target_sdk=app_info.target_sdk,
                        min_sdk=app_info.min_sdk,
                        install_time=app_info.install_time,
                        last_update_time=app_info.last_update_time,
                        app_size=app_info.app_size,
                        permissions=app_info.permissions,
                        total_analysis_count=app_info.total_analysis_count,
                        last_analysis_time=app_info.last_analysis_time
                    )
                    
                    session.add(app_table)
                    session.commit()
                    session.refresh(app_table)
                    
                    logger.info(f"保存新应用信息: {app_info.package_name}")
                    return app_table.id
                    
        except SQLAlchemyError as e:
            logger.error(f"保存应用信息失败: {e}")
            return None
    
    def save_analysis_session(self, analysis_session: AnalysisSession, app_id: int) -> Optional[int]:
        """
        保存分析会话
        
        Args:
            analysis_session: 分析会话对象
            app_id: 应用ID
            
        Returns:
            Optional[int]: 会话ID，失败时返回None
        """
        try:
            with self.get_session() as session:
                session_table = AnalysisSessionTable(
                    session_id=analysis_session.session_id,
                    app_id=app_id,
                    device_id=analysis_session.device_id,
                    max_depth=analysis_session.max_depth,
                    max_pages=analysis_session.max_pages,
                    enable_ai_enhancement=analysis_session.enable_ai_enhancement,
                    enable_screenshots=analysis_session.enable_screenshots,
                    status=analysis_session.status.value,
                    current_page_id=analysis_session.current_page_id,
                    pages_discovered=list(analysis_session.pages_discovered),
                    pages_analyzed=list(analysis_session.pages_analyzed),
                    pages_failed=list(analysis_session.pages_failed),
                    total_elements_found=analysis_session.total_elements_found,
                    total_actionable_elements=analysis_session.total_actionable_elements,
                    total_navigations=analysis_session.total_navigations,
                    ai_enhanced_elements=analysis_session.ai_enhanced_elements,
                    start_time=analysis_session.start_time,
                    end_time=analysis_session.end_time,
                    error_message=analysis_session.error_message,
                    error_details=analysis_session.error_details
                )
                
                session.add(session_table)
                session.commit()
                session.refresh(session_table)
                
                logger.info(f"保存分析会话: {analysis_session.session_id}")
                return session_table.id
                
        except SQLAlchemyError as e:
            logger.error(f"保存分析会话失败: {e}")
            return None

    def save_page(self, page: UIPage, session_id: int, app_id: int) -> Optional[int]:
        """
        保存页面信息

        Args:
            page: 页面对象
            session_id: 会话ID
            app_id: 应用ID

        Returns:
            Optional[int]: 页面数据库ID，失败时返回None
        """
        try:
            with self.get_session() as session:
                page_table = PageTable(
                    page_id=page.page_id,
                    session_id=session_id,
                    app_id=app_id,
                    page_name=page.page_name,
                    activity_name=page.activity_name,
                    package_name=page.package_name,
                    screenshot_path=page.screenshot_path,
                    xml_source_path=page.xml_source_path,
                    content_hash=page.content_hash,
                    element_count=page.element_count,
                    actionable_element_count=page.actionable_element_count,
                    analysis_status=page.analysis_status,
                    analysis_error=page.analysis_error,
                    parent_pages=list(page.parent_pages),
                    child_pages=list(page.child_pages),
                    visit_count=page.visit_count,
                    last_visited_at=page.last_visited_at
                )

                session.add(page_table)
                session.commit()
                session.refresh(page_table)

                logger.debug(f"保存页面: {page.page_name} (ID: {page.page_id})")
                return page_table.id

        except SQLAlchemyError as e:
            logger.error(f"保存页面失败: {e}")
            return None

    def save_elements(self, elements: List[UIElement], page_db_id: int) -> int:
        """
        批量保存页面元素

        Args:
            elements: 元素列表
            page_db_id: 页面数据库ID

        Returns:
            int: 成功保存的元素数量
        """
        try:
            saved_count = 0

            with self.get_session() as session:
                for element in elements:
                    try:
                        # 清洗元素数据
                        cleaned_element = self._clean_element_data(element)

                        element_table = ElementTable(
                            page_id=page_db_id,
                            element_id=cleaned_element.element_id,
                            element_type=cleaned_element.element_type,
                            text=cleaned_element.text,
                            resource_id=cleaned_element.resource_id,
                            content_desc=cleaned_element.content_desc,
                            bounds=self._position_to_dict(cleaned_element.position),
                            position=self._position_to_dict(cleaned_element.position),
                            attributes=cleaned_element.attributes.to_dict() if cleaned_element.attributes else None,
                            hierarchy_path=cleaned_element.hierarchy_path,
                            xpath=cleaned_element.xpath,
                            package_name=cleaned_element.package_name,
                            index=cleaned_element.index,
                            semantic_description=cleaned_element.semantic_description,
                            expected_action=cleaned_element.expected_action,
                            element_category=cleaned_element.element_category,
                            confidence_score=cleaned_element.confidence_score
                        )

                        session.add(element_table)
                        saved_count += 1

                    except Exception as e:
                        logger.warning(f"保存元素失败: {e}")
                        continue

                session.commit()
                logger.info(f"批量保存元素完成: {saved_count}/{len(elements)}")
                return saved_count

        except SQLAlchemyError as e:
            logger.error(f"批量保存元素失败: {e}")
            return 0

    def save_navigations(self, navigations: List[PageNavigation],
                        page_id_mapping: Dict[str, int],
                        element_id_mapping: Dict[str, int]) -> int:
        """
        批量保存页面导航关系

        Args:
            navigations: 导航关系列表
            page_id_mapping: 页面ID映射 {page_id: db_id}
            element_id_mapping: 元素ID映射 {element_id: db_id}

        Returns:
            int: 成功保存的导航关系数量
        """
        try:
            saved_count = 0

            with self.get_session() as session:
                for navigation in navigations:
                    try:
                        source_db_id = page_id_mapping.get(navigation.source_page_id)
                        target_db_id = page_id_mapping.get(navigation.target_page_id)
                        element_db_id = element_id_mapping.get(navigation.via_element_id)

                        if not all([source_db_id, target_db_id, element_db_id]):
                            logger.warning(f"导航关系映射不完整，跳过: {navigation.source_page_id} -> {navigation.target_page_id}")
                            continue

                        navigation_table = NavigationTable(
                            source_page_id=source_db_id,
                            target_page_id=target_db_id,
                            via_element_id=element_db_id,
                            action_type=navigation.action_type,
                            success_rate=navigation.success_rate,
                            avg_load_time=navigation.avg_load_time
                        )

                        session.add(navigation_table)
                        saved_count += 1

                    except Exception as e:
                        logger.warning(f"保存导航关系失败: {e}")
                        continue

                session.commit()
                logger.info(f"批量保存导航关系完成: {saved_count}/{len(navigations)}")
                return saved_count

        except SQLAlchemyError as e:
            logger.error(f"批量保存导航关系失败: {e}")
            return 0

    def save_complete_analysis_session(self, analysis_session: AnalysisSession) -> bool:
        """
        保存完整的分析会话（包括所有页面、元素和导航关系）

        Args:
            analysis_session: 分析会话对象

        Returns:
            bool: 保存是否成功
        """
        try:
            logger.info(f"开始保存完整分析会话: {analysis_session.session_id}")

            # 1. 保存应用信息
            app_id = self.save_app_info(analysis_session.app_info)
            if not app_id:
                logger.error("保存应用信息失败")
                return False

            # 2. 保存分析会话
            session_db_id = self.save_analysis_session(analysis_session, app_id)
            if not session_db_id:
                logger.error("保存分析会话失败")
                return False

            # 3. 保存页面和元素
            page_id_mapping = {}  # {page_id: db_id}
            element_id_mapping = {}  # {element_id: db_id}

            for page in analysis_session.pages.values():
                # 保存页面
                page_db_id = self.save_page(page, session_db_id, app_id)
                if page_db_id:
                    page_id_mapping[page.page_id] = page_db_id

                    # 保存页面元素
                    if page.elements:
                        saved_elements = self.save_elements(page.elements, page_db_id)
                        logger.debug(f"页面 {page.page_name} 保存了 {saved_elements} 个元素")

            # 构建元素ID映射（在所有页面保存完成后统一处理）
            with self.get_session() as session:
                for page_id, page_db_id in page_id_mapping.items():
                    db_elements = session.query(ElementTable).filter_by(page_id=page_db_id).all()
                    for db_element in db_elements:
                        element_id_mapping[db_element.element_id] = db_element.id

            # 4. 保存导航关系
            if analysis_session.pages:
                all_navigations = []
                for page in analysis_session.pages.values():
                    all_navigations.extend(page.navigations)

                if all_navigations:
                    saved_navigations = self.save_navigations(all_navigations, page_id_mapping, element_id_mapping)
                    logger.info(f"保存了 {saved_navigations} 个导航关系")

            logger.info(f"完整分析会话保存成功: {analysis_session.session_id}")
            return True

        except Exception as e:
            logger.error(f"保存完整分析会话失败: {e}")
            return False

    def _clean_element_data(self, element: UIElement) -> UIElement:
        """
        清洗元素数据

        Args:
            element: 原始元素

        Returns:
            UIElement: 清洗后的元素
        """
        try:
            # 创建元素副本
            cleaned = UIElement(
                element_id=element.element_id,
                element_type=element.element_type,
                text=self._clean_text(element.text),
                resource_id=element.resource_id,
                content_desc=self._clean_text(element.content_desc),
                position=element.position,
                attributes=element.attributes,
                hierarchy_path=element.hierarchy_path,
                xpath=element.xpath,
                package_name=element.package_name,
                index=element.index,
                semantic_description=self._clean_text(element.semantic_description),
                expected_action=self._clean_text(element.expected_action),
                element_category=element.element_category,
                confidence_score=element.confidence_score,
                created_at=element.created_at,
                updated_at=element.updated_at
            )

            return cleaned

        except Exception as e:
            logger.warning(f"清洗元素数据失败: {e}")
            return element

    def _clean_text(self, text: str) -> str:
        """
        清洗文本内容

        Args:
            text: 原始文本

        Returns:
            str: 清洗后的文本
        """
        if not text:
            return ""

        # 去除首尾空白
        cleaned = text.strip()

        # 限制长度
        max_length = 1000
        if len(cleaned) > max_length:
            cleaned = cleaned[:max_length] + "..."

        return cleaned

    def _position_to_dict(self, position) -> Optional[Dict[str, int]]:
        """
        将位置对象转换为字典

        Args:
            position: 位置对象

        Returns:
            Optional[Dict[str, int]]: 位置字典
        """
        if not position:
            return None

        try:
            return {
                'left': position.left,
                'top': position.top,
                'right': position.right,
                'bottom': position.bottom,
                'center_x': position.center_x,
                'center_y': position.center_y,
                'width': position.width,
                'height': position.height
            }
        except Exception:
            return None


# 全局数据库管理器实例
db_manager = DatabaseManager()

if __name__ == '__main__':
    db_manager.initialize_database()
