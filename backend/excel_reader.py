"""
Excel数据读取工具
用于读取测试用例和模块映射关系
"""
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Optional
import logging
import re
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class TestCase:
    """测试用例数据模型"""
    # 必填字段
    tcid: str                    # *TCID
    case_name: str              # *CaseName
    group_name: str             # *Group
    sub_group: str              # *SubGroup
    component: str              # *Component
    case_type: str              # *Type
    level: str                  # *Level
    steps: str                  # *Steps
    expect_result: str          # *ExpectResult
    automated: str              # *Automated
    owner: str                  # *Owner
    execute_owner: str          # *ExecuteOwner

    # 可选字段
    sort: int = 0               # Sort
    sub_component: str = ""     # SubComponent
    sub_function: str = ""      # SubFunction
    second_function: str = ""   # SecondFunction
    case_set_type: str = ""     # CaseSetType
    purpose: str = ""           # Purpose
    pre_condition: str = ""     # PreCondition
    keywords: str = ""          # Keywords
    phase: str = ""             # Phase
    test_area: str = ""         # TestArea
    execute_time: str = ""      # ExcuteTime
    standard: str = ""          # Standard
    simples: str = ""           # Simples
    environment: str = ""       # Environment
    os: str = ""                # OS
    android: str = ""           # Android
    cpu: str = ""               # CPU
    interaction_component: str = ""  # InteractionComponent
    brand: str = ""             # Brand
    sim: str = ""               # SIM
    source: str = ""            # Source
    match_project: str = ""     # MatchProject
    test_code_path: str = ""    # TestCodePath
    app_version: str = ""       # Appversion
    special_ability: str = ""   # SpecialAbility
    special_type: str = ""      # SpecialType
    update_time: str = ""       # 更新时间
    audit_result: str = ""      # AuditResult
    audit_remark: str = ""      # AuditRemark

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'tcid': self.tcid,
            'case_name': self.case_name,
            'group_name': self.group_name,
            'sub_group': self.sub_group,
            'component': self.component,
            'case_type': self.case_type,
            'level': self.level,
            'steps': self.steps,
            'expect_result': self.expect_result,
            'automated': self.automated,
            'owner': self.owner,
            'execute_owner': self.execute_owner,
            'sort': self.sort,
            'sub_component': self.sub_component,
            'sub_function': self.sub_function,
            'second_function': self.second_function,
            'case_set_type': self.case_set_type,
            'purpose': self.purpose,
            'pre_condition': self.pre_condition,
            'keywords': self.keywords,
            'phase': self.phase,
            'test_area': self.test_area,
            'execute_time': self.execute_time,
            'standard': self.standard,
            'simples': self.simples,
            'environment': self.environment,
            'os': self.os,
            'android': self.android,
            'cpu': self.cpu,
            'interaction_component': self.interaction_component,
            'brand': self.brand,
            'sim': self.sim,
            'source': self.source,
            'match_project': self.match_project,
            'test_code_path': self.test_code_path,
            'app_version': self.app_version,
            'special_ability': self.special_ability,
            'special_type': self.special_type,
            'update_time': self.update_time,
            'audit_result': self.audit_result,
            'audit_remark': self.audit_remark
        }


@dataclass
class PackageComponent:
    """包组件映射数据模型"""
    business_module: str  # 业务模块名称
    package_name: str     # 包名
    component_name: str   # 组件名称
    description: str = "" # 描述
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'business_module': self.business_module,
            'package_name': self.package_name,
            'component_name': self.component_name,
            'description': self.description
        }


class ExcelReader:
    """Excel文件读取器"""
    
    def __init__(self, data_dir: str = None):
        """
        初始化Excel读取器

        Args:
            data_dir: 数据目录路径，如果为None则自动检测
        """
        if data_dir is None:
            # 自动检测数据目录路径
            current_file = Path(__file__)
            project_root = current_file.parent.parent  # 从 backend/excel_reader.py 回到项目根目录
            self.data_dir = project_root / "backend" / "data"
        else:
            self.data_dir = Path(data_dir)

        self.case_file = self.data_dir / "case" / "case.xlsx"
        self.package_file = self.data_dir / "business_knowledge" / "package_component.xlsx"

    def _safe_str(self, value, default: str = "") -> str:
        """安全转换为字符串，处理NaN和None值"""
        if pd.isna(value) or value is None:
            return default
        str_value = str(value).strip()
        return str_value if str_value else default

    def _safe_int(self, value, default: int = 0) -> int:
        """安全转换为整数，处理NaN和None值"""
        if pd.isna(value) or value is None:
            return default
        try:
            return int(float(value))  # 先转float再转int，处理Excel中的数字格式
        except (ValueError, TypeError):
            return default

    def _extract_package_name(self, raw_value) -> str:
        """
        从原始值中提取纯英文包名
        处理包含中文描述的混合格式

        Args:
            raw_value: 原始值，可能包含中文描述

        Returns:
            str: 提取的纯英文包名
        """
        if pd.isna(raw_value) or not str(raw_value).strip():
            return ""

        text = str(raw_value).strip()

        # 跳过明显的无效值
        if text in ['/', '-', 'N/A', '无', '暂无', '待定']:
            return ""

        # 正则表达式匹配包名模式（按优先级排序）
        patterns = [
            # 括号内的包名：（com.xxx.xxx）或(com.xxx.xxx)
            r'[（(]([a-zA-Z][a-zA-Z0-9._]+)[）)]',
            # 标准包名格式：com.xxx.xxx
            r'(com\.[a-zA-Z0-9._]+)',
            # Android系统包名
            r'(android\.[a-zA-Z0-9._]+)',
            # 其他标准包名格式
            r'([a-zA-Z][a-zA-Z0-9]*\.[a-zA-Z0-9._]+)',
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                # 返回最长的匹配（通常是最完整的包名）
                package_name = max(matches, key=len)
                # 验证包名格式
                if re.match(r'^[a-zA-Z][a-zA-Z0-9._]*$', package_name):
                    return package_name

        # 如果没有匹配到，检查整个字符串是否是纯英文包名
        if re.match(r'^[a-zA-Z][a-zA-Z0-9._-]+$', text):
            return text

        return ""

    def _extract_english_from_description(self, description) -> str:
        """
        从描述字段中提取英文作为组件名

        Args:
            description: 描述字段，可能包含中英文混合

        Returns:
            str: 提取的英文组件名
        """
        if pd.isna(description) or not str(description).strip():
            return "未知组件"

        text = str(description).strip()

        # 跳过明显的无效值
        if text in ['/', '-', 'N/A', '无', '暂无', '待定']:
            return "未知组件"

        # 提取英文的正则表达式模式（按优先级排序）
        patterns = [
            # 括号内的英文：（English）或(English)
            r'[（(]([a-zA-Z][a-zA-Z0-9\s_-]*)[）)]',
            # 英文单词开头的部分（处理混合格式如 "Launcher启动器"）
            r'^([a-zA-Z][a-zA-Z0-9_-]*)',
            # PascalCase 英文单词
            r'\b([A-Z][a-zA-Z0-9]*(?:[A-Z][a-zA-Z0-9]*)*)\b',
            # 一般英文单词
            r'\b([a-zA-Z][a-zA-Z0-9_-]{2,})\b',
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text)
            if matches:
                # 返回最长的匹配
                english_name = max(matches, key=len).strip()
                # 验证是否为有效的英文组件名
                if re.match(r'^[a-zA-Z][a-zA-Z0-9_-]*$', english_name) and len(english_name) >= 2:
                    return english_name

        # 如果整个字符串都是英文，直接返回（移除空格）
        if re.match(r'^[a-zA-Z][a-zA-Z0-9\s_-]+$', text):
            # 对于连字符格式，取第一个部分
            if '-' in text:
                return text.split('-')[0].strip()
            return re.sub(r'\s+', '', text)

        return "未知组件"

    def _extract_component_name(self, component_value, description_value) -> str:
        """
        提取组件名，优先使用 component_value，
        如果为空则从 description_value 中提取英文

        Args:
            component_value: 原始组件名字段值
            description_value: 描述字段值

        Returns:
            str: 最终的组件名
        """
        # 首先尝试使用原始的 component_value
        if not pd.isna(component_value) and str(component_value).strip():
            component = str(component_value).strip()
            if component not in ['', '未知组件', 'N/A', '/', '-']:
                return component

        # 如果 component_value 无效，从 description 中提取英文
        return self._extract_english_from_description(description_value)

    def read_test_cases(self) -> List[TestCase]:
        """
        读取测试用例数据
        
        Returns:
            List[TestCase]: 测试用例列表
        """
        try:
            if not self.case_file.exists():
                logger.error(f"测试用例文件不存在: {self.case_file}")
                return []
            
            logger.info(f"读取测试用例文件: {self.case_file}")
            
            # 读取Excel文件
            df = pd.read_excel(self.case_file, engine='openpyxl')
            
            # 打印列名以便调试
            logger.info(f"Excel列名: {df.columns.tolist()}")
            
            test_cases = []
            auto_tcid_counter = 1  # 自动生成TCID的计数器

            for index, row in df.iterrows():
                try:
                    # 处理TCID字段，确保有有效值
                    tcid_value = row.get('*TCID')
                    if pd.isna(tcid_value) or not str(tcid_value).strip():
                        tcid_value = f'TC_{auto_tcid_counter:03d}'
                        auto_tcid_counter += 1

                    # 根据实际Excel列名映射
                    test_case = TestCase(
                        # 必填字段
                        tcid=str(tcid_value).strip(),
                        case_name=str(row.get('*CaseName', '未命名用例')).strip(),
                        group_name=str(row.get('*Group', '未分组')).strip(),
                        sub_group=str(row.get('*SubGroup', '默认子组')).strip(),
                        component=str(row.get('*Component', '未知组件')).strip(),
                        case_type=str(row.get('*Type', '功能测试')).strip(),
                        level=str(row.get('*Level', '中')).strip(),
                        steps=str(row.get('*Steps', '无测试步骤')).strip(),
                        expect_result=str(row.get('*ExpectResult', '无预期结果')).strip(),
                        automated=str(row.get('*Automated', '否')).strip(),
                        owner=str(row.get('*Owner', '未指定')).strip(),
                        execute_owner=str(row.get('*ExecuteOwner', '未指定')).strip(),

                        # 可选字段 - 安全处理空值
                        sort=self._safe_int(row.get('Sort'), 0),
                        sub_component=self._safe_str(row.get('SubComponent')),
                        sub_function=self._safe_str(row.get('SubFunction')),
                        second_function=self._safe_str(row.get('SecondFunction')),
                        case_set_type=self._safe_str(row.get('CaseSetType')),
                        purpose=self._safe_str(row.get('Purpose')),
                        pre_condition=self._safe_str(row.get('PreCondition')),
                        keywords=self._safe_str(row.get('Keywords')),
                        phase=self._safe_str(row.get('Phase')),
                        test_area=self._safe_str(row.get('TestArea')),
                        execute_time=self._safe_str(row.get('ExcuteTime')),
                        standard=self._safe_str(row.get('Standard')),
                        simples=self._safe_str(row.get('Simples')),
                        environment=self._safe_str(row.get('Environment')),
                        os=self._safe_str(row.get('OS')),
                        android=self._safe_str(row.get('Android')),
                        cpu=self._safe_str(row.get('CPU')),
                        interaction_component=self._safe_str(row.get('InteractionComponent')),
                        brand=self._safe_str(row.get('Brand')),
                        sim=self._safe_str(row.get('SIM')),
                        source=self._safe_str(row.get('Source')),
                        match_project=self._safe_str(row.get('MatchProject')),
                        test_code_path=self._safe_str(row.get('TestCodePath')),
                        app_version=self._safe_str(row.get('Appversion')),
                        special_ability=self._safe_str(row.get('SpecialAbility')),
                        special_type=self._safe_str(row.get('SpecialType')),
                        update_time=self._safe_str(row.get('更新时间')),
                        audit_result=self._safe_str(row.get('AuditResult')),
                        audit_remark=self._safe_str(row.get('AuditRemark'))
                    )
                    test_cases.append(test_case)

                except Exception as e:
                    logger.warning(f"解析第{index+1}行用例数据失败: {e}")
                    continue
            
            logger.info(f"成功读取 {len(test_cases)} 个测试用例")
            return test_cases
            
        except Exception as e:
            logger.error(f"读取测试用例文件失败: {e}")
            return []
    
    def read_package_components(self) -> List[PackageComponent]:
        """
        读取包组件映射数据
        
        Returns:
            List[PackageComponent]: 包组件映射列表
        """
        try:
            if not self.package_file.exists():
                logger.error(f"包组件映射文件不存在: {self.package_file}")
                return []
            
            logger.info(f"读取包组件映射文件: {self.package_file}")
            
            # 读取Excel文件
            df = pd.read_excel(self.package_file, engine='openpyxl')
            
            # 打印列名以便调试
            logger.info(f"Excel列名: {df.columns.tolist()}")
            
            components = []
            for index, row in df.iterrows():
                try:
                    # 优化的字段映射：使用 subcomponent 作为业务模块名称
                    business_module = self._safe_str(
                        row.get('subcomponent', row.get('子模块 【英文（中文）】subcomponent')),
                        '未知模块'
                    )

                    # 提取纯英文包名
                    package_name = self._extract_package_name(row.get('包名'))

                    # 优化的组件名提取：优先使用 Component，否则从描述中提取英文
                    component_name = self._extract_component_name(
                        row.get('Component'),
                        row.get('子模块 【英文（中文）】subcomponent')
                    )

                    description = self._safe_str(
                        row.get('子模块 【英文（中文）】subcomponent',
                        row.get('模块【英文（中文）】')),
                        ''
                    )

                    # 跳过空数据行（避免保存无意义的数据）
                    if business_module == '未知模块' and not package_name:
                        continue

                    # 跳过无效包名的行
                    if not package_name:
                        logger.debug(f"第{index+1}行跳过：无效包名 '{row.get('包名')}'")
                        continue

                    component = PackageComponent(
                        business_module=business_module,
                        package_name=package_name,
                        component_name=component_name,
                        description=description
                    )
                    components.append(component)
                    
                except Exception as e:
                    logger.warning(f"解析第{index+1}行组件映射数据失败: {e}")
                    continue
            
            logger.info(f"成功读取 {len(components)} 个包组件映射")
            return components
            
        except Exception as e:
            logger.error(f"读取包组件映射文件失败: {e}")
            return []
    
    def get_components_by_module(self, business_module: str) -> List[PackageComponent]:
        """
        根据业务模块获取相关组件
        
        Args:
            business_module: 业务模块名称
            
        Returns:
            List[PackageComponent]: 相关组件列表
        """
        all_components = self.read_package_components()
        return [comp for comp in all_components if comp.business_module == business_module]
    
    def get_test_cases_by_module(self, business_module: str) -> List[TestCase]:
        """
        根据业务模块获取相关测试用例
        
        Args:
            business_module: 业务模块名称
            
        Returns:
            List[TestCase]: 相关测试用例列表
        """
        all_cases = self.read_test_cases()
        return [case for case in all_cases if case.group_name == business_module]


def main():
    """测试函数"""
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建读取器
    reader = ExcelReader()
    
    # 读取测试用例
    test_cases = reader.read_test_cases()
    print(f"\n=== 测试用例 ({len(test_cases)}个) ===")
    for case in test_cases[:3]:  # 只显示前3个
        print(f"ID: {case.tcid}")
        print(f"模块: {case.group_name}")
        print(f"步骤: {case.steps[:100]}...")
        print("-" * 50)
    
    # 读取包组件映射
    components = reader.read_package_components()
    print(f"\n=== 包组件映射 ({len(components)}个) ===")
    for comp in components[:3]:  # 只显示前3个
        print(f"模块: {comp.business_module}")
        print(f"包名: {comp.package_name}")
        print(f"组件: {comp.component_name}")
        print("-" * 50)


if __name__ == "__main__":
    main()
