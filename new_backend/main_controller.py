"""
多智能体自动化脚本生成系统主控制器
整合所有智能体，提供统一的接口和工作流编排
"""
import os
import sys
import asyncio
import json
from typing import Dict, Any, Optional, List
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加相对路径导入
sys.path.append(str(Path(__file__).parent))

from config import config
from utils.database_manager import DatabaseManager
from utils.uiautomator2_helper import UIAutomator2Helper
from utils.autogen_client import AutoGenClientManager

# 导入所有智能体
from agents.page_element_extraction_agent import PageElementExtractionAgent
from agents.app_traversal_agent import AppTraversalAgent
from agents.case_analysis_agent import CaseAnalysisAgent
from agents.script_generation_agent import ScriptGenerationAgent

# 导入数据模型
from models.page_models import AppInfo
from models.case_models import TestCase
from models.session_models import WorkflowExecution, WorkflowStepExecution


class MainController:
    """主控制器"""
    
    def __init__(self):
        """初始化主控制器"""
        self.config = config
        self.logger = logger
        
        # 核心组件
        self.db_manager = DatabaseManager()
        self.ui_helper = UIAutomator2Helper()
        self.autogen_client = AutoGenClientManager()
        
        # 智能体实例
        self.page_agent = None
        self.traversal_agent = None
        self.case_agent = None
        self.script_agent = None
        
        # 工作流状态
        self.current_workflow = None
        self.workflow_steps = []
        
        # 初始化系统
        self._initialize_system()
    
    def _initialize_system(self):
        """初始化系统"""
        try:
            logger.info("初始化多智能体自动化脚本生成系统...")
            
            # 初始化智能体
            self.page_agent = PageElementExtractionAgent()
            self.traversal_agent = AppTraversalAgent()
            self.case_agent = CaseAnalysisAgent()
            self.script_agent = ScriptGenerationAgent()
            
            logger.info("系统初始化完成")
            
        except Exception as e:
            logger.error(f"系统初始化失败: {e}")
            raise
    
    async def analyze_app(self, package_name: str, app_name: str = None) -> Dict[str, Any]:
        """
        分析应用
        
        Args:
            package_name: 应用包名
            app_name: 应用名称（可选）
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        try:
            logger.info(f"开始分析应用: {package_name}")
            
            # 创建工作流
            workflow = await self._create_workflow("app_analysis", {
                "package_name": package_name,
                "app_name": app_name
            })
            
            # 步骤1: 创建应用信息
            app_info = await self._create_app_info(package_name, app_name)
            await self._record_workflow_step(workflow.id, "create_app_info", "completed", {
                "app_id": app_info.id if app_info else None
            })
            
            if not app_info:
                return {"success": False, "error": "创建应用信息失败"}
            
            # 步骤2: 应用遍历分析
            traversal_result = await self.traversal_agent.start_app_analysis(
                app_info=app_info,
                session_id=workflow.session_id
            )
            
            await self._record_workflow_step(workflow.id, "app_traversal", 
                                           "completed" if traversal_result.get('success') else "failed",
                                           traversal_result)
            
            # 更新工作流状态
            await self._update_workflow_status(workflow.id, 
                                             "completed" if traversal_result.get('success') else "failed")
            
            logger.info(f"应用分析完成: {package_name}")
            return {
                "success": True,
                "workflow_id": workflow.id,
                "app_info": app_info,
                "traversal_result": traversal_result
            }
            
        except Exception as e:
            logger.error(f"分析应用失败: {e}")
            if self.current_workflow:
                await self._update_workflow_status(self.current_workflow.id, "failed")
            return {"success": False, "error": str(e)}
    
    async def process_test_case(self, test_case_data: Dict[str, Any], 
                              app_elements: Optional[List] = None) -> Dict[str, Any]:
        """
        处理测试用例
        
        Args:
            test_case_data: 测试用例数据
            app_elements: 应用元素列表（可选）
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            logger.info(f"开始处理测试用例: {test_case_data.get('case_name', 'Unknown')}")
            
            # 创建工作流
            workflow = await self._create_workflow("test_case_processing", test_case_data)
            
            # 步骤1: 创建测试用例对象
            test_case = TestCase(**test_case_data)
            saved_case = self.db_manager.save_test_case(test_case)
            
            await self._record_workflow_step(workflow.id, "create_test_case", "completed", {
                "case_id": saved_case.id if saved_case else None
            })
            
            if not saved_case:
                return {"success": False, "error": "保存测试用例失败"}
            
            # 步骤2: 用例分析
            analysis_result = await self.case_agent.analyze_test_case(saved_case, app_elements)
            
            await self._record_workflow_step(workflow.id, "case_analysis",
                                           "completed" if analysis_result.get('success') else "failed",
                                           analysis_result)
            
            if not analysis_result.get('success'):
                await self._update_workflow_status(workflow.id, "failed")
                return {"success": False, "error": "用例分析失败", "details": analysis_result}
            
            # 步骤3: 脚本生成
            script_result = await self.script_agent.generate_script(
                saved_case, 
                analysis_result.get('analysis_result', {})
            )
            
            await self._record_workflow_step(workflow.id, "script_generation",
                                           "completed" if script_result.get('success') else "failed",
                                           script_result)
            
            # 更新工作流状态
            final_status = "completed" if script_result.get('success') else "failed"
            await self._update_workflow_status(workflow.id, final_status)
            
            logger.info(f"测试用例处理完成: {test_case_data.get('case_name', 'Unknown')}")
            return {
                "success": True,
                "workflow_id": workflow.id,
                "test_case": saved_case,
                "analysis_result": analysis_result,
                "script_result": script_result
            }
            
        except Exception as e:
            logger.error(f"处理测试用例失败: {e}")
            if self.current_workflow:
                await self._update_workflow_status(self.current_workflow.id, "failed")
            return {"success": False, "error": str(e)}
    
    async def batch_process_cases(self, cases_data: List[Dict[str, Any]], 
                                package_name: str = None) -> Dict[str, Any]:
        """
        批量处理测试用例
        
        Args:
            cases_data: 测试用例数据列表
            package_name: 应用包名（用于获取元素信息）
            
        Returns:
            Dict[str, Any]: 批量处理结果
        """
        try:
            logger.info(f"开始批量处理 {len(cases_data)} 个测试用例")
            
            # 创建批量工作流
            workflow = await self._create_workflow("batch_processing", {
                "total_cases": len(cases_data),
                "package_name": package_name
            })
            
            # 获取应用元素（如果指定了包名）
            app_elements = None
            if package_name:
                app_elements = await self._get_app_elements(package_name)
            
            # 批量处理结果
            results = []
            success_count = 0
            failed_count = 0
            
            for i, case_data in enumerate(cases_data, 1):
                try:
                    logger.info(f"处理第 {i}/{len(cases_data)} 个用例: {case_data.get('case_name', 'Unknown')}")
                    
                    # 处理单个用例
                    result = await self.process_test_case(case_data, app_elements)
                    
                    if result.get('success'):
                        success_count += 1
                    else:
                        failed_count += 1
                    
                    results.append({
                        "index": i,
                        "case_name": case_data.get('case_name', 'Unknown'),
                        "result": result
                    })
                    
                    # 记录进度
                    await self._record_workflow_step(workflow.id, f"process_case_{i}", 
                                                   "completed" if result.get('success') else "failed",
                                                   {"case_name": case_data.get('case_name'), "result": result})
                    
                except Exception as e:
                    logger.error(f"处理第 {i} 个用例失败: {e}")
                    failed_count += 1
                    results.append({
                        "index": i,
                        "case_name": case_data.get('case_name', 'Unknown'),
                        "result": {"success": False, "error": str(e)}
                    })
            
            # 更新工作流状态
            final_status = "completed" if failed_count == 0 else "partial_success"
            await self._update_workflow_status(workflow.id, final_status)
            
            logger.info(f"批量处理完成: 成功 {success_count}, 失败 {failed_count}")
            return {
                "success": True,
                "workflow_id": workflow.id,
                "total_cases": len(cases_data),
                "success_count": success_count,
                "failed_count": failed_count,
                "results": results
            }
            
        except Exception as e:
            logger.error(f"批量处理失败: {e}")
            if self.current_workflow:
                await self._update_workflow_status(self.current_workflow.id, "failed")
            return {"success": False, "error": str(e)}
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            # 检查各组件状态
            status = {
                "system_time": datetime.now().isoformat(),
                "components": {
                    "database": self._check_database_status(),
                    "uiautomator2": self._check_ui_helper_status(),
                    "autogen": self._check_autogen_status(),
                    "agents": self._check_agents_status()
                },
                "statistics": await self._get_system_statistics(),
                "health": "healthy"
            }
            
            # 计算整体健康状态
            component_statuses = [comp.get('status') for comp in status['components'].values()]
            if 'error' in component_statuses:
                status['health'] = 'error'
            elif 'warning' in component_statuses:
                status['health'] = 'warning'
            
            return status
            
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return {
                "system_time": datetime.now().isoformat(),
                "health": "error",
                "error": str(e)
            }
    
    def _check_database_status(self) -> Dict[str, Any]:
        """检查数据库状态"""
        try:
            session = self.db_manager.get_session()
            self.db_manager.close_session(session)
            return {"status": "healthy", "message": "数据库连接正常"}
        except Exception as e:
            return {"status": "error", "message": f"数据库连接失败: {e}"}
    
    def _check_ui_helper_status(self) -> Dict[str, Any]:
        """检查UIAutomator2状态"""
        try:
            if self.ui_helper.device:
                device_info = self.ui_helper.get_device_info()
                return {"status": "healthy", "message": "设备连接正常", "device_info": device_info}
            else:
                return {"status": "warning", "message": "设备未连接"}
        except Exception as e:
            return {"status": "error", "message": f"设备连接检查失败: {e}"}
    
    def _check_autogen_status(self) -> Dict[str, Any]:
        """检查AutoGen状态"""
        try:
            if self.autogen_client.is_available():
                return {"status": "healthy", "message": "AutoGen客户端可用"}
            else:
                return {"status": "warning", "message": "AutoGen客户端不可用，使用降级模式"}
        except Exception as e:
            return {"status": "error", "message": f"AutoGen状态检查失败: {e}"}
    
    def _check_agents_status(self) -> Dict[str, Any]:
        """检查智能体状态"""
        try:
            agents_status = {
                "page_agent": self.page_agent is not None,
                "traversal_agent": self.traversal_agent is not None,
                "case_agent": self.case_agent is not None,
                "script_agent": self.script_agent is not None
            }
            
            all_healthy = all(agents_status.values())
            return {
                "status": "healthy" if all_healthy else "warning",
                "message": "所有智能体正常" if all_healthy else "部分智能体未初始化",
                "agents": agents_status
            }
        except Exception as e:
            return {"status": "error", "message": f"智能体状态检查失败: {e}"}
    
    async def _get_system_statistics(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        try:
            # 这里可以添加更多统计信息
            return {
                "total_workflows": 0,  # 从数据库查询
                "completed_workflows": 0,
                "failed_workflows": 0,
                "total_test_cases": 0,
                "generated_scripts": 0
            }
        except Exception as e:
            logger.warning(f"获取系统统计信息失败: {e}")
            return {}
    
    async def _create_app_info(self, package_name: str, app_name: str = None) -> Optional[AppInfo]:
        """创建应用信息"""
        try:
            # 检查是否已存在
            existing_app = self.db_manager.get_app_by_package_name(package_name)
            if existing_app:
                logger.info(f"应用信息已存在: {package_name}")
                return existing_app
            
            # 创建新的应用信息
            app_info = AppInfo(
                package_name=package_name,
                app_name=app_name or package_name.split('.')[-1],
                version="unknown",
                analysis_status="pending"
            )
            
            saved_app = self.db_manager.save_app_info(app_info)
            return saved_app
            
        except Exception as e:
            logger.error(f"创建应用信息失败: {e}")
            return None
    
    async def _get_app_elements(self, package_name: str) -> Optional[List]:
        """获取应用元素"""
        try:
            # 从数据库获取应用信息
            app_info = self.db_manager.get_app_by_package_name(package_name)
            if not app_info:
                return None
            
            # 获取应用的所有页面和元素
            # 这里需要实现具体的查询逻辑
            return []
            
        except Exception as e:
            logger.error(f"获取应用元素失败: {e}")
            return None
    
    async def _create_workflow(self, workflow_type: str, workflow_data: Dict[str, Any]) -> WorkflowExecution:
        """创建工作流"""
        try:
            workflow = WorkflowExecution(
                workflow_id=f"{workflow_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                workflow_type=workflow_type,
                session_id=f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                status="running",
                input_data=workflow_data,
                start_time=datetime.now()
            )
            
            # 保存到数据库
            # saved_workflow = self.db_manager.save_workflow_execution(workflow)
            # 暂时返回内存对象
            self.current_workflow = workflow
            return workflow
            
        except Exception as e:
            logger.error(f"创建工作流失败: {e}")
            raise
    
    async def _record_workflow_step(self, workflow_id: str, step_name: str, 
                                  status: str, result_data: Dict[str, Any]):
        """记录工作流步骤"""
        try:
            step = WorkflowStepExecution(
                workflow_id=workflow_id,
                step_name=step_name,
                step_index=len(self.workflow_steps) + 1,
                status=status,
                start_time=datetime.now(),
                end_time=datetime.now(),
                result_data=result_data
            )
            
            self.workflow_steps.append(step)
            logger.debug(f"记录工作流步骤: {step_name} - {status}")
            
        except Exception as e:
            logger.error(f"记录工作流步骤失败: {e}")
    
    async def _update_workflow_status(self, workflow_id: str, status: str):
        """更新工作流状态"""
        try:
            if self.current_workflow and self.current_workflow.workflow_id == workflow_id:
                self.current_workflow.status = status
                self.current_workflow.end_time = datetime.now()
                
                logger.info(f"工作流状态更新: {workflow_id} -> {status}")
            
        except Exception as e:
            logger.error(f"更新工作流状态失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        try:
            # 清理智能体
            if self.page_agent:
                self.page_agent.cleanup()
            
            if self.traversal_agent:
                self.traversal_agent.cleanup()
            
            if self.case_agent:
                self.case_agent.cleanup()
            
            if self.script_agent:
                self.script_agent.cleanup()
            
            # 清理核心组件
            if self.autogen_client:
                self.autogen_client.cleanup()
            
            if self.ui_helper:
                self.ui_helper.cleanup()
            
            if self.db_manager:
                self.db_manager.cleanup()
            
            logger.info("主控制器资源清理完成")
            
        except Exception as e:
            logger.warning(f"清理主控制器资源失败: {e}")


# 便捷函数
async def analyze_app(package_name: str, app_name: str = None) -> Dict[str, Any]:
    """分析应用的便捷函数"""
    controller = MainController()
    try:
        return await controller.analyze_app(package_name, app_name)
    finally:
        controller.cleanup()


async def process_test_case(test_case_data: Dict[str, Any]) -> Dict[str, Any]:
    """处理测试用例的便捷函数"""
    controller = MainController()
    try:
        return await controller.process_test_case(test_case_data)
    finally:
        controller.cleanup()


async def batch_process_cases(cases_data: List[Dict[str, Any]], 
                            package_name: str = None) -> Dict[str, Any]:
    """批量处理测试用例的便捷函数"""
    controller = MainController()
    try:
        return await controller.batch_process_cases(cases_data, package_name)
    finally:
        controller.cleanup()


if __name__ == "__main__":
    # 示例使用
    async def main():
        controller = MainController()
        
        try:
            # 获取系统状态
            status = await controller.get_system_status()
            print(f"系统状态: {status}")
            
            # 示例：分析应用
            # result = await controller.analyze_app("com.android.settings", "设置")
            # print(f"应用分析结果: {result}")
            
        finally:
            controller.cleanup()
    
    asyncio.run(main())
