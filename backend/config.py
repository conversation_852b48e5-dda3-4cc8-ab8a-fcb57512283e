"""
移动端应用UI自动化测试分析系统配置文件
"""
import os
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = "localhost"
    port: int = 3306
    username: str = "root"
    password: str = "root123456"
    database: str = "ui_analysis"
    charset: str = "utf8mb4"
    
    @property
    def connection_url(self) -> str:
        """获取数据库连接URL"""
        return f"mysql+pymysql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}?charset={self.charset}"


@dataclass
class DeviceConfig:
    """设备配置"""
    device_id: Optional[str] = None  # 设备ID，None表示使用默认设备
    connection_timeout: int = 30  # 连接超时时间（秒）
    operation_timeout: int = 10  # 操作超时时间（秒）
    retry_count: int = 3  # 重试次数
    wait_after_action: float = 1.0  # 操作后等待时间（秒）


@dataclass
class CrawlerConfig:
    """页面爬虫配置"""
    max_depth: int = 20  # 最大遍历深度
    max_pages: int = 100  # 最大页面数量
    page_load_timeout: int = 30  # 页面加载超时时间（秒）
    screenshot_enabled: bool = True  # 是否启用截图
    xml_save_enabled: bool = True  # 是否保存XML文件
    duplicate_check_enabled: bool = True  # 是否启用重复检查
    enable_ai_enhancement: bool = True  # 是否启用AI语义增强


@dataclass
class AIConfig:
    """AI智能体配置"""
    model_name: str = "doubao-1-5-ui-tars-250428"  # UI-TARS模型
    api_key: str = "7cd14776-e901-4875-868d-e01ee77a4eb2"
    base_url: str = "https://ark.cn-beijing.volces.com/api/v3"
    max_retries: int = 3  # 最大重试次数
    timeout: int = 60  # 请求超时时间（秒）
    fallback_enabled: bool = True  # 是否启用降级处理


@dataclass
class PathConfig:
    """路径配置"""
    project_root: Path = Path(__file__).parent.parent
    backend_dir: Path = project_root / "backend"
    temp_dir: Path = project_root / "temp"
    docs_dir: Path = project_root / "docs"
    screenshots_dir: Path = temp_dir / "screenshots"
    xml_files_dir: Path = temp_dir / "xml_files"
    logs_dir: Path = temp_dir / "logs"
    
    def __post_init__(self):
        """创建必要的目录"""
        for dir_path in [self.temp_dir, self.screenshots_dir, self.xml_files_dir, self.logs_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)


class SystemConfig:
    """系统配置管理器"""
    
    def __init__(self):
        self.database = DatabaseConfig()
        self.device = DeviceConfig()
        self.crawler = CrawlerConfig()
        self.ai = AIConfig()
        self.paths = PathConfig()
        
        # 从环境变量加载配置
        self._load_from_env()
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        # 数据库配置
        if os.getenv("DB_HOST"):
            self.database.host = os.getenv("DB_HOST")
        if os.getenv("DB_PORT"):
            self.database.port = int(os.getenv("DB_PORT"))
        if os.getenv("DB_USERNAME"):
            self.database.username = os.getenv("DB_USERNAME")
        if os.getenv("DB_PASSWORD"):
            self.database.password = os.getenv("DB_PASSWORD")
        if os.getenv("DB_DATABASE"):
            self.database.database = os.getenv("DB_DATABASE")
        
        # 设备配置
        if os.getenv("DEVICE_ID"):
            self.device.device_id = os.getenv("DEVICE_ID")
        
        # AI配置
        if os.getenv("AI_API_KEY"):
            self.ai.api_key = os.getenv("AI_API_KEY")
        if os.getenv("AI_BASE_URL"):
            self.ai.base_url = os.getenv("AI_BASE_URL")
    
    def get_element_filter_rules(self) -> Dict[str, Any]:
        """获取元素筛选规则"""
        return {
            "clickable_attributes": ["clickable", "long_clickable", "checkable", "editable", "scrollable"],
            "exclude_invisible": True,
            "exclude_zero_size": True,
            "min_size": {"width": 10, "height": 10},  # 最小尺寸
            "exclude_classes": [
                "android.view.View",  # 通用View，通常不可操作
                "android.widget.LinearLayout",  # 布局容器
                "android.widget.RelativeLayout",  # 布局容器
                "android.widget.FrameLayout",  # 布局容器
            ]
        }
    
    def get_page_identification_rules(self) -> Dict[str, Any]:
        """获取页面识别规则"""
        return {
            "use_activity_name": True,
            "use_content_hash": True,
            "hash_elements": ["text", "resource_id", "content_desc"],
            "stable_check_count": 2,  # 页面稳定性检查次数
            "stable_check_interval": 1.0,  # 页面稳定性检查间隔（秒）
        }
    
    def get_navigation_rules(self) -> Dict[str, Any]:
        """获取导航规则"""
        return {
            "allowed_actions": ["click", "long_click", "scroll"],
            "exclude_back_button": True,  # 排除返回按钮
            "exclude_system_ui": True,  # 排除系统UI
            "max_click_attempts": 3,  # 最大点击尝试次数
            "wait_after_click": 2.0,  # 点击后等待时间（秒）
        }


# 全局配置实例
config = SystemConfig()
