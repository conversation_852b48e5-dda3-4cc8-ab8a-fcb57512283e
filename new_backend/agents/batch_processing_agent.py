"""
批量处理智能体
实现Excel批量处理和工作流编排功能
"""
import os
import sys
import json
import asyncio
import pandas as pd
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加相对路径导入
sys.path.append(str(Path(__file__).parent.parent))

from config import config
from utils.database_manager import DatabaseManager
from new_backend.models.case_models import TestCase
from models.session_models import WorkflowExecution

# 导入其他智能体
from agents.case_analysis_agent import CaseAnalysisAgent
from agents.script_generation_agent import ScriptGenerationAgent
from agents.script_review_agent import ScriptReviewAgent

# 导入Excel处理工具
from utils.excel_case_reader import ExcelCaseReader
from utils.module_package_mapper import ModulePackageMapper


class BatchProcessingAgent:
    """批量处理智能体"""
    
    def __init__(self):
        """初始化批量处理智能体"""
        self.config = config
        self.agent_name = "batch_processing_agent"
        
        # 核心组件
        self.db_manager = DatabaseManager()
        
        # 智能体实例
        self.case_agent = CaseAnalysisAgent()
        self.script_agent = ScriptGenerationAgent()
        self.review_agent = ScriptReviewAgent()

        # Excel处理工具
        self.excel_reader = ExcelCaseReader()
        self.package_mapper = ModulePackageMapper()

        # 批处理状态
        self.current_batch = None
        self.processing_stats = {}
        
        logger.info("批量处理智能体初始化完成")
    
    async def process_excel_file(self, excel_file_path: str,
                               sheet_name: str = None,
                               start_row: int = 1) -> Dict[str, Any]:
        """
        处理Excel文件中的测试用例 - 支持真实Excel文件结构

        Args:
            excel_file_path: Excel文件路径
            sheet_name: 工作表名称（可选）
            start_row: 开始行号（默认从第2行开始，第1行为标题）

        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            logger.info(f"开始处理Excel文件: {excel_file_path}")

            # 使用新的Excel读取器读取真实Excel文件
            real_cases = self.excel_reader.read_test_cases_from_excel(excel_file_path)

            if not real_cases:
                return {"success": False, "error": "Excel文件读取失败或无有效数据"}

            logger.info(f"从Excel文件读取到 {len(real_cases)} 个测试用例")

            # 转换为标准格式
            standard_cases = self.excel_reader.convert_to_standard_format(real_cases)

            if not standard_cases:
                return {"success": False, "error": "用例格式转换失败"}

            logger.info(f"转换为 {len(standard_cases)} 个标准格式用例")

            # 创建批处理工作流
            workflow = await self._create_batch_workflow("excel_processing", {
                "excel_file": excel_file_path,
                "sheet_name": sheet_name,
                "total_cases": len(standard_cases),
                "real_cases_count": len(real_cases)
            })

            # 批量处理测试用例
            results = await self._batch_process_standard_cases(standard_cases, workflow.workflow_id)

            # 生成处理报告
            report = self._generate_batch_report(results, excel_file_path)

            # 保存报告
            report_file = await self._save_batch_report(report, workflow.workflow_id)

            logger.info(f"Excel文件处理完成: {excel_file_path}")
            return {
                "success": True,
                "workflow_id": workflow.workflow_id,
                "total_cases": len(standard_cases),
                "real_cases_count": len(real_cases),
                "results": results,
                "report": report,
                "report_file": report_file
            }

        except Exception as e:
            logger.error(f"处理Excel文件失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _read_excel_file(self, excel_file_path: str, 
                        sheet_name: str = None,
                        start_row: int = 1) -> List[Dict[str, Any]]:
        """读取Excel文件"""
        try:
            # 检查文件是否存在
            if not os.path.exists(excel_file_path):
                logger.error(f"Excel文件不存在: {excel_file_path}")
                return []
            
            # 读取Excel文件
            if sheet_name:
                df = pd.read_excel(excel_file_path, sheet_name=sheet_name, skiprows=start_row-1)
            else:
                df = pd.read_excel(excel_file_path, skiprows=start_row-1)
            
            # 检查必要的列
            required_columns = ['case_id', 'case_name', 'test_steps']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                logger.error(f"Excel文件缺少必要列: {missing_columns}")
                return []
            
            # 转换为字典列表
            test_cases_data = []
            for index, row in df.iterrows():
                try:
                    # 跳过空行
                    if pd.isna(row.get('case_id')) or pd.isna(row.get('case_name')):
                        continue
                    
                    case_data = {
                        'case_id': str(row.get('case_id', '')).strip(),
                        'case_name': str(row.get('case_name', '')).strip(),
                        'test_purpose': str(row.get('test_purpose', '')).strip() if not pd.isna(row.get('test_purpose')) else None,
                        'package_name': str(row.get('package_name', '')).strip() if not pd.isna(row.get('package_name')) else 'com.example.app',
                        'module_name': str(row.get('module_name', '')).strip() if not pd.isna(row.get('module_name')) else None,
                        'preconditions': str(row.get('preconditions', '')).strip() if not pd.isna(row.get('preconditions')) else None,
                        'test_steps': str(row.get('test_steps', '')).strip(),
                        'expected_results': str(row.get('expected_results', '')).strip() if not pd.isna(row.get('expected_results')) else None,
                        'priority': str(row.get('priority', 'medium')).strip().lower(),
                        'test_type': str(row.get('test_type', 'functional')).strip().lower(),
                        'excel_row': index + start_row + 1  # 记录原始行号
                    }
                    
                    # 验证数据
                    if self._validate_case_data(case_data):
                        test_cases_data.append(case_data)
                    else:
                        logger.warning(f"第{case_data['excel_row']}行数据验证失败，跳过")
                        
                except Exception as e:
                    logger.warning(f"处理第{index + start_row + 1}行数据失败: {e}")
                    continue
            
            logger.info(f"成功读取 {len(test_cases_data)} 个有效测试用例")
            return test_cases_data
            
        except Exception as e:
            logger.error(f"读取Excel文件失败: {e}")
            return []
    
    def _validate_case_data(self, case_data: Dict[str, Any]) -> bool:
        """验证用例数据"""
        try:
            # 检查必要字段
            required_fields = ['case_id', 'case_name', 'test_steps']
            for field in required_fields:
                if not case_data.get(field):
                    logger.warning(f"缺少必要字段: {field}")
                    return False
            
            # 检查字段长度
            if len(case_data['case_id']) > 100:
                logger.warning("case_id过长")
                return False
            
            if len(case_data['case_name']) > 200:
                logger.warning("case_name过长")
                return False
            
            if len(case_data['test_steps']) < 10:
                logger.warning("test_steps内容过短")
                return False
            
            return True
            
        except Exception as e:
            logger.warning(f"验证用例数据失败: {e}")
            return False
    
    async def _batch_process_cases(self, test_cases_data: List[Dict[str, Any]], 
                                 workflow_id: str) -> Dict[str, Any]:
        """批量处理测试用例"""
        try:
            results = {
                'total_cases': len(test_cases_data),
                'success_count': 0,
                'failed_count': 0,
                'case_results': [],
                'processing_time': 0,
                'start_time': datetime.now()
            }
            
            logger.info(f"开始批量处理 {len(test_cases_data)} 个测试用例")
            
            for i, case_data in enumerate(test_cases_data, 1):
                try:
                    logger.info(f"处理第 {i}/{len(test_cases_data)} 个用例: {case_data.get('case_name')}")
                    
                    # 处理单个用例
                    case_result = await self._process_single_case(case_data, workflow_id, i)
                    
                    # 更新统计
                    if case_result.get('success'):
                        results['success_count'] += 1
                    else:
                        results['failed_count'] += 1
                    
                    # 记录结果
                    results['case_results'].append({
                        'index': i,
                        'case_id': case_data.get('case_id'),
                        'case_name': case_data.get('case_name'),
                        'excel_row': case_data.get('excel_row'),
                        'result': case_result,
                        'processing_time': case_result.get('processing_time', 0)
                    })
                    
                    # 记录进度
                    progress = (i / len(test_cases_data)) * 100
                    logger.info(f"批处理进度: {progress:.1f}% ({i}/{len(test_cases_data)})")
                    
                except Exception as e:
                    logger.error(f"处理第 {i} 个用例失败: {e}")
                    results['failed_count'] += 1
                    results['case_results'].append({
                        'index': i,
                        'case_id': case_data.get('case_id', 'unknown'),
                        'case_name': case_data.get('case_name', 'unknown'),
                        'excel_row': case_data.get('excel_row'),
                        'result': {'success': False, 'error': str(e)},
                        'processing_time': 0
                    })
            
            # 计算总处理时间
            results['end_time'] = datetime.now()
            results['processing_time'] = (results['end_time'] - results['start_time']).total_seconds()
            
            logger.info(f"批量处理完成: 成功 {results['success_count']}, 失败 {results['failed_count']}")
            return results
            
        except Exception as e:
            logger.error(f"批量处理失败: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _process_single_case(self, case_data: Dict[str, Any], 
                                 workflow_id: str, case_index: int) -> Dict[str, Any]:
        """处理单个测试用例"""
        try:
            start_time = datetime.now()
            
            # 创建测试用例对象
            test_case = TestCase(**case_data)
            saved_case = self.db_manager.save_test_case(test_case)
            
            if not saved_case:
                return {'success': False, 'error': '保存测试用例失败'}
            
            # 步骤1: 用例分析
            analysis_result = await self.case_agent.analyze_test_case(saved_case)
            
            if not analysis_result.get('success'):
                return {
                    'success': False, 
                    'error': '用例分析失败',
                    'details': analysis_result,
                    'processing_time': (datetime.now() - start_time).total_seconds()
                }
            
            # 步骤2: 脚本生成
            script_result = await self.script_agent.generate_script(
                saved_case, 
                analysis_result.get('analysis_result', {})
            )
            
            if not script_result.get('success'):
                return {
                    'success': False,
                    'error': '脚本生成失败',
                    'details': script_result,
                    'analysis_result': analysis_result,
                    'processing_time': (datetime.now() - start_time).total_seconds()
                }
            
            # 步骤3: 脚本审查（可选）
            review_result = None
            if self.config.batch_processing.enable_script_review:
                try:
                    # 获取生成的脚本对象
                    script_obj = script_result.get('script_result', {})
                    if script_obj:
                        review_result = await self.review_agent.review_script(
                            script_obj, saved_case, analysis_result.get('analysis_result', {})
                        )
                except Exception as e:
                    logger.warning(f"脚本审查失败: {e}")
                    review_result = {'success': False, 'error': str(e)}
            
            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return {
                'success': True,
                'test_case_id': saved_case.id,
                'analysis_result': analysis_result,
                'script_result': script_result,
                'review_result': review_result,
                'processing_time': processing_time
            }
            
        except Exception as e:
            logger.error(f"处理单个用例失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'processing_time': (datetime.now() - start_time).total_seconds() if 'start_time' in locals() else 0
            }
    
    def _generate_batch_report(self, results: Dict[str, Any], 
                             excel_file_path: str) -> Dict[str, Any]:
        """生成批处理报告"""
        try:
            report = {
                'report_info': {
                    'report_id': f"batch_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    'generation_time': datetime.now().isoformat(),
                    'source_file': excel_file_path,
                    'processor_agent': self.agent_name
                },
                'summary': {
                    'total_cases': results.get('total_cases', 0),
                    'success_count': results.get('success_count', 0),
                    'failed_count': results.get('failed_count', 0),
                    'success_rate': (results.get('success_count', 0) / max(results.get('total_cases', 1), 1)) * 100,
                    'total_processing_time': results.get('processing_time', 0),
                    'average_processing_time': results.get('processing_time', 0) / max(results.get('total_cases', 1), 1)
                },
                'detailed_results': [],
                'statistics': {
                    'by_status': {},
                    'by_error_type': {},
                    'processing_time_distribution': {}
                },
                'recommendations': []
            }
            
            # 处理详细结果
            for case_result in results.get('case_results', []):
                result_detail = {
                    'case_index': case_result.get('index'),
                    'case_id': case_result.get('case_id'),
                    'case_name': case_result.get('case_name'),
                    'excel_row': case_result.get('excel_row'),
                    'status': 'success' if case_result.get('result', {}).get('success') else 'failed',
                    'processing_time': case_result.get('processing_time', 0),
                    'error': case_result.get('result', {}).get('error') if not case_result.get('result', {}).get('success') else None
                }
                
                # 添加分析和生成结果摘要
                if case_result.get('result', {}).get('success'):
                    analysis_result = case_result.get('result', {}).get('analysis_result', {})
                    script_result = case_result.get('result', {}).get('script_result', {})
                    
                    result_detail.update({
                        'analysis_confidence': analysis_result.get('analysis_result', {}).get('analysis_confidence', 0),
                        'extracted_actions_count': len(analysis_result.get('analysis_result', {}).get('extracted_actions', [])),
                        'extracted_assertions_count': len(analysis_result.get('analysis_result', {}).get('extracted_assertions', [])),
                        'script_generated': bool(script_result.get('script_result', {}).get('script_content')),
                        'script_length': len(script_result.get('script_result', {}).get('script_content', ''))
                    })
                
                report['detailed_results'].append(result_detail)
            
            # 生成统计信息
            report['statistics'] = self._generate_statistics(results)
            
            # 生成建议
            report['recommendations'] = self._generate_recommendations(results)
            
            return report
            
        except Exception as e:
            logger.error(f"生成批处理报告失败: {e}")
            return {'error': str(e)}
    
    def _generate_statistics(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成统计信息"""
        try:
            statistics = {
                'by_status': {'success': 0, 'failed': 0},
                'by_error_type': {},
                'processing_time_distribution': {
                    'min': float('inf'),
                    'max': 0,
                    'avg': 0,
                    'total': 0
                }
            }
            
            processing_times = []
            
            for case_result in results.get('case_results', []):
                # 状态统计
                if case_result.get('result', {}).get('success'):
                    statistics['by_status']['success'] += 1
                else:
                    statistics['by_status']['failed'] += 1
                    
                    # 错误类型统计
                    error = case_result.get('result', {}).get('error', 'unknown')
                    error_type = error.split(':')[0] if ':' in error else error
                    statistics['by_error_type'][error_type] = statistics['by_error_type'].get(error_type, 0) + 1
                
                # 处理时间统计
                processing_time = case_result.get('processing_time', 0)
                processing_times.append(processing_time)
                
                statistics['processing_time_distribution']['min'] = min(
                    statistics['processing_time_distribution']['min'], processing_time
                )
                statistics['processing_time_distribution']['max'] = max(
                    statistics['processing_time_distribution']['max'], processing_time
                )
            
            # 计算平均处理时间
            if processing_times:
                statistics['processing_time_distribution']['avg'] = sum(processing_times) / len(processing_times)
                statistics['processing_time_distribution']['total'] = sum(processing_times)
                
                if statistics['processing_time_distribution']['min'] == float('inf'):
                    statistics['processing_time_distribution']['min'] = 0
            
            return statistics
            
        except Exception as e:
            logger.warning(f"生成统计信息失败: {e}")
            return {}
    
    def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        try:
            recommendations = []
            
            success_rate = (results.get('success_count', 0) / max(results.get('total_cases', 1), 1)) * 100
            
            if success_rate < 50:
                recommendations.append("成功率较低，建议检查Excel文件格式和用例质量")
            elif success_rate < 80:
                recommendations.append("成功率中等，建议优化用例描述的清晰度")
            else:
                recommendations.append("批处理效果良好，可以考虑扩大批处理规模")
            
            # 基于错误类型的建议
            failed_cases = [case for case in results.get('case_results', []) 
                          if not case.get('result', {}).get('success')]
            
            if failed_cases:
                common_errors = {}
                for case in failed_cases:
                    error = case.get('result', {}).get('error', '')
                    if '分析失败' in error:
                        common_errors['analysis'] = common_errors.get('analysis', 0) + 1
                    elif '生成失败' in error:
                        common_errors['generation'] = common_errors.get('generation', 0) + 1
                    elif '保存失败' in error:
                        common_errors['database'] = common_errors.get('database', 0) + 1
                
                if common_errors.get('analysis', 0) > 0:
                    recommendations.append("多个用例分析失败，建议检查用例描述的完整性")
                if common_errors.get('generation', 0) > 0:
                    recommendations.append("多个脚本生成失败，建议检查AI模型配置")
                if common_errors.get('database', 0) > 0:
                    recommendations.append("数据库操作失败，建议检查数据库连接")
            
            # 性能建议
            avg_time = results.get('processing_time', 0) / max(results.get('total_cases', 1), 1)
            if avg_time > 60:  # 平均处理时间超过1分钟
                recommendations.append("处理时间较长，建议优化AI模型调用或增加并发处理")
            
            return recommendations

        except Exception as e:
            logger.warning(f"生成建议失败: {e}")
            return ["请手动检查处理结果"]

    async def _save_batch_report(self, report: Dict[str, Any],
                               workflow_id: str) -> Optional[str]:
        """保存批处理报告"""
        try:
            # 创建报告目录
            reports_dir = Path(self.config.data_paths.reports_dir)
            reports_dir.mkdir(exist_ok=True)

            # 生成报告文件名
            report_filename = f"batch_report_{workflow_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            report_file_path = reports_dir / report_filename

            # 保存JSON报告
            with open(report_file_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)

            # 生成Excel报告
            excel_report_path = await self._generate_excel_report(report, reports_dir, workflow_id)

            logger.info(f"批处理报告保存到: {report_file_path}")
            if excel_report_path:
                logger.info(f"Excel报告保存到: {excel_report_path}")

            return str(report_file_path)

        except Exception as e:
            logger.error(f"保存批处理报告失败: {e}")
            return None

    async def _generate_excel_report(self, report: Dict[str, Any],
                                   reports_dir: Path, workflow_id: str) -> Optional[str]:
        """生成Excel格式的报告"""
        try:
            excel_filename = f"batch_report_{workflow_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            excel_file_path = reports_dir / excel_filename

            with pd.ExcelWriter(excel_file_path, engine='openpyxl') as writer:
                # 摘要工作表
                summary_data = {
                    '指标': ['总用例数', '成功数', '失败数', '成功率(%)', '总处理时间(秒)', '平均处理时间(秒)'],
                    '值': [
                        report['summary']['total_cases'],
                        report['summary']['success_count'],
                        report['summary']['failed_count'],
                        round(report['summary']['success_rate'], 2),
                        round(report['summary']['total_processing_time'], 2),
                        round(report['summary']['average_processing_time'], 2)
                    ]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='摘要', index=False)

                # 详细结果工作表
                if report.get('detailed_results'):
                    detailed_df = pd.DataFrame(report['detailed_results'])
                    detailed_df.to_excel(writer, sheet_name='详细结果', index=False)

                # 统计信息工作表
                if report.get('statistics'):
                    stats = report['statistics']

                    # 状态统计
                    status_data = {
                        '状态': list(stats.get('by_status', {}).keys()),
                        '数量': list(stats.get('by_status', {}).values())
                    }
                    status_df = pd.DataFrame(status_data)
                    status_df.to_excel(writer, sheet_name='状态统计', index=False)

                    # 错误类型统计
                    if stats.get('by_error_type'):
                        error_data = {
                            '错误类型': list(stats['by_error_type'].keys()),
                            '数量': list(stats['by_error_type'].values())
                        }
                        error_df = pd.DataFrame(error_data)
                        error_df.to_excel(writer, sheet_name='错误统计', index=False)

                # 建议工作表
                if report.get('recommendations'):
                    recommendations_data = {
                        '序号': range(1, len(report['recommendations']) + 1),
                        '建议': report['recommendations']
                    }
                    recommendations_df = pd.DataFrame(recommendations_data)
                    recommendations_df.to_excel(writer, sheet_name='改进建议', index=False)

            return str(excel_file_path)

        except Exception as e:
            logger.error(f"生成Excel报告失败: {e}")
            return None

    async def _create_batch_workflow(self, workflow_type: str,
                                   workflow_data: Dict[str, Any]) -> WorkflowExecution:
        """创建批处理工作流"""
        try:
            workflow = WorkflowExecution(
                workflow_id=f"{workflow_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                workflow_type=workflow_type,
                session_id=f"batch_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                status="running",
                input_data=workflow_data,
                start_time=datetime.now()
            )

            self.current_batch = workflow
            return workflow

        except Exception as e:
            logger.error(f"创建批处理工作流失败: {e}")
            raise

    async def export_scripts_to_files(self, script_ids: List[str],
                                    output_dir: str = None) -> Dict[str, Any]:
        """
        导出脚本到文件

        Args:
            script_ids: 脚本ID列表
            output_dir: 输出目录（可选）

        Returns:
            Dict[str, Any]: 导出结果
        """
        try:
            if not output_dir:
                output_dir = Path(self.config.data_paths.scripts_dir) / f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)

            exported_files = []
            failed_exports = []

            for script_id in script_ids:
                try:
                    # 从数据库获取脚本
                    script = self.db_manager.get_script_by_id(script_id)
                    if not script:
                        failed_exports.append({'script_id': script_id, 'error': '脚本不存在'})
                        continue

                    # 生成文件名
                    safe_name = "".join(c for c in script.script_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
                    filename = f"{safe_name}_{script_id}.ts"
                    file_path = output_path / filename

                    # 写入脚本内容
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(script.script_content)

                    exported_files.append({
                        'script_id': script_id,
                        'script_name': script.script_name,
                        'file_path': str(file_path),
                        'file_size': len(script.script_content)
                    })

                except Exception as e:
                    failed_exports.append({'script_id': script_id, 'error': str(e)})

            logger.info(f"脚本导出完成: 成功 {len(exported_files)}, 失败 {len(failed_exports)}")
            return {
                'success': True,
                'output_dir': str(output_path),
                'exported_count': len(exported_files),
                'failed_count': len(failed_exports),
                'exported_files': exported_files,
                'failed_exports': failed_exports
            }

        except Exception as e:
            logger.error(f"导出脚本失败: {e}")
            return {'success': False, 'error': str(e)}

    async def create_project_structure(self, project_name: str,
                                     script_ids: List[str],
                                     output_dir: str = None) -> Dict[str, Any]:
        """
        创建完整的项目结构

        Args:
            project_name: 项目名称
            script_ids: 脚本ID列表
            output_dir: 输出目录（可选）

        Returns:
            Dict[str, Any]: 创建结果
        """
        try:
            if not output_dir:
                output_dir = Path(self.config.data_paths.projects_dir) / project_name

            project_path = Path(output_dir)
            project_path.mkdir(parents=True, exist_ok=True)

            # 创建项目目录结构
            directories = ['src', 'tests', 'config', 'docs', 'reports']
            for directory in directories:
                (project_path / directory).mkdir(exist_ok=True)

            # 导出脚本到tests目录
            export_result = await self.export_scripts_to_files(
                script_ids,
                str(project_path / 'tests')
            )

            # 创建package.json
            package_json = {
                "name": project_name.lower().replace(' ', '-'),
                "version": "1.0.0",
                "description": f"自动生成的测试项目: {project_name}",
                "main": "index.js",
                "scripts": {
                    "test": "echo \"Error: no test specified\" && exit 1"
                },
                "dependencies": {
                    "@midscene/android": "latest",
                    "dotenv": "^16.0.0"
                },
                "devDependencies": {
                    "@types/node": "^20.0.0",
                    "typescript": "^5.0.0"
                }
            }

            with open(project_path / 'package.json', 'w', encoding='utf-8') as f:
                json.dump(package_json, f, indent=2)

            # 创建tsconfig.json
            tsconfig = {
                "compilerOptions": {
                    "target": "ES2020",
                    "module": "commonjs",
                    "lib": ["ES2020"],
                    "outDir": "./dist",
                    "rootDir": "./src",
                    "strict": True,
                    "esModuleInterop": True,
                    "skipLibCheck": True,
                    "forceConsistentCasingInFileNames": True,
                    "resolveJsonModule": True
                },
                "include": ["src/**/*", "tests/**/*"],
                "exclude": ["node_modules", "dist"]
            }

            with open(project_path / 'tsconfig.json', 'w', encoding='utf-8') as f:
                json.dump(tsconfig, f, indent=2)

            # 创建README.md
            readme_content = f"""# {project_name}

自动生成的Android自动化测试项目

## 项目结构

- `src/`: 源代码目录
- `tests/`: 测试脚本目录
- `config/`: 配置文件目录
- `docs/`: 文档目录
- `reports/`: 测试报告目录

## 安装依赖

```bash
npm install
```

## 运行测试

```bash
# 编译TypeScript
npx tsc

# 运行测试脚本
node dist/tests/<script-name>.js
```

## 注意事项

1. 确保Android设备已连接并启用USB调试
2. 配置环境变量（参考.env.example）
3. 根据实际情况调整测试脚本中的应用包名

## 生成信息

- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 脚本数量: {len(script_ids)}
- 生成工具: 多智能体自动化脚本生成系统
"""

            with open(project_path / 'README.md', 'w', encoding='utf-8') as f:
                f.write(readme_content)

            # 创建.env.example
            env_example = """# Android设备配置
DEVICE_SERIAL=auto

# AI模型配置
OPENAI_API_KEY=your-api-key-here
ANTHROPIC_API_KEY=your-api-key-here

# 测试配置
TEST_TIMEOUT=60000
SCREENSHOT_DIR=./reports/screenshots
"""

            with open(project_path / '.env.example', 'w', encoding='utf-8') as f:
                f.write(env_example)

            logger.info(f"项目结构创建完成: {project_path}")
            return {
                'success': True,
                'project_path': str(project_path),
                'project_name': project_name,
                'export_result': export_result,
                'created_files': [
                    'package.json',
                    'tsconfig.json',
                    'README.md',
                    '.env.example'
                ]
            }

        except Exception as e:
            logger.error(f"创建项目结构失败: {e}")
            return {'success': False, 'error': str(e)}

    def get_batch_statistics(self) -> Dict[str, Any]:
        """获取批处理统计信息"""
        try:
            # 这里可以从数据库查询历史批处理统计
            return {
                'total_batches': 0,
                'total_processed_cases': 0,
                'average_success_rate': 0.0,
                'average_processing_time': 0.0,
                'most_common_errors': [],
                'performance_trends': []
            }
        except Exception as e:
            logger.error(f"获取批处理统计失败: {e}")
            return {}

    def cleanup(self):
        """清理资源"""
        try:
            if self.case_agent:
                self.case_agent.cleanup()

            if self.script_agent:
                self.script_agent.cleanup()

            if self.review_agent:
                self.review_agent.cleanup()

            logger.info("批量处理智能体资源清理完成")

        except Exception as e:
            logger.warning(f"清理批量处理智能体资源失败: {e}")
