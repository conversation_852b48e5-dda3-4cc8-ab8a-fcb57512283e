# 基于RAG和多应用的Page Object智能化自动化测试方案

## 📋 方案概述

基于现有Page Object模式，结合RAG（检索增强生成）技术和多应用架构，构建智能化的Android自动化测试框架。通过数据库存储多应用的页面元素信息，利用RAG技术智能检索匹配用例步骤与页面对象，自动生成可执行的UI自动化脚本。

### 🎯 核心优化目标
- **多应用支持**: 支持多个Android应用的Page Object管理
- **智能检索**: 基于RAG技术智能匹配用例步骤与页面元素
- **数据库驱动**: 元素信息存储在数据库中，支持动态更新
- **自动生成**: 根据自然语言用例自动生成完整的测试脚本
- **框架继承**: 生成的脚本继承现有框架的公共方法

### 🔧 技术架构特点
- **应用隔离**: 每个应用独立的Page Object体系
- **向量检索**: 使用向量数据库进行语义相似度匹配
- **智能映射**: 自然语言步骤到具体操作的智能映射
- **动态生成**: 实时生成符合框架规范的测试脚本

## 🏗️ 优化架构设计

### 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│              RAG驱动的多应用Page Object智能化测试框架            │
├─────────────────────────────────────────────────────────────────┤
│  🎯 自然语言处理层 (NL Processing Layer)                       │
│  ├── 自然语言用例解析器 (NL Case Parser)                       │
│  ├── 意图识别引擎 (Intent Recognition Engine)                  │
│  ├── 步骤分解器 (Step Decomposer)                              │
│  └── 语义向量化器 (Semantic Vectorizer)                       │
├─────────────────────────────────────────────────────────────────┤
│  🔍 RAG检索层 (RAG Retrieval Layer)                            │
│  ├── 向量数据库 (Vector Database - Milvus)                    │
│  ├── 语义检索引擎 (Semantic Search Engine)                     │
│  ├── 相似度计算器 (Similarity Calculator)                      │
│  └── 上下文增强器 (Context Enhancer)                          │
├─────────────────────────────────────────────────────────────────┤
│  📱 多应用管理层 (Multi-App Management Layer)                  │
│  ├── 应用注册中心 (App Registry)                               │
│  ├── 应用元数据管理 (App Metadata Manager)                     │
│  ├── 页面映射管理 (Page Mapping Manager)                       │
│  └── 应用版本控制 (App Version Control)                        │
├─────────────────────────────────────────────────────────────────┤
│  📄 智能Page Object层 (Intelligent Page Object Layer)          │
│  ├── 应用A Page Objects                                        │
│  │   ├── App_A_LoginPage, App_A_HomePage...                    │
│  ├── 应用B Page Objects                                        │
│  │   ├── App_B_LoginPage, App_B_HomePage...                    │
│  ├── 通用组件库 (Common Components)                            │
│  └── 动态Page Object生成器 (Dynamic PO Generator)              │
├─────────────────────────────────────────────────────────────────┤
│  💾 数据存储层 (Data Storage Layer)                            │
│  ├── 关系数据库 (MySQL)                                        │
│  │   ├── 应用信息表 (app_info)                                 │
│  │   ├── 页面信息表 (page_info)                                │
│  │   ├── 元素信息表 (element_info)                             │
│  │   └── 操作映射表 (action_mapping)                           │
│  ├── 向量数据库 (Milvus)                                       │
│  │   ├── 页面向量 (page_vectors)                               │
│  │   ├── 元素向量 (element_vectors)                            │
│  │   └── 操作向量 (action_vectors)                             │
│  └── 缓存层 (Redis)                                            │
├─────────────────────────────────────────────────────────────────┤
│  🤖 智能生成层 (Intelligent Generation Layer)                  │
│  ├── 脚本生成引擎 (Script Generation Engine)                   │
│  ├── 代码模板引擎 (Code Template Engine)                       │
│  ├── 依赖注入器 (Dependency Injector)                          │
│  └── 质量检查器 (Quality Checker)                              │
├─────────────────────────────────────────────────────────────────┤
│  🔧 执行引擎层 (Execution Engine Layer)                        │
│  ├── 动态脚本加载器 (Dynamic Script Loader)                    │
│  ├── 运行时环境管理 (Runtime Environment Manager)              │
│  ├── 执行监控器 (Execution Monitor)                            │
│  └── 结果收集器 (Result Collector)                             │
└─────────────────────────────────────────────────────────────────┘
```

### 核心模块设计

#### 1. 多应用数据库设计
```sql
-- 应用信息表
CREATE TABLE app_info (
    id VARCHAR(36) PRIMARY KEY,
    app_name VARCHAR(100) NOT NULL UNIQUE,
    package_name VARCHAR(200) NOT NULL UNIQUE,
    app_version VARCHAR(50),
    framework_version VARCHAR(50),
    description TEXT,
    icon_url VARCHAR(500),
    status ENUM('active', 'inactive', 'deprecated') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_package_name (package_name),
    INDEX idx_app_name (app_name)
);

-- 页面信息表
CREATE TABLE page_info (
    id VARCHAR(36) PRIMARY KEY,
    app_id VARCHAR(36) NOT NULL,
    page_name VARCHAR(100) NOT NULL,
    page_class_name VARCHAR(200) NOT NULL,
    page_type ENUM('activity', 'fragment', 'dialog', 'component') DEFAULT 'activity',
    page_identifier JSON NOT NULL,
    page_description TEXT,
    page_screenshot_url VARCHAR(500),
    page_hierarchy TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (app_id) REFERENCES app_info(id) ON DELETE CASCADE,
    UNIQUE KEY uk_app_page (app_id, page_name),
    INDEX idx_page_type (page_type)
);

-- 元素信息表
CREATE TABLE element_info (
    id VARCHAR(36) PRIMARY KEY,
    page_id VARCHAR(36) NOT NULL,
    element_name VARCHAR(100) NOT NULL,
    element_type ENUM('button', 'input', 'text', 'image', 'list', 'other') NOT NULL,
    locator_strategies JSON NOT NULL,
    element_description TEXT,
    element_attributes JSON,
    business_actions JSON,
    semantic_keywords TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (page_id) REFERENCES page_info(id) ON DELETE CASCADE,
    UNIQUE KEY uk_page_element (page_id, element_name),
    INDEX idx_element_type (element_type),
    FULLTEXT idx_semantic_keywords (semantic_keywords)
);

-- 操作映射表
CREATE TABLE action_mapping (
    id VARCHAR(36) PRIMARY KEY,
    action_name VARCHAR(100) NOT NULL,
    action_type ENUM('basic', 'composite', 'business_knowledge') NOT NULL,
    method_name VARCHAR(200) NOT NULL,
    method_class VARCHAR(200) NOT NULL,
    parameters_schema JSON,
    action_description TEXT,
    semantic_keywords TEXT,
    usage_examples JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_action_method (action_name, method_name),
    INDEX idx_action_type (action_type),
    FULLTEXT idx_action_keywords (semantic_keywords)
);

-- 应用页面流转关系表
CREATE TABLE page_flow (
    id VARCHAR(36) PRIMARY KEY,
    app_id VARCHAR(36) NOT NULL,
    from_page_id VARCHAR(36) NOT NULL,
    to_page_id VARCHAR(36) NOT NULL,
    trigger_action VARCHAR(200),
    flow_description TEXT,
    flow_probability DECIMAL(3,2) DEFAULT 1.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (app_id) REFERENCES app_info(id) ON DELETE CASCADE,
    FOREIGN KEY (from_page_id) REFERENCES page_info(id) ON DELETE CASCADE,
    FOREIGN KEY (to_page_id) REFERENCES page_info(id) ON DELETE CASCADE,
    UNIQUE KEY uk_page_flow (from_page_id, to_page_id, trigger_action)
);
```

#### 2. RAG检索引擎设计
```
framework/rag/
├── core/                               # RAG核心模块
│   ├── vectorizer.py                  # 向量化器
│   ├── retriever.py                   # 检索器
│   ├── ranker.py                      # 排序器
│   └── context_builder.py             # 上下文构建器
├── embeddings/                        # 嵌入模型
│   ├── text_embedder.py              # 文本嵌入
│   ├── semantic_embedder.py          # 语义嵌入
│   └── multimodal_embedder.py        # 多模态嵌入
├── storage/                           # 存储适配器
│   ├── milvus_adapter.py             # Milvus适配器
│   ├── mysql_adapter.py              # MySQL适配器
│   └── cache_adapter.py              # 缓存适配器
├── search/                            # 搜索引擎
│   ├── semantic_search.py            # 语义搜索
│   ├── hybrid_search.py              # 混合搜索
│   └── contextual_search.py          # 上下文搜索
└── utils/                             # 工具类
    ├── similarity_calculator.py      # 相似度计算
    ├── text_processor.py             # 文本处理
    └── query_optimizer.py            # 查询优化
```

## 🔄 核心业务流程

### 1. 多应用Page Object注册流程

```mermaid
sequenceDiagram
    participant Dev as 开发者
    participant AR as 应用注册中心
    participant DB as 数据库
    participant VDB as 向量数据库
    participant PG as Page Object生成器

    Dev->>AR: 注册新应用
    AR->>DB: 保存应用信息
    Dev->>AR: 上传页面定义
    AR->>DB: 保存页面和元素信息
    AR->>VDB: 生成语义向量
    VDB->>VDB: 存储页面/元素向量
    AR->>PG: 触发Page Object生成
    PG->>PG: 生成应用专属Page Object类
    PG->>AR: 返回生成结果
    AR->>Dev: 注册完成通知
```

### 2. RAG驱动的脚本生成流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant NLP as 自然语言处理
    participant RAG as RAG检索引擎
    participant VDB as 向量数据库
    participant DB as 关系数据库
    participant SG as 脚本生成引擎
    participant QC as 质量检查器

    U->>NLP: 输入自然语言用例+起始应用
    NLP->>NLP: 解析用例步骤
    NLP->>RAG: 发送语义查询
    RAG->>VDB: 向量相似度搜索
    VDB->>RAG: 返回候选页面/元素
    RAG->>DB: 查询详细信息
    DB->>RAG: 返回完整上下文
    RAG->>SG: 发送增强上下文
    SG->>SG: 生成测试脚本
    SG->>QC: 代码质量检查
    QC->>SG: 返回优化建议
    SG->>U: 返回可执行脚本
```

### 3. 智能元素匹配流程

```mermaid
sequenceDiagram
    participant Step as 测试步骤
    participant SE as 语义引擎
    participant VDB as 向量数据库
    participant CM as 上下文管理器
    participant EM as 元素匹配器

    Step->>SE: "点击登录按钮"
    SE->>SE: 提取关键词和意图
    SE->>VDB: 查询相似元素
    VDB->>SE: 返回候选元素列表
    SE->>CM: 获取当前页面上下文
    CM->>SE: 返回页面信息
    SE->>EM: 执行智能匹配
    EM->>EM: 计算匹配分数
    EM->>Step: 返回最佳匹配元素
```

## 💻 核心代码实现

### 1. 多应用管理器

#### AppRegistry应用注册中心
```python
"""
应用注册中心
管理多个Android应用的Page Object注册和元数据
"""
import json
import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger
from sqlalchemy.orm import Session

from framework.database.models import AppInfo, PageInfo, ElementInfo
from framework.rag.core.vectorizer import SemanticVectorizer
from framework.rag.storage.milvus_adapter import MilvusAdapter
from framework.page_objects.generator import PageObjectGenerator

class AppRegistry:
    """应用注册中心"""
    
    def __init__(self, db_session: Session):
        """
        初始化应用注册中心
        
        Args:
            db_session: 数据库会话
        """
        self.db = db_session
        self.vectorizer = SemanticVectorizer()
        self.vector_db = MilvusAdapter()
        self.page_generator = PageObjectGenerator()
        
    async def register_app(self, app_config: Dict[str, Any]) -> str:
        """
        注册新应用
        
        Args:
            app_config: 应用配置信息
            
        Returns:
            应用ID
        """
        try:
            # 创建应用记录
            app_id = str(uuid.uuid4())
            app_info = AppInfo(
                id=app_id,
                app_name=app_config["app_name"],
                package_name=app_config["package_name"],
                app_version=app_config.get("app_version", "1.0.0"),
                framework_version=app_config.get("framework_version", "1.0.0"),
                description=app_config.get("description", ""),
                icon_url=app_config.get("icon_url", ""),
                status="active"
            )
            
            self.db.add(app_info)
            self.db.commit()
            
            logger.info(f"应用注册成功: {app_config['app_name']} (ID: {app_id})")
            return app_id
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"应用注册失败: {str(e)}")
            raise
    
    async def register_page(self, app_id: str, page_config: Dict[str, Any]) -> str:
        """
        注册页面信息
        
        Args:
            app_id: 应用ID
            page_config: 页面配置信息
            
        Returns:
            页面ID
        """
        try:
            # 创建页面记录
            page_id = str(uuid.uuid4())
            page_info = PageInfo(
                id=page_id,
                app_id=app_id,
                page_name=page_config["page_name"],
                page_class_name=page_config["page_class_name"],
                page_type=page_config.get("page_type", "activity"),
                page_identifier=json.dumps(page_config["page_identifier"]),
                page_description=page_config.get("page_description", ""),
                page_screenshot_url=page_config.get("page_screenshot_url", ""),
                page_hierarchy=page_config.get("page_hierarchy", "")
            )
            
            self.db.add(page_info)
            
            # 注册页面元素
            elements = page_config.get("elements", [])
            for element_config in elements:
                await self._register_element(page_id, element_config)
            
            # 生成页面语义向量
            await self._generate_page_vectors(page_id, page_config)
            
            self.db.commit()
            
            logger.info(f"页面注册成功: {page_config['page_name']} (ID: {page_id})")
            return page_id
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"页面注册失败: {str(e)}")
            raise
    
    async def _register_element(self, page_id: str, element_config: Dict[str, Any]):
        """注册页面元素"""
        element_id = str(uuid.uuid4())
        element_info = ElementInfo(
            id=element_id,
            page_id=page_id,
            element_name=element_config["element_name"],
            element_type=element_config["element_type"],
            locator_strategies=json.dumps(element_config["locator_strategies"]),
            element_description=element_config.get("element_description", ""),
            element_attributes=json.dumps(element_config.get("element_attributes", {})),
            business_actions=json.dumps(element_config.get("business_actions", [])),
            semantic_keywords=element_config.get("semantic_keywords", "")
        )
        
        self.db.add(element_info)
        
        # 生成元素语义向量
        await self._generate_element_vectors(element_id, element_config)
    
    async def _generate_page_vectors(self, page_id: str, page_config: Dict[str, Any]):
        """生成页面语义向量"""
        try:
            # 构建页面语义文本
            semantic_text = f"{page_config['page_name']} {page_config.get('page_description', '')}"
            
            # 生成向量
            vector = await self.vectorizer.encode_text(semantic_text)
            
            # 存储到向量数据库
            await self.vector_db.insert_page_vector(page_id, vector, {
                "page_name": page_config["page_name"],
                "page_type": page_config.get("page_type", "activity"),
                "app_id": page_config.get("app_id", "")
            })
            
        except Exception as e:
            logger.error(f"生成页面向量失败: {str(e)}")
    
    async def _generate_element_vectors(self, element_id: str, element_config: Dict[str, Any]):
        """生成元素语义向量"""
        try:
            # 构建元素语义文本
            semantic_text = f"{element_config['element_name']} {element_config['element_type']} {element_config.get('element_description', '')} {element_config.get('semantic_keywords', '')}"
            
            # 生成向量
            vector = await self.vectorizer.encode_text(semantic_text)
            
            # 存储到向量数据库
            await self.vector_db.insert_element_vector(element_id, vector, {
                "element_name": element_config["element_name"],
                "element_type": element_config["element_type"],
                "business_actions": element_config.get("business_actions", [])
            })
            
        except Exception as e:
            logger.error(f"生成元素向量失败: {str(e)}")
    
    async def get_app_info(self, app_identifier: str) -> Optional[Dict[str, Any]]:
        """
        获取应用信息
        
        Args:
            app_identifier: 应用ID或包名
            
        Returns:
            应用信息字典
        """
        try:
            # 尝试按ID查询
            app = self.db.query(AppInfo).filter(AppInfo.id == app_identifier).first()
            
            # 如果按ID未找到，尝试按包名查询
            if not app:
                app = self.db.query(AppInfo).filter(AppInfo.package_name == app_identifier).first()
            
            if not app:
                return None
            
            return {
                "id": app.id,
                "app_name": app.app_name,
                "package_name": app.package_name,
                "app_version": app.app_version,
                "framework_version": app.framework_version,
                "description": app.description,
                "status": app.status
            }
            
        except Exception as e:
            logger.error(f"获取应用信息失败: {str(e)}")
            return None
    
    async def list_app_pages(self, app_id: str) -> List[Dict[str, Any]]:
        """
        获取应用的所有页面
        
        Args:
            app_id: 应用ID
            
        Returns:
            页面信息列表
        """
        try:
            pages = self.db.query(PageInfo).filter(PageInfo.app_id == app_id).all()
            
            return [
                {
                    "id": page.id,
                    "page_name": page.page_name,
                    "page_class_name": page.page_class_name,
                    "page_type": page.page_type,
                    "page_description": page.page_description
                }
                for page in pages
            ]
            
        except Exception as e:
            logger.error(f"获取应用页面列表失败: {str(e)}")
            return []
    
    async def generate_page_objects(self, app_id: str) -> bool:
        """
        为应用生成Page Object类
        
        Args:
            app_id: 应用ID
            
        Returns:
            生成是否成功
        """
        try:
            # 获取应用信息
            app_info = await self.get_app_info(app_id)
            if not app_info:
                logger.error(f"应用不存在: {app_id}")
                return False
            
            # 获取应用页面
            pages = await self.list_app_pages(app_id)
            
            # 生成Page Object类
            for page in pages:
                page_elements = await self._get_page_elements(page["id"])
                
                success = await self.page_generator.generate_page_object(
                    app_info=app_info,
                    page_info=page,
                    elements=page_elements
                )
                
                if not success:
                    logger.error(f"生成Page Object失败: {page['page_name']}")
                    return False
            
            logger.info(f"应用 {app_info['app_name']} 的Page Object生成完成")
            return True
            
        except Exception as e:
            logger.error(f"生成Page Object失败: {str(e)}")
            return False
    
    async def _get_page_elements(self, page_id: str) -> List[Dict[str, Any]]:
        """获取页面元素列表"""
        try:
            elements = self.db.query(ElementInfo).filter(ElementInfo.page_id == page_id).all()
            
            return [
                {
                    "id": element.id,
                    "element_name": element.element_name,
                    "element_type": element.element_type,
                    "locator_strategies": json.loads(element.locator_strategies),
                    "element_description": element.element_description,
                    "element_attributes": json.loads(element.element_attributes),
                    "business_actions": json.loads(element.business_actions),
                    "semantic_keywords": element.semantic_keywords
                }
                for element in elements
            ]
            
        except Exception as e:
            logger.error(f"获取页面元素失败: {str(e)}")
            return []
```

### 2. RAG检索引擎

#### SemanticRetriever语义检索器
```python
"""
语义检索器
基于RAG技术进行智能检索和匹配
"""
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from loguru import logger

from framework.rag.core.vectorizer import SemanticVectorizer
from framework.rag.storage.milvus_adapter import MilvusAdapter
from framework.rag.storage.mysql_adapter import MySQLAdapter
from framework.rag.utils.similarity_calculator import SimilarityCalculator

class SemanticRetriever:
    """语义检索器"""
    
    def __init__(self):
        """初始化语义检索器"""
        self.vectorizer = SemanticVectorizer()
        self.vector_db = MilvusAdapter()
        self.mysql_db = MySQLAdapter()
        self.similarity_calc = SimilarityCalculator()
        
    async def retrieve_page_for_step(
        self, 
        step_description: str, 
        app_id: str,
        current_page_context: Optional[Dict[str, Any]] = None,
        top_k: int = 5
    ) -> List[Dict[str, Any]]:
        """
        为测试步骤检索匹配的页面
        
        Args:
            step_description: 步骤描述
            app_id: 应用ID
            current_page_context: 当前页面上下文
            top_k: 返回top-k结果
            
        Returns:
            匹配的页面列表
        """
        try:
            # 生成步骤描述的向量
            step_vector = await self.vectorizer.encode_text(step_description)
            
            # 在向量数据库中搜索相似页面
            similar_pages = await self.vector_db.search_similar_pages(
                query_vector=step_vector,
                app_id=app_id,
                top_k=top_k
            )
            
            # 获取页面详细信息
            enriched_pages = []
            for page_result in similar_pages:
                page_info = await self.mysql_db.get_page_info(page_result["page_id"])
                if page_info:
                    page_info["similarity_score"] = page_result["score"]
                    enriched_pages.append(page_info)
            
            # 基于上下文重新排序
            if current_page_context:
                enriched_pages = await self._rerank_by_context(
                    enriched_pages, current_page_context
                )
            
            return enriched_pages
            
        except Exception as e:
            logger.error(f"页面检索失败: {str(e)}")
            return []
    
    async def retrieve_element_for_action(
        self, 
        action_description: str, 
        page_id: str,
        action_type: Optional[str] = None,
        top_k: int = 3
    ) -> List[Dict[str, Any]]:
        """
        为操作描述检索匹配的元素
        
        Args:
            action_description: 操作描述
            page_id: 页面ID
            action_type: 操作类型过滤
            top_k: 返回top-k结果
            
        Returns:
            匹配的元素列表
        """
        try:
            # 生成操作描述的向量
            action_vector = await self.vectorizer.encode_text(action_description)
            
            # 在向量数据库中搜索相似元素
            similar_elements = await self.vector_db.search_similar_elements(
                query_vector=action_vector,
                page_id=page_id,
                element_type=action_type,
                top_k=top_k
            )
            
            # 获取元素详细信息
            enriched_elements = []
            for element_result in similar_elements:
                element_info = await self.mysql_db.get_element_info(element_result["element_id"])
                if element_info:
                    element_info["similarity_score"] = element_result["score"]
                    enriched_elements.append(element_info)
            
            return enriched_elements
            
        except Exception as e:
            logger.error(f"元素检索失败: {str(e)}")
            return []
    
    async def retrieve_framework_methods(
        self, 
        action_description: str,
        method_type: Optional[str] = None,
        top_k: int = 5
    ) -> List[Dict[str, Any]]:
        """
        检索框架中匹配的方法
        
        Args:
            action_description: 操作描述
            method_type: 方法类型过滤
            top_k: 返回top-k结果
            
        Returns:
            匹配的框架方法列表
        """
        try:
            # 生成操作描述的向量
            action_vector = await self.vectorizer.encode_text(action_description)
            
            # 搜索匹配的框架方法
            similar_methods = await self.vector_db.search_similar_methods(
                query_vector=action_vector,
                method_type=method_type,
                top_k=top_k
            )
            
            # 获取方法详细信息
            enriched_methods = []
            for method_result in similar_methods:
                method_info = await self.mysql_db.get_action_mapping(method_result["method_id"])
                if method_info:
                    method_info["similarity_score"] = method_result["score"]
                    enriched_methods.append(method_info)
            
            return enriched_methods
            
        except Exception as e:
            logger.error(f"框架方法检索失败: {str(e)}")
            return []
    
    async def build_step_context(
        self, 
        step_description: str,
        app_id: str,
        previous_steps: List[str] = None
    ) -> Dict[str, Any]:
        """
        构建步骤的完整上下文
        
        Args:
            step_description: 当前步骤描述
            app_id: 应用ID
            previous_steps: 之前的步骤列表
            
        Returns:
            完整的步骤上下文
        """
        try:
            context = {
                "current_step": step_description,
                "app_id": app_id,
                "matched_pages": [],
                "matched_elements": [],
                "matched_methods": [],
                "confidence_score": 0.0
            }
            
            # 检索匹配的页面
            matched_pages = await self.retrieve_page_for_step(step_description, app_id)
            context["matched_pages"] = matched_pages
            
            # 如果找到匹配的页面，检索页面元素
            if matched_pages:
                best_page = matched_pages[0]
                matched_elements = await self.retrieve_element_for_action(
                    step_description, best_page["id"]
                )
                context["matched_elements"] = matched_elements
            
            # 检索匹配的框架方法
            matched_methods = await self.retrieve_framework_methods(step_description)
            context["matched_methods"] = matched_methods
            
            # 计算整体置信度
            context["confidence_score"] = self._calculate_context_confidence(context)
            
            return context
            
        except Exception as e:
            logger.error(f"构建步骤上下文失败: {str(e)}")
            return {}
    
    async def _rerank_by_context(
        self, 
        pages: List[Dict[str, Any]], 
        context: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """基于上下文重新排序页面"""
        try:
            # 这里可以实现更复杂的重排序逻辑
            # 例如考虑页面流转关系、用户行为模式等
            
            # 简单实现：基于页面类型和相似度重新计算分数
            for page in pages:
                context_bonus = 0.0
                
                # 如果当前在登录页面，下一步可能是主页
                if context.get("current_page_type") == "login" and page.get("page_type") == "home":
                    context_bonus = 0.1
                
                # 更新分数
                page["similarity_score"] += context_bonus
            
            # 重新排序
            pages.sort(key=lambda x: x["similarity_score"], reverse=True)
            
            return pages
            
        except Exception as e:
            logger.error(f"基于上下文重排序失败: {str(e)}")
            return pages
    
    def _calculate_context_confidence(self, context: Dict[str, Any]) -> float:
        """计算上下文置信度"""
        try:
            confidence = 0.0
            
            # 页面匹配置信度
            if context["matched_pages"]:
                page_confidence = context["matched_pages"][0].get("similarity_score", 0.0)
                confidence += page_confidence * 0.4
            
            # 元素匹配置信度
            if context["matched_elements"]:
                element_confidence = context["matched_elements"][0].get("similarity_score", 0.0)
                confidence += element_confidence * 0.4
            
            # 方法匹配置信度
            if context["matched_methods"]:
                method_confidence = context["matched_methods"][0].get("similarity_score", 0.0)
                confidence += method_confidence * 0.2
            
            return min(confidence, 1.0)
            
        except Exception as e:
            logger.error(f"计算置信度失败: {str(e)}")
            return 0.0
```

### 3. 智能脚本生成引擎

#### IntelligentScriptGenerator智能脚本生成器
```python
"""
智能脚本生成引擎
基于RAG检索结果和框架模板生成可执行的测试脚本
"""
import json
import re
from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger
from jinja2 import Template, Environment, FileSystemLoader

from framework.rag.core.retriever import SemanticRetriever
from framework.database.models import AppInfo
from framework.templates.script_templates import ScriptTemplateManager

class IntelligentScriptGenerator:
    """智能脚本生成引擎"""

    def __init__(self):
        """初始化脚本生成引擎"""
        self.retriever = SemanticRetriever()
        self.template_manager = ScriptTemplateManager()
        self.jinja_env = Environment(
            loader=FileSystemLoader('framework/templates'),
            trim_blocks=True,
            lstrip_blocks=True
        )

    async def generate_test_script(
        self,
        nl_test_case: str,
        app_identifier: str,
        test_name: str,
        additional_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        生成完整的测试脚本

        Args:
            nl_test_case: 自然语言测试用例
            app_identifier: 应用标识符（ID或包名）
            test_name: 测试名称
            additional_context: 额外上下文信息

        Returns:
            生成的脚本信息
        """
        try:
            logger.info(f"开始生成测试脚本: {test_name}")

            # 1. 解析自然语言用例
            parsed_steps = await self._parse_nl_test_case(nl_test_case)

            # 2. 获取应用信息
            app_info = await self._get_app_info(app_identifier)
            if not app_info:
                raise ValueError(f"应用不存在: {app_identifier}")

            # 3. 为每个步骤构建上下文
            step_contexts = []
            for i, step in enumerate(parsed_steps):
                context = await self.retriever.build_step_context(
                    step_description=step["description"],
                    app_id=app_info["id"],
                    previous_steps=[s["description"] for s in parsed_steps[:i]]
                )
                context["step_index"] = i
                context["step_info"] = step
                step_contexts.append(context)

            # 4. 生成脚本代码
            script_content = await self._generate_script_content(
                app_info=app_info,
                test_name=test_name,
                step_contexts=step_contexts,
                additional_context=additional_context
            )

            # 5. 生成项目文件
            project_files = await self._generate_project_files(
                app_info=app_info,
                test_name=test_name,
                script_content=script_content
            )

            # 6. 质量检查和优化
            quality_report = await self._quality_check(script_content)

            result = {
                "test_name": test_name,
                "app_info": app_info,
                "script_content": script_content,
                "project_files": project_files,
                "step_contexts": step_contexts,
                "quality_report": quality_report,
                "generated_at": datetime.now().isoformat()
            }

            logger.info(f"测试脚本生成完成: {test_name}")
            return result

        except Exception as e:
            logger.error(f"生成测试脚本失败: {str(e)}")
            raise

    async def _parse_nl_test_case(self, nl_test_case: str) -> List[Dict[str, Any]]:
        """解析自然语言测试用例"""
        try:
            # 按行分割并清理
            lines = [line.strip() for line in nl_test_case.split('\n') if line.strip()]

            steps = []
            for i, line in enumerate(lines):
                # 移除步骤编号
                step_text = re.sub(r'^\d+[\.\)]\s*', '', line)

                # 分析步骤类型
                step_type = self._classify_step_type(step_text)

                step = {
                    "index": i,
                    "description": step_text,
                    "type": step_type,
                    "action": self._extract_action(step_text),
                    "target": self._extract_target(step_text),
                    "value": self._extract_value(step_text)
                }
                steps.append(step)

            return steps

        except Exception as e:
            logger.error(f"解析自然语言用例失败: {str(e)}")
            return []

    def _classify_step_type(self, step_text: str) -> str:
        """分类步骤类型"""
        step_text_lower = step_text.lower()

        if any(keyword in step_text_lower for keyword in ['点击', 'click', '按', '选择']):
            return 'click'
        elif any(keyword in step_text_lower for keyword in ['输入', 'input', '填写', '键入']):
            return 'input'
        elif any(keyword in step_text_lower for keyword in ['验证', 'verify', '检查', '确认']):
            return 'verify'
        elif any(keyword in step_text_lower for keyword in ['滑动', 'swipe', '滚动', 'scroll']):
            return 'swipe'
        elif any(keyword in step_text_lower for keyword in ['等待', 'wait', '暂停']):
            return 'wait'
        elif any(keyword in step_text_lower for keyword in ['打开', 'open', '启动', 'launch']):
            return 'launch'
        else:
            return 'action'

    def _extract_action(self, step_text: str) -> str:
        """提取操作动词"""
        action_patterns = [
            r'(点击|click|按|选择)',
            r'(输入|input|填写|键入)',
            r'(验证|verify|检查|确认)',
            r'(滑动|swipe|滚动|scroll)',
            r'(等待|wait|暂停)',
            r'(打开|open|启动|launch)'
        ]

        for pattern in action_patterns:
            match = re.search(pattern, step_text, re.IGNORECASE)
            if match:
                return match.group(1)

        return step_text.split()[0] if step_text.split() else ""

    def _extract_target(self, step_text: str) -> str:
        """提取操作目标"""
        # 提取引号中的内容
        quoted_match = re.search(r'["""\'](.*?)["""\']', step_text)
        if quoted_match:
            return quoted_match.group(1)

        # 提取常见目标模式
        target_patterns = [
            r'(按钮|button)',
            r'(输入框|input|文本框)',
            r'(链接|link)',
            r'(图标|icon)',
            r'(菜单|menu)',
            r'(列表|list)',
            r'(页面|page)'
        ]

        for pattern in target_patterns:
            if re.search(pattern, step_text, re.IGNORECASE):
                return pattern.strip('()')

        return ""

    def _extract_value(self, step_text: str) -> str:
        """提取输入值"""
        # 提取引号中的内容作为值
        quoted_match = re.search(r'["""\'](.*?)["""\']', step_text)
        if quoted_match:
            return quoted_match.group(1)

        return ""

    async def _get_app_info(self, app_identifier: str) -> Optional[Dict[str, Any]]:
        """获取应用信息"""
        try:
            from framework.registry.app_registry import AppRegistry
            from framework.database.session import get_db_session

            with get_db_session() as db:
                registry = AppRegistry(db)
                return await registry.get_app_info(app_identifier)

        except Exception as e:
            logger.error(f"获取应用信息失败: {str(e)}")
            return None

    async def _generate_script_content(
        self,
        app_info: Dict[str, Any],
        test_name: str,
        step_contexts: List[Dict[str, Any]],
        additional_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """生成脚本内容"""
        try:
            # 构建模板上下文
            template_context = {
                "app_info": app_info,
                "test_name": test_name,
                "test_class_name": self._to_class_name(test_name),
                "test_method_name": self._to_method_name(test_name),
                "step_contexts": step_contexts,
                "imports": self._generate_imports(step_contexts),
                "setup_code": self._generate_setup_code(app_info),
                "test_steps": self._generate_test_steps(step_contexts),
                "cleanup_code": self._generate_cleanup_code(),
                "generated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            if additional_context:
                template_context.update(additional_context)

            # 加载并渲染模板
            template = self.jinja_env.get_template('intelligent_test_script.py.j2')
            script_content = template.render(**template_context)

            return script_content

        except Exception as e:
            logger.error(f"生成脚本内容失败: {str(e)}")
            raise

    def _generate_imports(self, step_contexts: List[Dict[str, Any]]) -> List[str]:
        """生成导入语句"""
        imports = [
            "import pytest",
            "import allure",
            "import time",
            "from typing import Dict, Any",
            "from loguru import logger",
            "",
            "from framework.pages.base.base_page import BasePage",
            "from framework.utils.test_helpers import TestHelpers",
            "from framework.data.test_data_factory import TestDataFactory"
        ]

        # 根据步骤上下文添加特定的导入
        used_pages = set()
        for context in step_contexts:
            for page in context.get("matched_pages", []):
                page_class = page.get("page_class_name", "")
                if page_class:
                    used_pages.add(page_class)

        # 添加页面导入
        for page_class in sorted(used_pages):
            module_name = self._class_to_module_name(page_class)
            imports.append(f"from framework.pages.{module_name} import {page_class}")

        return imports

    def _generate_setup_code(self, app_info: Dict[str, Any]) -> str:
        """生成设置代码"""
        return f'''
        self.driver = app_context
        self.app_package = "{app_info['package_name']}"
        self.test_helpers = TestHelpers(self.driver)

        # 启动应用
        self.driver.app_start(self.app_package, stop=True)
        time.sleep(2)
        '''

    def _generate_test_steps(self, step_contexts: List[Dict[str, Any]]) -> List[str]:
        """生成测试步骤代码"""
        test_steps = []

        for context in step_contexts:
            step_info = context["step_info"]
            step_code = self._generate_single_step_code(context)

            test_steps.append({
                "step_number": context["step_index"] + 1,
                "description": step_info["description"],
                "code": step_code,
                "confidence": context.get("confidence_score", 0.0)
            })

        return test_steps

    def _generate_single_step_code(self, context: Dict[str, Any]) -> str:
        """生成单个步骤的代码"""
        step_info = context["step_info"]
        step_type = step_info["type"]

        # 获取最佳匹配的页面和元素
        best_page = context["matched_pages"][0] if context["matched_pages"] else None
        best_element = context["matched_elements"][0] if context["matched_elements"] else None
        best_method = context["matched_methods"][0] if context["matched_methods"] else None

        if not best_page:
            return f'# TODO: 未找到匹配的页面 - {step_info["description"]}'

        page_class = best_page["page_class_name"]
        page_var = self._class_to_var_name(page_class)

        code_lines = []

        # 初始化页面对象
        code_lines.append(f'{page_var} = {page_class}(self.driver)')
        code_lines.append(f'assert {page_var}.wait_for_page_load(), "页面加载失败"')

        # 根据步骤类型生成具体操作
        if step_type == 'click' and best_element:
            element_name = best_element["element_name"]
            code_lines.append(f'assert {page_var}.click_element("{element_name}"), "点击失败"')

        elif step_type == 'input' and best_element:
            element_name = best_element["element_name"]
            input_value = step_info.get("value", "test_value")
            code_lines.append(f'assert {page_var}.input_text("{element_name}", "{input_value}"), "输入失败"')

        elif step_type == 'verify':
            if best_element:
                element_name = best_element["element_name"]
                code_lines.append(f'assert {page_var}.verify_element_present("{element_name}"), "元素验证失败"')
            else:
                verify_text = step_info.get("target", "")
                if verify_text:
                    code_lines.append(f'assert {page_var}.verify_text_present("{verify_text}"), "文本验证失败"')

        elif step_type == 'wait':
            wait_time = 2  # 默认等待时间
            code_lines.append(f'time.sleep({wait_time})')

        else:
            # 使用最佳匹配的框架方法
            if best_method:
                method_name = best_method["method_name"]
                code_lines.append(f'# 使用框架方法: {method_name}')
                code_lines.append(f'{page_var}.{method_name}()')
            else:
                code_lines.append(f'# TODO: 实现操作 - {step_info["description"]}')

        return '\n        '.join(code_lines)

    def _generate_cleanup_code(self) -> str:
        """生成清理代码"""
        return '''
        # 清理测试环境
        self.driver.press("home")
        time.sleep(1)
        '''

    async def _generate_project_files(
        self,
        app_info: Dict[str, Any],
        test_name: str,
        script_content: str
    ) -> Dict[str, str]:
        """生成项目文件"""
        app_name = app_info["app_name"].lower().replace(" ", "_")
        test_file_name = f"test_{self._to_snake_case(test_name)}.py"

        return {
            f"tests/{app_name}/{test_file_name}": script_content,
            f"tests/{app_name}/conftest.py": self._generate_conftest(app_info),
            f"tests/{app_name}/pytest.ini": self._generate_pytest_ini(),
            f"tests/{app_name}/requirements.txt": self._generate_requirements(),
            f"tests/{app_name}/README.md": self._generate_readme(app_info, test_name),
            f"tests/{app_name}/__init__.py": "",
        }

    def _generate_conftest(self, app_info: Dict[str, Any]) -> str:
        """生成conftest.py"""
        return f'''"""
{app_info["app_name"]} 测试配置
"""
import pytest
import uiautomator2 as u2
import allure
from typing import Generator

@pytest.fixture(scope="session")
def device() -> Generator[u2.Device, None, None]:
    """设备连接夹具"""
    d = u2.connect()
    d.implicitly_wait(10.0)
    d.settings['operation_delay'] = (0.2, 0.5)
    yield d
    d.app_stop_all(excludes=['com.android.systemui'])

@pytest.fixture(scope="function")
def app_context(device):
    """应用上下文夹具"""
    device.screen_on()
    device.unlock()
    yield device
    device.press("home")

@pytest.hookimpl(tryfirst=True, hookwrapper=True)
def pytest_runtest_makereport(item, call):
    """测试报告钩子"""
    outcome = yield
    rep = outcome.get_result()

    if rep.when == "call" and rep.failed:
        if hasattr(item, "funcargs") and "device" in item.funcargs:
            device = item.funcargs["device"]
            screenshot = device.screenshot(format='raw')
            allure.attach(
                screenshot,
                name="失败截图",
                attachment_type=allure.attachment_type.PNG
            )
'''

    def _generate_pytest_ini(self) -> str:
        """生成pytest.ini"""
        return '''[tool:pytest]
testpaths = .
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts =
    --allure-dir=allure-results
    --clean-alluredir
    --tb=short
    -v
markers =
    smoke: 冒烟测试
    regression: 回归测试
    ui: UI测试
    android: Android测试
    intelligent: 智能生成测试
'''

    def _generate_requirements(self) -> str:
        """生成requirements.txt"""
        return '''uiautomator2>=3.4.0
pytest>=7.0.0
allure-pytest>=2.12.0
pytest-html>=3.1.0
loguru>=0.6.0
'''

    def _generate_readme(self, app_info: Dict[str, Any], test_name: str) -> str:
        """生成README.md"""
        return f'''# {app_info["app_name"]} 自动化测试

## 测试用例: {test_name}

本测试用例由智能脚本生成引擎自动生成，基于RAG技术和Page Object模式。

### 应用信息
- **应用名称**: {app_info["app_name"]}
- **包名**: {app_info["package_name"]}
- **版本**: {app_info.get("app_version", "未知")}

### 运行测试

```bash
# 安装依赖
pip install -r requirements.txt

# 运行测试
pytest test_{self._to_snake_case(test_name)}.py -v

# 生成报告
allure serve allure-results
```

### 注意事项
1. 确保Android设备已连接并启用USB调试
2. 确保目标应用已安装在设备上
3. 测试脚本基于智能分析生成，可能需要人工调整
'''

    async def _quality_check(self, script_content: str) -> Dict[str, Any]:
        """代码质量检查"""
        quality_report = {
            "syntax_valid": True,
            "import_issues": [],
            "method_issues": [],
            "suggestions": [],
            "score": 0.0
        }

        try:
            # 语法检查
            compile(script_content, '<string>', 'exec')
            quality_report["syntax_valid"] = True
        except SyntaxError as e:
            quality_report["syntax_valid"] = False
            quality_report["suggestions"].append(f"语法错误: {str(e)}")

        # 检查TODO项
        todo_count = script_content.count("# TODO:")
        if todo_count > 0:
            quality_report["suggestions"].append(f"发现 {todo_count} 个待完成项，需要人工补充")

        # 计算质量分数
        base_score = 0.8 if quality_report["syntax_valid"] else 0.3
        todo_penalty = min(todo_count * 0.1, 0.3)
        quality_report["score"] = max(base_score - todo_penalty, 0.0)

        return quality_report

    def _to_class_name(self, name: str) -> str:
        """转换为类名格式"""
        name = re.sub(r'[^\w\s]', '', name)
        words = name.split()
        return ''.join(word.capitalize() for word in words)

    def _to_method_name(self, name: str) -> str:
        """转换为方法名格式"""
        name = re.sub(r'[^\w\s]', '', name)
        words = name.split()
        return words[0].lower() + ''.join(word.capitalize() for word in words[1:]) if words else ""

    def _to_snake_case(self, name: str) -> str:
        """转换为蛇形命名"""
        name = re.sub(r'[^\w\s]', '', name)
        name = re.sub(r'\s+', '_', name.strip())
        return name.lower()

    def _class_to_module_name(self, class_name: str) -> str:
        """类名转模块名"""
        # 将驼峰命名转换为下划线命名
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', class_name)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()

    def _class_to_var_name(self, class_name: str) -> str:
        """类名转变量名"""
        # 将类名转换为变量名（首字母小写）
        return class_name[0].lower() + class_name[1:] if class_name else ""
```

### 4. 智能脚本模板

#### 智能测试脚本模板 (intelligent_test_script.py.j2)
```python
"""
{{ app_info.app_name }} - {{ test_name }}
智能生成的Android自动化测试脚本

应用信息:
- 应用名称: {{ app_info.app_name }}
- 包名: {{ app_info.package_name }}
- 生成时间: {{ generated_at }}

注意: 本脚本由智能引擎自动生成，可能需要人工调整
"""

{% for import_line in imports %}
{{ import_line }}
{% endfor %}

@allure.epic("{{ app_info.app_name }}")
@allure.feature("智能生成测试")
@pytest.mark.android
@pytest.mark.intelligent
class {{ test_class_name }}:
    """{{ test_name }} - 智能生成的测试类"""

    @pytest.fixture(autouse=True)
    def setup_test(self, app_context):
        """测试前置设置"""
        {{ setup_code | indent(8) }}

    @allure.story("{{ test_name }}")
    @allure.title("{{ test_name }}")
    @allure.description("""
    本测试用例由智能脚本生成引擎自动生成
    基于RAG技术和Page Object模式

    测试步骤:
    {% for step in test_steps %}
    {{ step.step_number }}. {{ step.description }} (置信度: {{ "%.2f"|format(step.confidence) }})
    {% endfor %}
    """)
    @pytest.mark.smoke
    def {{ test_method_name }}(self):
        """{{ test_name }}"""

        try:
            {% for step in test_steps %}
            # 步骤{{ step.step_number }}: {{ step.description }}
            with allure.step("{{ step.description }}"):
                {{ step.code | indent(16) }}

                # 添加步骤间等待
                time.sleep(0.5)

            {% endfor %}

            logger.info("测试执行完成: {{ test_name }}")

        except Exception as e:
            logger.error(f"测试执行失败: {str(e)}")

            # 失败时截图
            screenshot = self.driver.screenshot(format='raw')
            allure.attach(
                screenshot,
                name="测试失败截图",
                attachment_type=allure.attachment_type.PNG
            )
            raise

        finally:
            # 清理测试环境
            {{ cleanup_code | indent(12) }}

    def _wait_for_element_with_retry(self, page_obj, element_name: str, max_retries: int = 3) -> bool:
        """带重试的元素等待"""
        for attempt in range(max_retries):
            try:
                if page_obj.verify_element_present(element_name, timeout=5):
                    return True
            except Exception as e:
                if attempt == max_retries - 1:
                    logger.error(f"元素等待失败: {element_name}, 错误: {str(e)}")
                    return False
                time.sleep(1)
        return False

    def _safe_operation(self, operation_func, operation_name: str, max_retries: int = 2) -> bool:
        """安全操作执行"""
        for attempt in range(max_retries):
            try:
                result = operation_func()
                if result:
                    return True
            except Exception as e:
                if attempt == max_retries - 1:
                    logger.error(f"操作失败: {operation_name}, 错误: {str(e)}")
                    return False
                time.sleep(0.5)
        return False
```

### 5. 完整使用示例

#### 智能脚本生成API
```python
"""
智能脚本生成API
提供RESTful接口调用智能脚本生成功能
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, Any
from loguru import logger

from framework.generation.intelligent_script_generator import IntelligentScriptGenerator
from framework.registry.app_registry import AppRegistry

router = APIRouter(prefix="/intelligent", tags=["智能脚本生成"])

@router.post("/generate-script")
async def generate_intelligent_script(
    request: Dict[str, Any],
    background_tasks: BackgroundTasks
) -> Dict[str, Any]:
    """
    生成智能测试脚本

    请求参数:
    - nl_test_case: 自然语言测试用例
    - app_identifier: 应用标识符
    - test_name: 测试名称
    - additional_context: 额外上下文
    """
    try:
        generator = IntelligentScriptGenerator()

        result = await generator.generate_test_script(
            nl_test_case=request["nl_test_case"],
            app_identifier=request["app_identifier"],
            test_name=request["test_name"],
            additional_context=request.get("additional_context")
        )

        # 后台保存生成的脚本
        background_tasks.add_task(
            save_generated_script,
            result
        )

        return {
            "success": True,
            "result": result,
            "message": "智能脚本生成完成"
        }

    except Exception as e:
        logger.error(f"智能脚本生成失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

async def save_generated_script(result: Dict[str, Any]):
    """保存生成的脚本到文件系统"""
    try:
        import os
        from pathlib import Path

        # 创建输出目录
        output_dir = Path("generated_tests") / result["app_info"]["app_name"]
        output_dir.mkdir(parents=True, exist_ok=True)

        # 保存所有项目文件
        for file_path, content in result["project_files"].items():
            full_path = output_dir / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)

            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)

        logger.info(f"脚本文件已保存到: {output_dir}")

    except Exception as e:
        logger.error(f"保存脚本文件失败: {str(e)}")
```

#### 实际使用示例
```python
"""
智能脚本生成使用示例
"""
import asyncio
from framework.generation.intelligent_script_generator import IntelligentScriptGenerator

async def example_usage():
    """使用示例"""

    # 自然语言测试用例
    nl_test_case = """
    1. 打开购物应用
    2. 点击搜索框
    3. 输入"手机"
    4. 点击搜索按钮
    5. 选择第一个商品
    6. 点击加入购物车
    7. 验证购物车图标显示数量
    8. 点击购物车
    9. 验证商品已添加到购物车
    """

    # 创建生成器
    generator = IntelligentScriptGenerator()

    # 生成测试脚本
    result = await generator.generate_test_script(
        nl_test_case=nl_test_case,
        app_identifier="com.example.shopping",  # 应用包名
        test_name="购物车添加商品测试",
        additional_context={
            "test_priority": "high",
            "test_category": "smoke"
        }
    )

    # 输出结果
    print("生成的测试脚本:")
    print(result["script_content"])

    print("\n质量报告:")
    print(f"语法正确: {result['quality_report']['syntax_valid']}")
    print(f"质量分数: {result['quality_report']['score']:.2f}")

    print("\n步骤上下文:")
    for i, context in enumerate(result["step_contexts"]):
        print(f"步骤{i+1}: {context['current_step']}")
        print(f"  置信度: {context['confidence_score']:.2f}")
        print(f"  匹配页面: {len(context['matched_pages'])}")
        print(f"  匹配元素: {len(context['matched_elements'])}")

if __name__ == "__main__":
    asyncio.run(example_usage())
```

## 💾 数据库模型和适配器

### 1. SQLAlchemy数据库模型

#### 数据库模型定义
```python
"""
数据库模型定义
支持多应用的Page Object元数据管理
"""
from sqlalchemy import Column, String, Text, JSON, DECIMAL, TIMESTAMP, Enum, ForeignKey, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

Base = declarative_base()

class AppStatus(enum.Enum):
    """应用状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DEPRECATED = "deprecated"

class PageType(enum.Enum):
    """页面类型枚举"""
    ACTIVITY = "activity"
    FRAGMENT = "fragment"
    DIALOG = "dialog"
    COMPONENT = "component"

class ElementType(enum.Enum):
    """元素类型枚举"""
    BUTTON = "button"
    INPUT = "input"
    TEXT = "text"
    IMAGE = "image"
    LIST = "list"
    OTHER = "other"

class ActionType(enum.Enum):
    """操作类型枚举"""
    BASIC = "basic"
    COMPOSITE = "composite"
    BUSINESS = "business_knowledge"

class AppInfo(Base):
    """应用信息表"""
    __tablename__ = 'app_info'

    id = Column(String(36), primary_key=True)
    app_name = Column(String(100), nullable=False, unique=True)
    package_name = Column(String(200), nullable=False, unique=True)
    app_version = Column(String(50))
    framework_version = Column(String(50))
    description = Column(Text)
    icon_url = Column(String(500))
    status = Column(Enum(AppStatus), default=AppStatus.ACTIVE)
    created_at = Column(TIMESTAMP, server_default=func.now())
    updated_at = Column(TIMESTAMP, server_default=func.now(), onupdate=func.now())

    # 关系
    pages = relationship("PageInfo", back_populates="app", cascade="all, delete-orphan")
    page_flows = relationship("PageFlow", back_populates="app", cascade="all, delete-orphan")

    # 索引
    __table_args__ = (
        Index('idx_package_name', 'package_name'),
        Index('idx_app_name', 'app_name'),
    )

class PageInfo(Base):
    """页面信息表"""
    __tablename__ = 'page_info'

    id = Column(String(36), primary_key=True)
    app_id = Column(String(36), ForeignKey('app_info.id'), nullable=False)
    page_name = Column(String(100), nullable=False)
    page_class_name = Column(String(200), nullable=False)
    page_type = Column(Enum(PageType), default=PageType.ACTIVITY)
    page_identifier = Column(JSON, nullable=False)
    page_description = Column(Text)
    page_screenshot_url = Column(String(500))
    page_hierarchy = Column(Text)
    created_at = Column(TIMESTAMP, server_default=func.now())
    updated_at = Column(TIMESTAMP, server_default=func.now(), onupdate=func.now())

    # 关系
    app = relationship("AppInfo", back_populates="pages")
    elements = relationship("ElementInfo", back_populates="page", cascade="all, delete-orphan")
    from_flows = relationship("PageFlow", foreign_keys="PageFlow.from_page_id", back_populates="from_page")
    to_flows = relationship("PageFlow", foreign_keys="PageFlow.to_page_id", back_populates="to_page")

    # 索引
    __table_args__ = (
        Index('uk_app_page', 'app_id', 'page_name', unique=True),
        Index('idx_page_type', 'page_type'),
    )

class ElementInfo(Base):
    """元素信息表"""
    __tablename__ = 'element_info'

    id = Column(String(36), primary_key=True)
    page_id = Column(String(36), ForeignKey('page_info.id'), nullable=False)
    element_name = Column(String(100), nullable=False)
    element_type = Column(Enum(ElementType), nullable=False)
    locator_strategies = Column(JSON, nullable=False)
    element_description = Column(Text)
    element_attributes = Column(JSON)
    business_actions = Column(JSON)
    semantic_keywords = Column(Text)
    created_at = Column(TIMESTAMP, server_default=func.now())
    updated_at = Column(TIMESTAMP, server_default=func.now(), onupdate=func.now())

    # 关系
    page = relationship("PageInfo", back_populates="elements")

    # 索引
    __table_args__ = (
        Index('uk_page_element', 'page_id', 'element_name', unique=True),
        Index('idx_element_type', 'element_type'),
        Index('idx_semantic_keywords', 'semantic_keywords', mysql_prefix='FULLTEXT'),
    )

class ActionMapping(Base):
    """操作映射表"""
    __tablename__ = 'action_mapping'

    id = Column(String(36), primary_key=True)
    action_name = Column(String(100), nullable=False)
    action_type = Column(Enum(ActionType), nullable=False)
    method_name = Column(String(200), nullable=False)
    method_class = Column(String(200), nullable=False)
    parameters_schema = Column(JSON)
    action_description = Column(Text)
    semantic_keywords = Column(Text)
    usage_examples = Column(JSON)
    created_at = Column(TIMESTAMP, server_default=func.now())
    updated_at = Column(TIMESTAMP, server_default=func.now(), onupdate=func.now())

    # 索引
    __table_args__ = (
        Index('uk_action_method', 'action_name', 'method_name', unique=True),
        Index('idx_action_type', 'action_type'),
        Index('idx_action_keywords', 'semantic_keywords', mysql_prefix='FULLTEXT'),
    )

class PageFlow(Base):
    """页面流转关系表"""
    __tablename__ = 'page_flow'

    id = Column(String(36), primary_key=True)
    app_id = Column(String(36), ForeignKey('app_info.id'), nullable=False)
    from_page_id = Column(String(36), ForeignKey('page_info.id'), nullable=False)
    to_page_id = Column(String(36), ForeignKey('page_info.id'), nullable=False)
    trigger_action = Column(String(200))
    flow_description = Column(Text)
    flow_probability = Column(DECIMAL(3, 2), default=1.00)
    created_at = Column(TIMESTAMP, server_default=func.now())

    # 关系
    app = relationship("AppInfo", back_populates="page_flows")
    from_page = relationship("PageInfo", foreign_keys=[from_page_id], back_populates="from_flows")
    to_page = relationship("PageInfo", foreign_keys=[to_page_id], back_populates="to_flows")

    # 索引
    __table_args__ = (
        Index('uk_page_flow', 'from_page_id', 'to_page_id', 'trigger_action', unique=True),
    )
```

### 2. Milvus向量数据库适配器

#### MilvusAdapter向量存储适配器
```python
"""
Milvus向量数据库适配器
管理页面、元素、操作的语义向量存储和检索
"""
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from pymilvus import connections, Collection, FieldSchema, CollectionSchema, DataType, utility
from loguru import logger

class MilvusAdapter:
    """Milvus向量数据库适配器"""

    def __init__(self, host: str = "localhost", port: int = 19530):
        """
        初始化Milvus连接

        Args:
            host: Milvus服务器地址
            port: Milvus服务器端口
        """
        self.host = host
        self.port = port
        self.connection_name = "default"
        self.vector_dim = 768  # 向量维度，根据嵌入模型调整

        # 集合名称
        self.page_collection_name = "page_vectors"
        self.element_collection_name = "element_vectors"
        self.method_collection_name = "method_vectors"

        self._connect()
        self._create_collections()

    def _connect(self):
        """连接到Milvus"""
        try:
            connections.connect(
                alias=self.connection_name,
                host=self.host,
                port=self.port
            )
            logger.info(f"成功连接到Milvus: {self.host}:{self.port}")
        except Exception as e:
            logger.error(f"连接Milvus失败: {str(e)}")
            raise

    def _create_collections(self):
        """创建向量集合"""
        try:
            # 创建页面向量集合
            self._create_page_collection()

            # 创建元素向量集合
            self._create_element_collection()

            # 创建方法向量集合
            self._create_method_collection()

        except Exception as e:
            logger.error(f"创建向量集合失败: {str(e)}")
            raise

    def _create_page_collection(self):
        """创建页面向量集合"""
        if utility.has_collection(self.page_collection_name):
            return

        fields = [
            FieldSchema(name="id", dtype=DataType.VARCHAR, max_length=36, is_primary=True),
            FieldSchema(name="page_id", dtype=DataType.VARCHAR, max_length=36),
            FieldSchema(name="app_id", dtype=DataType.VARCHAR, max_length=36),
            FieldSchema(name="page_name", dtype=DataType.VARCHAR, max_length=100),
            FieldSchema(name="page_type", dtype=DataType.VARCHAR, max_length=50),
            FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=self.vector_dim)
        ]

        schema = CollectionSchema(fields, description="页面语义向量集合")
        collection = Collection(self.page_collection_name, schema)

        # 创建索引
        index_params = {
            "metric_type": "COSINE",
            "index_type": "IVF_FLAT",
            "params": {"nlist": 128}
        }
        collection.create_index("vector", index_params)

        logger.info(f"页面向量集合创建成功: {self.page_collection_name}")

    def _create_element_collection(self):
        """创建元素向量集合"""
        if utility.has_collection(self.element_collection_name):
            return

        fields = [
            FieldSchema(name="id", dtype=DataType.VARCHAR, max_length=36, is_primary=True),
            FieldSchema(name="element_id", dtype=DataType.VARCHAR, max_length=36),
            FieldSchema(name="page_id", dtype=DataType.VARCHAR, max_length=36),
            FieldSchema(name="element_name", dtype=DataType.VARCHAR, max_length=100),
            FieldSchema(name="element_type", dtype=DataType.VARCHAR, max_length=50),
            FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=self.vector_dim)
        ]

        schema = CollectionSchema(fields, description="元素语义向量集合")
        collection = Collection(self.element_collection_name, schema)

        # 创建索引
        index_params = {
            "metric_type": "COSINE",
            "index_type": "IVF_FLAT",
            "params": {"nlist": 128}
        }
        collection.create_index("vector", index_params)

        logger.info(f"元素向量集合创建成功: {self.element_collection_name}")

    def _create_method_collection(self):
        """创建方法向量集合"""
        if utility.has_collection(self.method_collection_name):
            return

        fields = [
            FieldSchema(name="id", dtype=DataType.VARCHAR, max_length=36, is_primary=True),
            FieldSchema(name="method_id", dtype=DataType.VARCHAR, max_length=36),
            FieldSchema(name="action_name", dtype=DataType.VARCHAR, max_length=100),
            FieldSchema(name="method_name", dtype=DataType.VARCHAR, max_length=200),
            FieldSchema(name="action_type", dtype=DataType.VARCHAR, max_length=50),
            FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=self.vector_dim)
        ]

        schema = CollectionSchema(fields, description="方法语义向量集合")
        collection = Collection(self.method_collection_name, schema)

        # 创建索引
        index_params = {
            "metric_type": "COSINE",
            "index_type": "IVF_FLAT",
            "params": {"nlist": 128}
        }
        collection.create_index("vector", index_params)

        logger.info(f"方法向量集合创建成功: {self.method_collection_name}")

    async def insert_page_vector(
        self,
        page_id: str,
        vector: np.ndarray,
        metadata: Dict[str, Any]
    ) -> bool:
        """
        插入页面向量

        Args:
            page_id: 页面ID
            vector: 语义向量
            metadata: 元数据

        Returns:
            插入是否成功
        """
        try:
            collection = Collection(self.page_collection_name)

            data = [
                [f"page_{page_id}"],  # id
                [page_id],  # page_id
                [metadata.get("app_id", "")],  # app_id
                [metadata.get("page_name", "")],  # page_name
                [metadata.get("page_type", "")],  # page_type
                [vector.tolist()]  # vector
            ]

            collection.insert(data)
            collection.flush()

            logger.debug(f"页面向量插入成功: {page_id}")
            return True

        except Exception as e:
            logger.error(f"插入页面向量失败: {str(e)}")
            return False

    async def insert_element_vector(
        self,
        element_id: str,
        vector: np.ndarray,
        metadata: Dict[str, Any]
    ) -> bool:
        """
        插入元素向量

        Args:
            element_id: 元素ID
            vector: 语义向量
            metadata: 元数据

        Returns:
            插入是否成功
        """
        try:
            collection = Collection(self.element_collection_name)

            data = [
                [f"element_{element_id}"],  # id
                [element_id],  # element_id
                [metadata.get("page_id", "")],  # page_id
                [metadata.get("element_name", "")],  # element_name
                [metadata.get("element_type", "")],  # element_type
                [vector.tolist()]  # vector
            ]

            collection.insert(data)
            collection.flush()

            logger.debug(f"元素向量插入成功: {element_id}")
            return True

        except Exception as e:
            logger.error(f"插入元素向量失败: {str(e)}")
            return False

    async def search_similar_pages(
        self,
        query_vector: np.ndarray,
        app_id: str,
        top_k: int = 5,
        score_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """
        搜索相似页面

        Args:
            query_vector: 查询向量
            app_id: 应用ID
            top_k: 返回数量
            score_threshold: 分数阈值

        Returns:
            相似页面列表
        """
        try:
            collection = Collection(self.page_collection_name)
            collection.load()

            search_params = {
                "metric_type": "COSINE",
                "params": {"nprobe": 10}
            }

            # 构建过滤表达式
            expr = f'app_id == "{app_id}"'

            results = collection.search(
                data=[query_vector.tolist()],
                anns_field="vector",
                param=search_params,
                limit=top_k,
                expr=expr,
                output_fields=["page_id", "page_name", "page_type"]
            )

            similar_pages = []
            for hits in results:
                for hit in hits:
                    if hit.score >= score_threshold:
                        similar_pages.append({
                            "page_id": hit.entity.get("page_id"),
                            "page_name": hit.entity.get("page_name"),
                            "page_type": hit.entity.get("page_type"),
                            "score": hit.score
                        })

            return similar_pages

        except Exception as e:
            logger.error(f"搜索相似页面失败: {str(e)}")
            return []

    async def search_similar_elements(
        self,
        query_vector: np.ndarray,
        page_id: str,
        element_type: Optional[str] = None,
        top_k: int = 3,
        score_threshold: float = 0.6
    ) -> List[Dict[str, Any]]:
        """
        搜索相似元素

        Args:
            query_vector: 查询向量
            page_id: 页面ID
            element_type: 元素类型过滤
            top_k: 返回数量
            score_threshold: 分数阈值

        Returns:
            相似元素列表
        """
        try:
            collection = Collection(self.element_collection_name)
            collection.load()

            search_params = {
                "metric_type": "COSINE",
                "params": {"nprobe": 10}
            }

            # 构建过滤表达式
            expr = f'page_id == "{page_id}"'
            if element_type:
                expr += f' and element_type == "{element_type}"'

            results = collection.search(
                data=[query_vector.tolist()],
                anns_field="vector",
                param=search_params,
                limit=top_k,
                expr=expr,
                output_fields=["element_id", "element_name", "element_type"]
            )

            similar_elements = []
            for hits in results:
                for hit in hits:
                    if hit.score >= score_threshold:
                        similar_elements.append({
                            "element_id": hit.entity.get("element_id"),
                            "element_name": hit.entity.get("element_name"),
                            "element_type": hit.entity.get("element_type"),
                            "score": hit.score
                        })

            return similar_elements

        except Exception as e:
            logger.error(f"搜索相似元素失败: {str(e)}")
            return []

    async def search_similar_methods(
        self,
        query_vector: np.ndarray,
        method_type: Optional[str] = None,
        top_k: int = 5,
        score_threshold: float = 0.6
    ) -> List[Dict[str, Any]]:
        """
        搜索相似方法

        Args:
            query_vector: 查询向量
            method_type: 方法类型过滤
            top_k: 返回数量
            score_threshold: 分数阈值

        Returns:
            相似方法列表
        """
        try:
            collection = Collection(self.method_collection_name)
            collection.load()

            search_params = {
                "metric_type": "COSINE",
                "params": {"nprobe": 10}
            }

            # 构建过滤表达式
            expr = None
            if method_type:
                expr = f'action_type == "{method_type}"'

            results = collection.search(
                data=[query_vector.tolist()],
                anns_field="vector",
                param=search_params,
                limit=top_k,
                expr=expr,
                output_fields=["method_id", "action_name", "method_name", "action_type"]
            )

            similar_methods = []
            for hits in results:
                for hit in hits:
                    if hit.score >= score_threshold:
                        similar_methods.append({
                            "method_id": hit.entity.get("method_id"),
                            "action_name": hit.entity.get("action_name"),
                            "method_name": hit.entity.get("method_name"),
                            "action_type": hit.entity.get("action_type"),
                            "score": hit.score
                        })

            return similar_methods

        except Exception as e:
            logger.error(f"搜索相似方法失败: {str(e)}")
            return []

    def close(self):
        """关闭连接"""
        try:
            connections.disconnect(self.connection_name)
            logger.info("Milvus连接已关闭")
        except Exception as e:
            logger.error(f"关闭Milvus连接失败: {str(e)}")
```

### 3. 语义向量化器

#### SemanticVectorizer语义向量化器
```python
"""
语义向量化器
将文本转换为语义向量，支持多种嵌入模型
"""
import numpy as np
from typing import List, Union, Optional
from sentence_transformers import SentenceTransformer
from transformers import AutoTokenizer, AutoModel
import torch
from loguru import logger

class SemanticVectorizer:
    """语义向量化器"""

    def __init__(self, model_name: str = "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"):
        """
        初始化语义向量化器

        Args:
            model_name: 嵌入模型名称
        """
        self.model_name = model_name
        self.model = None
        self.tokenizer = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        self._load_model()

    def _load_model(self):
        """加载嵌入模型"""
        try:
            # 优先使用sentence-transformers
            if "sentence-transformers" in self.model_name:
                self.model = SentenceTransformer(self.model_name, device=self.device)
                logger.info(f"SentenceTransformer模型加载成功: {self.model_name}")
            else:
                # 使用transformers库
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
                self.model = AutoModel.from_pretrained(self.model_name).to(self.device)
                logger.info(f"Transformers模型加载成功: {self.model_name}")

        except Exception as e:
            logger.error(f"模型加载失败: {str(e)}")
            raise

    async def encode_text(self, text: str) -> np.ndarray:
        """
        将文本编码为向量

        Args:
            text: 输入文本

        Returns:
            文本向量
        """
        try:
            if isinstance(self.model, SentenceTransformer):
                # 使用sentence-transformers
                vector = self.model.encode(text, convert_to_numpy=True)
            else:
                # 使用transformers
                vector = self._encode_with_transformers(text)

            return vector.astype(np.float32)

        except Exception as e:
            logger.error(f"文本编码失败: {str(e)}")
            raise

    async def encode_texts(self, texts: List[str]) -> np.ndarray:
        """
        批量编码文本

        Args:
            texts: 文本列表

        Returns:
            文本向量矩阵
        """
        try:
            if isinstance(self.model, SentenceTransformer):
                # 使用sentence-transformers批量编码
                vectors = self.model.encode(texts, convert_to_numpy=True)
            else:
                # 使用transformers逐个编码
                vectors = []
                for text in texts:
                    vector = self._encode_with_transformers(text)
                    vectors.append(vector)
                vectors = np.array(vectors)

            return vectors.astype(np.float32)

        except Exception as e:
            logger.error(f"批量文本编码失败: {str(e)}")
            raise

    def _encode_with_transformers(self, text: str) -> np.ndarray:
        """使用transformers库编码文本"""
        try:
            # 分词
            inputs = self.tokenizer(
                text,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=512
            ).to(self.device)

            # 获取模型输出
            with torch.no_grad():
                outputs = self.model(**inputs)

            # 使用[CLS]标记的向量或平均池化
            if hasattr(outputs, 'last_hidden_state'):
                # 平均池化
                attention_mask = inputs['attention_mask']
                token_embeddings = outputs.last_hidden_state
                input_mask_expanded = attention_mask.unsqueeze(-1).expand(token_embeddings.size()).float()
                vector = torch.sum(token_embeddings * input_mask_expanded, 1) / torch.clamp(input_mask_expanded.sum(1), min=1e-9)
            else:
                # 使用pooler_output
                vector = outputs.pooler_output

            return vector.cpu().numpy().squeeze()

        except Exception as e:
            logger.error(f"Transformers编码失败: {str(e)}")
            raise

    def calculate_similarity(self, vector1: np.ndarray, vector2: np.ndarray) -> float:
        """
        计算两个向量的余弦相似度

        Args:
            vector1: 向量1
            vector2: 向量2

        Returns:
            余弦相似度
        """
        try:
            # 归一化向量
            norm1 = np.linalg.norm(vector1)
            norm2 = np.linalg.norm(vector2)

            if norm1 == 0 or norm2 == 0:
                return 0.0

            # 计算余弦相似度
            similarity = np.dot(vector1, vector2) / (norm1 * norm2)
            return float(similarity)

        except Exception as e:
            logger.error(f"相似度计算失败: {str(e)}")
            return 0.0

    def get_vector_dimension(self) -> int:
        """获取向量维度"""
        try:
            if isinstance(self.model, SentenceTransformer):
                return self.model.get_sentence_embedding_dimension()
            else:
                # 对于transformers模型，需要通过编码一个示例文本来获取维度
                sample_vector = self._encode_with_transformers("sample text")
                return sample_vector.shape[0]

        except Exception as e:
            logger.error(f"获取向量维度失败: {str(e)}")
            return 768  # 默认维度
```

## 🚀 部署和配置方案

### 1. Docker容器化部署

#### docker-compose.yml配置
```yaml
version: '3.8'

services:
  # 主应用服务
  intelligent-test-framework:
    build:
      context: .
      dockerfile: Dockerfile.intelligent
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql://root:password@mysql:3306/intelligent_test
      - MILVUS_HOST=milvus-standalone
      - MILVUS_PORT=19530
      - REDIS_URL=redis://redis:6379/0
      - EMBEDDING_MODEL=sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2
    volumes:
      - ./generated_tests:/app/generated_tests
      - ./logs:/app/logs
      - ./models:/app/models
    depends_on:
      - mysql
      - milvus-standalone
      - redis
    networks:
      - intelligent-test-network

  # MySQL数据库
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: intelligent_test
      MYSQL_USER: test_user
      MYSQL_PASSWORD: test_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - intelligent-test-network

  # Milvus向量数据库
  etcd:
    container_name: milvus-etcd
    image: quay.io/coreos/etcd:v3.5.5
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=4294967296
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - etcd_data:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    networks:
      - intelligent-test-network

  minio:
    container_name: milvus-minio
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    ports:
      - "9001:9001"
      - "9000:9000"
    volumes:
      - minio_data:/minio_data
    command: minio server /minio_data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - intelligent-test-network

  milvus-standalone:
    container_name: milvus-standalone
    image: milvusdb/milvus:v2.3.0
    command: ["milvus", "run", "standalone"]
    environment:
      ETCD_ENDPOINTS: etcd:2379
      MINIO_ADDRESS: minio:9000
    volumes:
      - milvus_data:/var/lib/milvus
    ports:
      - "19530:19530"
      - "9091:9091"
    depends_on:
      - "etcd"
      - "minio"
    networks:
      - intelligent-test-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - intelligent-test-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - intelligent-test-framework
    networks:
      - intelligent-test-network

volumes:
  mysql_data:
  milvus_data:
  etcd_data:
  minio_data:
  redis_data:

networks:
  intelligent-test-network:
    driver: bridge
```

#### Dockerfile.intelligent
```dockerfile
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    git \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY requirements.intelligent.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.intelligent.txt

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p /app/generated_tests /app/logs /app/models

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
```

### 2. 配置管理

#### 环境配置文件
```python
"""
环境配置管理
支持多环境配置和动态配置更新
"""
import os
from typing import Dict, Any, Optional
from pydantic import BaseSettings, Field
from functools import lru_cache

class DatabaseConfig(BaseSettings):
    """数据库配置"""
    url: str = Field(default="mysql://root:password@localhost:3306/intelligent_test")
    pool_size: int = Field(default=10)
    max_overflow: int = Field(default=20)
    pool_timeout: int = Field(default=30)
    pool_recycle: int = Field(default=3600)

    class Config:
        env_prefix = "DATABASE_"

class MilvusConfig(BaseSettings):
    """Milvus配置"""
    host: str = Field(default="localhost")
    port: int = Field(default=19530)
    collection_prefix: str = Field(default="intelligent_test")
    vector_dim: int = Field(default=768)
    index_type: str = Field(default="IVF_FLAT")
    metric_type: str = Field(default="COSINE")

    class Config:
        env_prefix = "MILVUS_"

class RedisConfig(BaseSettings):
    """Redis配置"""
    url: str = Field(default="redis://localhost:6379/0")
    max_connections: int = Field(default=10)
    socket_timeout: int = Field(default=5)
    socket_connect_timeout: int = Field(default=5)

    class Config:
        env_prefix = "REDIS_"

class EmbeddingConfig(BaseSettings):
    """嵌入模型配置"""
    model_name: str = Field(default="sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2")
    model_cache_dir: str = Field(default="./models")
    batch_size: int = Field(default=32)
    max_seq_length: int = Field(default=512)
    device: str = Field(default="auto")  # auto, cpu, cuda

    class Config:
        env_prefix = "EMBEDDING_"

class GenerationConfig(BaseSettings):
    """脚本生成配置"""
    template_dir: str = Field(default="./framework/templates")
    output_dir: str = Field(default="./generated_tests")
    quality_threshold: float = Field(default=0.7)
    max_retries: int = Field(default=3)
    timeout: int = Field(default=300)

    class Config:
        env_prefix = "GENERATION_"

class LoggingConfig(BaseSettings):
    """日志配置"""
    level: str = Field(default="INFO")
    format: str = Field(default="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}")
    rotation: str = Field(default="1 day")
    retention: str = Field(default="30 days")
    log_dir: str = Field(default="./logs")

    class Config:
        env_prefix = "LOGGING_"

class IntelligentTestConfig(BaseSettings):
    """主配置类"""
    environment: str = Field(default="development")
    debug: bool = Field(default=False)
    api_host: str = Field(default="0.0.0.0")
    api_port: int = Field(default=8000)
    api_workers: int = Field(default=1)

    # 子配置
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    milvus: MilvusConfig = Field(default_factory=MilvusConfig)
    redis: RedisConfig = Field(default_factory=RedisConfig)
    embedding: EmbeddingConfig = Field(default_factory=EmbeddingConfig)
    generation: GenerationConfig = Field(default_factory=GenerationConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

@lru_cache()
def get_config() -> IntelligentTestConfig:
    """获取配置实例（单例模式）"""
    return IntelligentTestConfig()

# 环境特定配置
def get_environment_config(env: str = None) -> Dict[str, Any]:
    """获取环境特定配置"""
    env = env or os.getenv("ENVIRONMENT", "development")

    configs = {
        "development": {
            "database": {
                "url": "mysql://root:password@localhost:3306/intelligent_test_dev"
            },
            "debug": True,
            "logging": {
                "level": "DEBUG"
            }
        },
        "testing": {
            "database": {
                "url": "mysql://root:password@localhost:3306/intelligent_test_test"
            },
            "debug": True,
            "logging": {
                "level": "DEBUG"
            }
        },
        "production": {
            "database": {
                "url": "mysql://root:password@mysql:3306/intelligent_test"
            },
            "debug": False,
            "api_workers": 4,
            "logging": {
                "level": "INFO"
            }
        }
    }

    return configs.get(env, configs["development"])
```

### 3. 初始化脚本

#### 数据库初始化脚本
```sql
-- sql/init.sql
-- 智能测试框架数据库初始化脚本

CREATE DATABASE IF NOT EXISTS intelligent_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE intelligent_test;

-- 创建应用信息表
CREATE TABLE IF NOT EXISTS app_info (
    id VARCHAR(36) PRIMARY KEY,
    app_name VARCHAR(100) NOT NULL UNIQUE,
    package_name VARCHAR(200) NOT NULL UNIQUE,
    app_version VARCHAR(50),
    framework_version VARCHAR(50),
    description TEXT,
    icon_url VARCHAR(500),
    status ENUM('active', 'inactive', 'deprecated') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_package_name (package_name),
    INDEX idx_app_name (app_name)
);

-- 创建页面信息表
CREATE TABLE IF NOT EXISTS page_info (
    id VARCHAR(36) PRIMARY KEY,
    app_id VARCHAR(36) NOT NULL,
    page_name VARCHAR(100) NOT NULL,
    page_class_name VARCHAR(200) NOT NULL,
    page_type ENUM('activity', 'fragment', 'dialog', 'component') DEFAULT 'activity',
    page_identifier JSON NOT NULL,
    page_description TEXT,
    page_screenshot_url VARCHAR(500),
    page_hierarchy TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (app_id) REFERENCES app_info(id) ON DELETE CASCADE,
    UNIQUE KEY uk_app_page (app_id, page_name),
    INDEX idx_page_type (page_type)
);

-- 创建元素信息表
CREATE TABLE IF NOT EXISTS element_info (
    id VARCHAR(36) PRIMARY KEY,
    page_id VARCHAR(36) NOT NULL,
    element_name VARCHAR(100) NOT NULL,
    element_type ENUM('button', 'input', 'text', 'image', 'list', 'other') NOT NULL,
    locator_strategies JSON NOT NULL,
    element_description TEXT,
    element_attributes JSON,
    business_actions JSON,
    semantic_keywords TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (page_id) REFERENCES page_info(id) ON DELETE CASCADE,
    UNIQUE KEY uk_page_element (page_id, element_name),
    INDEX idx_element_type (element_type),
    FULLTEXT idx_semantic_keywords (semantic_keywords)
);

-- 创建操作映射表
CREATE TABLE IF NOT EXISTS action_mapping (
    id VARCHAR(36) PRIMARY KEY,
    action_name VARCHAR(100) NOT NULL,
    action_type ENUM('basic', 'composite', 'business_knowledge') NOT NULL,
    method_name VARCHAR(200) NOT NULL,
    method_class VARCHAR(200) NOT NULL,
    parameters_schema JSON,
    action_description TEXT,
    semantic_keywords TEXT,
    usage_examples JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_action_method (action_name, method_name),
    INDEX idx_action_type (action_type),
    FULLTEXT idx_action_keywords (semantic_keywords)
);

-- 创建页面流转关系表
CREATE TABLE IF NOT EXISTS page_flow (
    id VARCHAR(36) PRIMARY KEY,
    app_id VARCHAR(36) NOT NULL,
    from_page_id VARCHAR(36) NOT NULL,
    to_page_id VARCHAR(36) NOT NULL,
    trigger_action VARCHAR(200),
    flow_description TEXT,
    flow_probability DECIMAL(3,2) DEFAULT 1.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (app_id) REFERENCES app_info(id) ON DELETE CASCADE,
    FOREIGN KEY (from_page_id) REFERENCES page_info(id) ON DELETE CASCADE,
    FOREIGN KEY (to_page_id) REFERENCES page_info(id) ON DELETE CASCADE,
    UNIQUE KEY uk_page_flow (from_page_id, to_page_id, trigger_action)
);

-- 插入示例数据
INSERT INTO app_info (id, app_name, package_name, app_version, description) VALUES
('app-001', '购物应用', 'com.example.shopping', '1.0.0', '电商购物应用'),
('app-002', '社交应用', 'com.example.social', '2.1.0', '社交媒体应用'),
('app-003', '工具应用', 'com.example.tools', '1.5.0', '实用工具应用');

-- 插入示例框架方法映射
INSERT INTO action_mapping (id, action_name, action_type, method_name, method_class, action_description, semantic_keywords) VALUES
('action-001', '点击元素', 'basic', 'click_element', 'BasePage', '点击页面元素', '点击 click 按 选择'),
('action-002', '输入文本', 'basic', 'input_text', 'BasePage', '在元素中输入文本', '输入 input 填写 键入'),
('action-003', '验证元素存在', 'basic', 'verify_element_present', 'BasePage', '验证元素是否存在', '验证 verify 检查 确认'),
('action-004', '等待页面加载', 'basic', 'wait_for_page_load', 'BasePage', '等待页面加载完成', '等待 wait 加载'),
('action-005', '滑动页面', 'basic', 'swipe_page', 'BasePage', '滑动页面', '滑动 swipe 滚动 scroll'),
('action-006', '登录操作', 'composite', 'perform_login', 'LoginAction', '执行登录操作', '登录 login 登入'),
('action-007', '搜索商品', 'business_knowledge', 'search_product', 'ShoppingAction', '搜索商品', '搜索 search 查找'),
('action-008', '添加到购物车', 'business_knowledge', 'add_to_cart', 'ShoppingAction', '添加商品到购物车', '加入购物车 添加 add cart');
```

### 4. 快速启动脚本

#### setup.sh安装脚本
```bash
#!/bin/bash
# 智能测试框架快速安装脚本

set -e

echo "🚀 开始安装智能测试框架..."

# 检查Docker和Docker Compose
check_docker() {
    echo "📋 检查Docker环境..."
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker未安装，请先安装Docker"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        echo "❌ Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi

    echo "✅ Docker环境检查通过"
}

# 创建必要目录
create_directories() {
    echo "📁 创建项目目录..."
    mkdir -p {generated_tests,logs,models,sql,nginx}
    mkdir -p nginx/ssl
    echo "✅ 目录创建完成"
}

# 下载配置文件
download_configs() {
    echo "⬇️ 下载配置文件..."

    # 创建nginx配置
    cat > nginx/nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream intelligent_test {
        server intelligent-test-framework:8000;
    }

    server {
        listen 80;
        server_name localhost;

        location / {
            proxy_pass http://intelligent_test;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
EOF

    # 创建环境配置文件
    cat > .env << 'EOF'
# 环境配置
ENVIRONMENT=development
DEBUG=true

# 数据库配置
DATABASE_URL=mysql://root:password@mysql:3306/intelligent_test
DATABASE_POOL_SIZE=10

# Milvus配置
MILVUS_HOST=milvus-standalone
MILVUS_PORT=19530

# Redis配置
REDIS_URL=redis://redis:6379/0

# 嵌入模型配置
EMBEDDING_MODEL=sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2
EMBEDDING_MODEL_CACHE_DIR=./models

# 日志配置
LOGGING_LEVEL=INFO
LOGGING_LOG_DIR=./logs
EOF

    echo "✅ 配置文件创建完成"
}

# 创建requirements文件
create_requirements() {
    echo "📦 创建依赖文件..."

    cat > requirements.intelligent.txt << 'EOF'
# 核心框架
fastapi>=0.100.0
uvicorn[standard]>=0.23.0
pydantic>=2.0.0
sqlalchemy>=2.0.0
alembic>=1.11.0

# 数据库驱动
pymysql>=1.1.0
redis>=4.6.0

# 向量数据库
pymilvus>=2.3.0

# 机器学习和NLP
sentence-transformers>=2.2.0
transformers>=4.30.0
torch>=2.0.0
numpy>=1.24.0
scikit-learn>=1.3.0

# 工具库
loguru>=0.7.0
jinja2>=3.1.0
pyyaml>=6.0
requests>=2.31.0
aiofiles>=23.0.0

# 测试相关
pytest>=7.4.0
pytest-asyncio>=0.21.0
allure-pytest>=2.13.0

# Android自动化
uiautomator2>=3.4.0
EOF

    echo "✅ 依赖文件创建完成"
}

# 启动服务
start_services() {
    echo "🚀 启动服务..."

    # 构建并启动服务
    docker-compose up -d --build

    echo "⏳ 等待服务启动..."
    sleep 30

    # 检查服务状态
    echo "📊 检查服务状态..."
    docker-compose ps

    # 等待数据库初始化
    echo "⏳ 等待数据库初始化..."
    sleep 10

    # 检查服务健康状态
    check_health
}

# 检查服务健康状态
check_health() {
    echo "🔍 检查服务健康状态..."

    # 检查API服务
    if curl -f http://localhost/health > /dev/null 2>&1; then
        echo "✅ API服务运行正常"
    else
        echo "❌ API服务异常"
    fi

    # 检查Milvus
    if docker-compose exec -T milvus-standalone curl -f http://localhost:9091/healthz > /dev/null 2>&1; then
        echo "✅ Milvus服务运行正常"
    else
        echo "❌ Milvus服务异常"
    fi

    # 检查MySQL
    if docker-compose exec -T mysql mysqladmin ping -h localhost -u root -ppassword > /dev/null 2>&1; then
        echo "✅ MySQL服务运行正常"
    else
        echo "❌ MySQL服务异常"
    fi
}

# 显示使用信息
show_usage() {
    echo ""
    echo "🎉 智能测试框架安装完成！"
    echo ""
    echo "📝 服务访问信息："
    echo "- API服务: http://localhost"
    echo "- API文档: http://localhost/docs"
    echo "- Milvus管理: http://localhost:9091"
    echo "- MySQL: localhost:3306 (root/password)"
    echo ""
    echo "🔧 常用命令："
    echo "- 查看日志: docker-compose logs -f"
    echo "- 停止服务: docker-compose down"
    echo "- 重启服务: docker-compose restart"
    echo "- 查看状态: docker-compose ps"
    echo ""
    echo "📚 下一步："
    echo "1. 注册应用: POST /intelligent/register-app"
    echo "2. 注册页面: POST /intelligent/register-page"
    echo "3. 生成脚本: POST /intelligent/generate-script"
    echo ""
}

# 主安装流程
main() {
    check_docker
    create_directories
    download_configs
    create_requirements
    start_services
    show_usage
}

# 错误处理
trap 'echo "❌ 安装过程中发生错误，请检查日志"; exit 1' ERR

# 执行安装
main "$@"
```

## 📚 完整使用指南

### 1. 应用注册和配置

#### 注册新应用
```python
"""
应用注册示例
演示如何注册新的Android应用到智能测试框架
"""
import asyncio
import requests
import json

# API基础URL
BASE_URL = "http://localhost"

async def register_shopping_app():
    """注册购物应用示例"""

    # 1. 注册应用基本信息
    app_config = {
        "app_name": "购物商城",
        "package_name": "com.example.shopping",
        "app_version": "2.1.0",
        "framework_version": "1.0.0",
        "description": "电商购物应用，支持商品浏览、购物车、订单管理等功能",
        "icon_url": "https://example.com/shopping-icon.png"
    }

    response = requests.post(f"{BASE_URL}/intelligent/register-app", json=app_config)
    app_result = response.json()
    app_id = app_result["app_id"]

    print(f"应用注册成功，ID: {app_id}")

    # 2. 注册登录页面
    login_page_config = {
        "app_id": app_id,
        "page_name": "登录页面",
        "page_class_name": "ShoppingLoginPage",
        "page_type": "activity",
        "page_identifier": {
            "primary": {"type": "text", "value": "用户登录"},
            "secondary": {"type": "resource_id", "value": "login_container"}
        },
        "page_description": "用户登录页面，支持用户名密码登录和第三方登录",
        "elements": [
            {
                "element_name": "username_input",
                "element_type": "input",
                "locator_strategies": {
                    "primary": {"type": "resource_id", "value": "et_username"},
                    "fallback": {"type": "text", "value": "请输入用户名"}
                },
                "element_description": "用户名输入框",
                "business_actions": ["input_username", "clear_username"],
                "semantic_keywords": "用户名 username 账号 手机号"
            },
            {
                "element_name": "password_input",
                "element_type": "input",
                "locator_strategies": {
                    "primary": {"type": "resource_id", "value": "et_password"},
                    "fallback": {"type": "text", "value": "请输入密码"}
                },
                "element_description": "密码输入框",
                "business_actions": ["input_password", "clear_password"],
                "semantic_keywords": "密码 password 口令"
            },
            {
                "element_name": "login_button",
                "element_type": "button",
                "locator_strategies": {
                    "primary": {"type": "resource_id", "value": "btn_login"},
                    "fallback": {"type": "text", "value": "登录"}
                },
                "element_description": "登录按钮",
                "business_actions": ["click_login"],
                "semantic_keywords": "登录 login 登入 确认"
            }
        ]
    }

    response = requests.post(f"{BASE_URL}/intelligent/register-page", json=login_page_config)
    login_page_result = response.json()

    print(f"登录页面注册成功，ID: {login_page_result['page_id']}")

    # 3. 注册首页
    home_page_config = {
        "app_id": app_id,
        "page_name": "首页",
        "page_class_name": "ShoppingHomePage",
        "page_type": "activity",
        "page_identifier": {
            "primary": {"type": "text", "value": "首页"},
            "secondary": {"type": "resource_id", "value": "home_container"}
        },
        "page_description": "应用首页，展示商品分类、推荐商品、搜索功能",
        "elements": [
            {
                "element_name": "search_box",
                "element_type": "input",
                "locator_strategies": {
                    "primary": {"type": "resource_id", "value": "et_search"},
                    "fallback": {"type": "text", "value": "搜索商品"}
                },
                "element_description": "搜索输入框",
                "business_actions": ["input_search_keyword", "clear_search"],
                "semantic_keywords": "搜索 search 查找 商品"
            },
            {
                "element_name": "search_button",
                "element_type": "button",
                "locator_strategies": {
                    "primary": {"type": "resource_id", "value": "btn_search"},
                    "fallback": {"type": "text", "value": "搜索"}
                },
                "element_description": "搜索按钮",
                "business_actions": ["click_search"],
                "semantic_keywords": "搜索 search 查找"
            },
            {
                "element_name": "product_list",
                "element_type": "list",
                "locator_strategies": {
                    "primary": {"type": "resource_id", "value": "rv_products"},
                    "fallback": {"type": "class_name", "value": "androidx.recyclerview.widget.RecyclerView"}
                },
                "element_description": "商品列表",
                "business_actions": ["select_product", "scroll_products"],
                "semantic_keywords": "商品列表 产品 商品"
            }
        ]
    }

    response = requests.post(f"{BASE_URL}/intelligent/register-page", json=home_page_config)
    home_page_result = response.json()

    print(f"首页注册成功，ID: {home_page_result['page_id']}")

    return app_id

# 运行注册示例
if __name__ == "__main__":
    app_id = asyncio.run(register_shopping_app())
    print(f"购物应用注册完成，应用ID: {app_id}")
```

### 2. 智能脚本生成

#### 生成测试脚本示例
```python
"""
智能脚本生成示例
演示如何使用自然语言生成Android自动化测试脚本
"""
import requests
import json

def generate_login_test_script():
    """生成登录测试脚本"""

    # 自然语言测试用例
    nl_test_case = """
    测试用例：用户登录功能验证

    测试步骤：
    1. 打开购物应用
    2. 等待登录页面加载完成
    3. 在用户名输入框中输入"test_user"
    4. 在密码输入框中输入"123456"
    5. 点击登录按钮
    6. 等待页面跳转
    7. 验证成功进入首页
    8. 验证首页显示用户信息

    预期结果：
    - 用户能够成功登录
    - 页面跳转到首页
    - 首页显示正确的用户信息
    """

    # 生成请求
    generation_request = {
        "nl_test_case": nl_test_case,
        "app_identifier": "com.example.shopping",  # 可以是应用ID或包名
        "test_name": "用户登录功能验证",
        "additional_context": {
            "test_priority": "high",
            "test_category": "smoke",
            "test_tags": ["login", "authentication", "smoke"],
            "timeout": 30,
            "retry_count": 3
        }
    }

    # 调用生成API
    response = requests.post(
        "http://localhost/intelligent/generate-script",
        json=generation_request
    )

    if response.status_code == 200:
        result = response.json()

        print("🎉 脚本生成成功！")
        print(f"测试名称: {result['result']['test_name']}")
        print(f"应用信息: {result['result']['app_info']['app_name']}")
        print(f"质量分数: {result['result']['quality_report']['score']:.2f}")

        # 显示生成的脚本
        print("\n📄 生成的测试脚本:")
        print("=" * 80)
        print(result['result']['script_content'])
        print("=" * 80)

        # 显示步骤上下文信息
        print("\n🔍 步骤分析结果:")
        for i, context in enumerate(result['result']['step_contexts']):
            step_info = context['step_info']
            print(f"\n步骤 {i+1}: {step_info['description']}")
            print(f"  - 步骤类型: {step_info['type']}")
            print(f"  - 置信度: {context['confidence_score']:.2f}")
            print(f"  - 匹配页面数: {len(context['matched_pages'])}")
            print(f"  - 匹配元素数: {len(context['matched_elements'])}")
            print(f"  - 匹配方法数: {len(context['matched_methods'])}")

            if context['matched_pages']:
                best_page = context['matched_pages'][0]
                print(f"  - 最佳页面: {best_page['page_name']} (相似度: {best_page['similarity_score']:.2f})")

            if context['matched_elements']:
                best_element = context['matched_elements'][0]
                print(f"  - 最佳元素: {best_element['element_name']} (相似度: {best_element['similarity_score']:.2f})")

        # 显示项目文件
        print("\n📁 生成的项目文件:")
        for file_path in result['result']['project_files'].keys():
            print(f"  - {file_path}")

        return result['result']
    else:
        print(f"❌ 脚本生成失败: {response.text}")
        return None

def generate_shopping_test_script():
    """生成购物流程测试脚本"""

    nl_test_case = """
    测试用例：完整购物流程测试

    前置条件：
    - 用户已登录
    - 应用已启动到首页

    测试步骤：
    1. 在首页搜索框中输入"手机"
    2. 点击搜索按钮
    3. 等待搜索结果加载
    4. 选择第一个商品
    5. 进入商品详情页
    6. 点击"加入购物车"按钮
    7. 修改商品数量为2
    8. 点击"立即购买"
    9. 进入订单确认页面
    10. 验证商品信息正确
    11. 验证数量为2
    12. 验证总价计算正确

    预期结果：
    - 能够成功搜索到商品
    - 商品能够正常加入购物车
    - 数量修改功能正常
    - 订单信息显示正确
    """

    generation_request = {
        "nl_test_case": nl_test_case,
        "app_identifier": "com.example.shopping",
        "test_name": "完整购物流程测试",
        "additional_context": {
            "test_priority": "high",
            "test_category": "regression",
            "test_tags": ["shopping", "cart", "order", "e2e"],
            "preconditions": ["user_logged_in", "app_on_homepage"],
            "test_data": {
                "search_keyword": "手机",
                "quantity": 2,
                "expected_product_type": "手机"
            }
        }
    }

    response = requests.post(
        "http://localhost/intelligent/generate-script",
        json=generation_request
    )

    if response.status_code == 200:
        result = response.json()
        print("🛒 购物流程测试脚本生成成功！")
        return result['result']
    else:
        print(f"❌ 购物流程脚本生成失败: {response.text}")
        return None

# 批量生成测试脚本
def batch_generate_scripts():
    """批量生成多个测试脚本"""

    test_cases = [
        {
            "name": "用户注册功能测试",
            "nl_case": """
            1. 打开应用
            2. 点击注册按钮
            3. 输入手机号
            4. 输入验证码
            5. 设置密码
            6. 点击完成注册
            7. 验证注册成功
            """,
            "category": "smoke"
        },
        {
            "name": "商品收藏功能测试",
            "nl_case": """
            1. 搜索商品
            2. 进入商品详情
            3. 点击收藏按钮
            4. 进入个人中心
            5. 查看我的收藏
            6. 验证商品已收藏
            """,
            "category": "regression"
        },
        {
            "name": "订单支付功能测试",
            "nl_case": """
            1. 添加商品到购物车
            2. 进入购物车
            3. 点击结算
            4. 选择收货地址
            5. 选择支付方式
            6. 确认支付
            7. 验证支付成功
            """,
            "category": "critical"
        }
    ]

    results = []
    for test_case in test_cases:
        print(f"\n🔄 生成测试脚本: {test_case['name']}")

        request = {
            "nl_test_case": test_case["nl_case"],
            "app_identifier": "com.example.shopping",
            "test_name": test_case["name"],
            "additional_context": {
                "test_category": test_case["category"]
            }
        }

        response = requests.post(
            "http://localhost/intelligent/generate-script",
            json=request
        )

        if response.status_code == 200:
            result = response.json()
            results.append(result['result'])
            print(f"✅ {test_case['name']} 生成成功")
        else:
            print(f"❌ {test_case['name']} 生成失败")

    print(f"\n📊 批量生成完成，成功生成 {len(results)} 个测试脚本")
    return results

if __name__ == "__main__":
    # 生成单个测试脚本
    print("🚀 开始生成登录测试脚本...")
    login_result = generate_login_test_script()

    print("\n" + "="*80)

    # 生成购物流程测试脚本
    print("🛒 开始生成购物流程测试脚本...")
    shopping_result = generate_shopping_test_script()

    print("\n" + "="*80)

    # 批量生成测试脚本
    print("📦 开始批量生成测试脚本...")
    batch_results = batch_generate_scripts()
```

### 3. 最佳实践和优化建议

#### 自然语言用例编写最佳实践
```markdown
# 自然语言测试用例编写最佳实践

## 1. 用例结构规范

### 推荐格式
```
测试用例：[功能模块] + [具体场景] + 测试

前置条件：
- 条件1
- 条件2

测试步骤：
1. 具体操作步骤1
2. 具体操作步骤2
3. 验证步骤

预期结果：
- 结果1
- 结果2
```

### 优秀示例
```
测试用例：购物车商品数量修改功能测试

前置条件：
- 用户已登录
- 购物车中已有商品

测试步骤：
1. 进入购物车页面
2. 找到目标商品
3. 点击数量增加按钮
4. 验证数量显示更新
5. 点击数量减少按钮
6. 验证数量显示更新
7. 验证总价重新计算

预期结果：
- 数量能够正确增减
- 总价实时更新
- 界面响应流畅
```

## 2. 操作描述规范

### 动作词汇标准化
- ✅ 推荐：点击"登录"按钮
- ✅ 推荐：在搜索框中输入"手机"
- ✅ 推荐：验证页面显示"欢迎"文字
- ❌ 避免：点一下那个按钮
- ❌ 避免：输入一些内容
- ❌ 避免：看看是否正确

### 元素描述规范
- 使用具体的元素名称：登录按钮、用户名输入框、商品列表
- 包含元素的文本内容："确定"按钮、"取消"链接
- 描述元素的位置：页面顶部的搜索框、底部的导航栏

### 数据描述规范
- 使用引号包围具体数据："test_user"、"123456"、"手机"
- 明确数据类型：数量为2、价格为99.99元
- 指定数据来源：使用测试账号、随机生成手机号

## 3. 验证点编写规范

### 功能验证
- 明确验证内容：验证登录成功、验证商品已添加
- 指定验证方式：检查页面标题、确认元素存在
- 包含预期值：验证显示"欢迎回来"、确认数量为2

### UI验证
- 元素存在性：验证用户头像显示
- 文本内容：验证页面标题为"首页"
- 状态变化：验证按钮变为可点击状态

### 数据验证
- 数值计算：验证总价等于单价乘以数量
- 数据一致性：验证订单信息与购物车一致
- 格式正确性：验证手机号格式正确

## 4. 常见问题和解决方案

### 问题1：步骤描述过于模糊
❌ 错误示例：
```
1. 打开应用
2. 进行登录
3. 检查结果
```

✅ 正确示例：
```
1. 启动购物应用
2. 在用户名输入框中输入"test_user"
3. 在密码输入框中输入"123456"
4. 点击"登录"按钮
5. 验证页面跳转到首页
6. 验证页面显示"欢迎回来，test_user"
```

### 问题2：缺少必要的等待和验证
❌ 错误示例：
```
1. 点击搜索按钮
2. 选择第一个商品
```

✅ 正确示例：
```
1. 点击搜索按钮
2. 等待搜索结果加载完成
3. 验证搜索结果列表显示
4. 选择搜索结果中的第一个商品
```

### 问题3：验证点不够具体
❌ 错误示例：
```
验证登录成功
```

✅ 正确示例：
```
验证页面跳转到首页
验证页面标题显示"首页"
验证右上角显示用户头像
验证底部导航栏显示完整
```
```

#### 性能优化建议
```python
"""
智能测试框架性能优化建议
"""

# 1. 向量检索优化
class VectorSearchOptimizer:
    """向量搜索优化器"""

    def __init__(self):
        self.cache = {}
        self.batch_size = 32

    async def optimized_search(self, queries: List[str], app_id: str):
        """优化的批量搜索"""
        # 批量向量化
        vectors = await self.vectorizer.encode_texts(queries)

        # 批量搜索
        results = await self.vector_db.batch_search(vectors, app_id)

        return results

# 2. 数据库查询优化
class DatabaseOptimizer:
    """数据库查询优化器"""

    def __init__(self):
        self.connection_pool = create_engine(
            DATABASE_URL,
            pool_size=20,
            max_overflow=30,
            pool_pre_ping=True
        )

    async def optimized_page_query(self, app_id: str):
        """优化的页面查询"""
        # 使用连接池和预编译语句
        query = """
        SELECT p.*, e.element_name, e.element_type, e.locator_strategies
        FROM page_info p
        LEFT JOIN element_info e ON p.id = e.page_id
        WHERE p.app_id = %s
        ORDER BY p.page_name, e.element_name
        """

        # 批量获取并缓存结果
        results = await self.execute_query(query, (app_id,))
        return self.group_by_page(results)

# 3. 缓存策略优化
class CacheOptimizer:
    """缓存优化器"""

    def __init__(self):
        self.redis_client = redis.Redis(
            connection_pool=redis.ConnectionPool(
                host='redis',
                port=6379,
                max_connections=20
            )
        )

    async def cached_vector_search(self, query: str, app_id: str):
        """带缓存的向量搜索"""
        cache_key = f"vector_search:{app_id}:{hash(query)}"

        # 尝试从缓存获取
        cached_result = await self.redis_client.get(cache_key)
        if cached_result:
            return json.loads(cached_result)

        # 执行搜索并缓存结果
        result = await self.vector_search(query, app_id)
        await self.redis_client.setex(
            cache_key,
            3600,  # 1小时过期
            json.dumps(result)
        )

        return result

# 4. 并发处理优化
class ConcurrencyOptimizer:
    """并发处理优化器"""

    def __init__(self):
        self.semaphore = asyncio.Semaphore(10)  # 限制并发数

    async def concurrent_step_processing(self, steps: List[str], app_id: str):
        """并发处理测试步骤"""
        async def process_step(step):
            async with self.semaphore:
                return await self.build_step_context(step, app_id)

        # 并发处理所有步骤
        tasks = [process_step(step) for step in steps]
        results = await asyncio.gather(*tasks)

        return results
```

---

*本优化方案基于RAG技术和多应用架构，提供了完整的使用指南、最佳实践和性能优化建议。通过详细的应用注册流程、智能脚本生成示例、自然语言用例编写规范和性能优化策略，为用户提供了全面的技术指导，确保能够高效地使用智能化测试框架，实现从自然语言到可执行测试脚本的完整转换。*
