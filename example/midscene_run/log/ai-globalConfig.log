[2025-08-29T17:06:36.842+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-08-29T17:06:36.842+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-08-29T17:06:36.843+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-08-29T17:06:36.843+08:00] provider has no specific model SDK declared
[2025-08-29T17:06:36.843+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_****************************************************0a0',
  openaiExtraConfig: undefined,
  modelName: 'qwen/qwen-vl-max-latest',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-08-29T17:06:36.843+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig qwen/qwen-vl-max-latest
[2025-08-29T17:06:36.843+08:00] decideModelConfig as legacy logic with intent default.
[2025-08-29T17:06:36.844+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-08-29T17:06:36.844+08:00] provider has no specific model SDK declared
[2025-08-29T17:06:36.844+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_****************************************************0a0',
  openaiExtraConfig: undefined,
  modelName: 'qwen/qwen-vl-max-latest',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-08-29T17:06:36.844+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-08-29T17:06:36.844+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-08-29T17:06:36.845+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-08-29T17:06:36.845+08:00] provider has no specific model SDK declared
[2025-08-29T17:06:36.845+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_****************************************************0a0',
  openaiExtraConfig: undefined,
  modelName: 'qwen/qwen-vl-max-latest',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-08-29T17:06:36.845+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-08-29T17:06:36.845+08:00] decideModelConfig as legacy logic with intent planning.
[2025-08-29T17:06:36.845+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-08-29T17:06:36.845+08:00] provider has no specific model SDK declared
[2025-08-29T17:06:36.845+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_****************************************************0a0',
  openaiExtraConfig: undefined,
  modelName: 'qwen/qwen-vl-max-latest',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-08-29T17:07:27.169+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-08-29T17:07:27.169+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-08-29T17:07:27.170+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-08-29T17:07:27.170+08:00] provider has no specific model SDK declared
[2025-08-29T17:07:27.171+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_****************************************************0a0',
  openaiExtraConfig: undefined,
  modelName: 'deepseek-v3',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-08-29T17:07:27.171+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig deepseek-v3
[2025-08-29T17:07:27.171+08:00] decideModelConfig as legacy logic with intent default.
[2025-08-29T17:07:27.171+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-08-29T17:07:27.171+08:00] provider has no specific model SDK declared
[2025-08-29T17:07:27.171+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_****************************************************0a0',
  openaiExtraConfig: undefined,
  modelName: 'deepseek-v3',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-08-29T17:07:27.171+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-08-29T17:07:27.171+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-08-29T17:07:27.171+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-08-29T17:07:27.171+08:00] provider has no specific model SDK declared
[2025-08-29T17:07:27.172+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_****************************************************0a0',
  openaiExtraConfig: undefined,
  modelName: 'deepseek-v3',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-08-29T17:07:27.172+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-08-29T17:07:27.172+08:00] decideModelConfig as legacy logic with intent planning.
[2025-08-29T17:07:27.172+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-08-29T17:07:27.172+08:00] provider has no specific model SDK declared
[2025-08-29T17:07:27.172+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_****************************************************0a0',
  openaiExtraConfig: undefined,
  modelName: 'deepseek-v3',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-08-29T17:14:43.171+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-08-29T17:14:43.171+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-08-29T17:14:43.171+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-08-29T17:14:43.171+08:00] provider has no specific model SDK declared
[2025-08-29T17:14:43.172+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_****************************************************0a0',
  openaiExtraConfig: undefined,
  modelName: 'deepseek-v3',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-08-29T17:14:43.172+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig deepseek-v3
[2025-08-29T17:14:43.172+08:00] decideModelConfig as legacy logic with intent default.
[2025-08-29T17:14:43.172+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-08-29T17:14:43.172+08:00] provider has no specific model SDK declared
[2025-08-29T17:14:43.172+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_****************************************************0a0',
  openaiExtraConfig: undefined,
  modelName: 'deepseek-v3',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-08-29T17:14:43.172+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-08-29T17:14:43.172+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-08-29T17:14:43.172+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-08-29T17:14:43.172+08:00] provider has no specific model SDK declared
[2025-08-29T17:14:43.172+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_****************************************************0a0',
  openaiExtraConfig: undefined,
  modelName: 'deepseek-v3',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-08-29T17:14:43.172+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-08-29T17:14:43.172+08:00] decideModelConfig as legacy logic with intent planning.
[2025-08-29T17:14:43.172+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-08-29T17:14:43.172+08:00] provider has no specific model SDK declared
[2025-08-29T17:14:43.172+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_****************************************************0a0',
  openaiExtraConfig: undefined,
  modelName: 'deepseek-v3',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-08-29T17:16:14.762+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-08-29T17:16:14.762+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-08-29T17:16:14.763+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-08-29T17:16:14.763+08:00] provider has no specific model SDK declared
[2025-08-29T17:16:14.764+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_****************************************************0a0',
  openaiExtraConfig: undefined,
  modelName: 'deepseek-v3',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-08-29T17:16:14.764+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig deepseek-v3
[2025-08-29T17:16:14.764+08:00] decideModelConfig as legacy logic with intent default.
[2025-08-29T17:16:14.764+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-08-29T17:16:14.764+08:00] provider has no specific model SDK declared
[2025-08-29T17:16:14.764+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_****************************************************0a0',
  openaiExtraConfig: undefined,
  modelName: 'deepseek-v3',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-08-29T17:16:14.764+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-08-29T17:16:14.764+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-08-29T17:16:14.765+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-08-29T17:16:14.765+08:00] provider has no specific model SDK declared
[2025-08-29T17:16:14.765+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_****************************************************0a0',
  openaiExtraConfig: undefined,
  modelName: 'deepseek-v3',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
[2025-08-29T17:16:14.765+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-08-29T17:16:14.765+08:00] decideModelConfig as legacy logic with intent planning.
[2025-08-29T17:16:14.765+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-08-29T17:16:14.765+08:00] provider has no specific model SDK declared
[2025-08-29T17:16:14.765+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://hk-intra-paas.transsion.com/tranai-proxy/v1',
  openaiApiKey: 'sk_****************************************************0a0',
  openaiExtraConfig: undefined,
  modelName: 'deepseek-v3',
  vlMode: 'qwen-vl',
  uiTarsVersion: undefined,
  modelDescription: 'qwen-vl mode',
  from: 'legacy-env'
}
