"""
单页面元素AI语义增强主流程
整合页面分析器、多模态智能体和数据库管理器，实现完整的单页面元素语义增强流程
"""
import asyncio
import time
from typing import Optional, Dict, Any, Tuple
from datetime import datetime
from pathlib import Path
from loguru import logger

from backend.page_analyzer import PageAnalyzer
from backend.multimodal_semantic_agent import MultiModalUISemanticAgent
from backend.database import DatabaseManager
from backend.models.page_model import UIPage, PageAnalysisResult
from backend.models.app_model import AppInfo, AnalysisSession, AnalysisStatus
from backend.config import config


class PageSemanticEnhancer:
    """单页面元素AI语义增强器"""

    def __init__(self, device_id: Optional[str] = None):
        """
        初始化页面语义增强器

        Args:
            device_id: 设备ID，为None时使用默认设备
        """
        self.device_id = device_id or config.device.device_id

        # 初始化组件
        self.page_analyzer = PageAnalyzer(device_id)
        self.semantic_agent = MultiModalUISemanticAgent()
        self.db_manager = DatabaseManager()

        # 配置参数
        self.enable_ai_enhancement = config.crawler.enable_ai_enhancement
        self.enable_screenshots = config.crawler.screenshot_enabled
        self.save_to_database = True

        logger.info(f"页面语义增强器初始化完成 - 设备: {self.device_id}")

    def initialize(self) -> bool:
        """
        初始化所有组件

        Returns:
            bool: 初始化是否成功
        """
        try:
            logger.info("初始化页面语义增强器组件...")

            # 1. 连接设备
            if not self.page_analyzer.connect_device():
                logger.error("页面分析器设备连接失败")
                return False

            # 2. 初始化数据库
            if self.save_to_database:
                if not self.db_manager.initialize_database():
                    logger.error("数据库初始化失败")
                    return False

            logger.info("页面语义增强器组件初始化成功")
            return True

        except Exception as e:
            logger.error(f"页面语义增强器初始化失败: {e}")
            return False

    async def enhance_current_page(self,
                                 app_info: Optional[AppInfo] = None,
                                 session_id: Optional[str] = None) -> Dict[str, Any]:
        """
        增强当前页面的元素语义信息

        Args:
            app_info: 应用信息，为None时自动获取
            session_id: 分析会话ID，为None时自动生成

        Returns:
            Dict[str, Any]: 增强结果
        """
        start_time = time.time()

        try:
            logger.info("开始单页面元素语义增强")

            # 1. 分析当前页面
            logger.info("步骤1: 分析当前页面")
            page_result = self.page_analyzer.analyze_current_page(
                capture_screenshot=self.enable_screenshots,
                save_xml=True,
                wait_stable=True
            )

            if not page_result.success:
                logger.error(f"页面分析失败: {page_result.error_message}")
                return self._create_error_result("页面分析失败", page_result.error_message, start_time)

            page = page_result.page
            logger.info(f"页面分析成功: {page.page_name} (元素数量: {len(page.elements)})")

            # 2. AI语义增强
            enhanced_count = 0
            total_elements = len(page.elements)

            if self.enable_ai_enhancement and page.screenshot_path:
                logger.info("步骤2: AI语义增强")
                try:
                    enhanced_count, total_elements = await self.semantic_agent.enhance_page_elements_with_screenshot(
                        page, page.screenshot_path
                    )
                    logger.info(f"AI语义增强完成: {enhanced_count}/{total_elements} 个元素")
                except Exception as e:
                    logger.warning(f"AI语义增强失败，跳过: {e}")
            else:
                logger.info("跳过AI语义增强（未启用或无截图）")

            # 3. 保存到数据库（包含AI增强信息）
            saved_to_db = False
            if self.save_to_database:
                logger.info("步骤3: 保存到数据库（包含AI增强信息）")
                try:
                    # 确保页面对象包含最新的AI增强信息
                    self._update_page_with_enhanced_elements(page)

                    saved_to_db = await self._save_page_to_database(page, app_info, session_id)
                    if saved_to_db:
                        logger.info(f"页面数据保存到数据库成功 - 包含{enhanced_count}个AI增强元素")
                    else:
                        logger.warning("页面数据保存到数据库失败")
                except Exception as e:
                    logger.error(f"保存到数据库失败: {e}")
            else:
                logger.info("跳过数据库保存（未启用）")

            # 4. 生成结果
            duration = time.time() - start_time

            result = {
                "success": True,
                "page_id": page.page_id,
                "page_name": page.page_name,
                "activity_name": page.activity_name,
                "package_name": page.package_name,
                "total_elements": total_elements,
                "actionable_elements": len([e for e in page.elements if e.is_actionable()]),
                "enhanced_elements": enhanced_count,
                "ai_enhancement_enabled": self.enable_ai_enhancement,
                "screenshot_captured": bool(page.screenshot_path),
                "screenshot_path": page.screenshot_path,
                "xml_saved": bool(page.xml_source_path),
                "xml_path": page.xml_source_path,
                "saved_to_database": saved_to_db,
                "processing_duration": duration,
                "timestamp": datetime.now().isoformat()
            }

            logger.info(f"单页面元素语义增强完成 - 耗时: {duration:.2f}秒")
            return result

        except Exception as e:
            logger.error(f"单页面元素语义增强失败: {e}")
            return self._create_error_result("处理失败", str(e), start_time)

    async def _save_page_to_database(self,
                                   page: UIPage,
                                   app_info: Optional[AppInfo] = None,
                                   session_id: Optional[str] = None) -> bool:
        """
        保存页面到数据库

        Args:
            page: 页面对象
            app_info: 应用信息
            session_id: 会话ID

        Returns:
            bool: 保存是否成功
        """
        try:
            # 获取或创建应用信息
            if not app_info:
                app_info = self._get_current_app_info(page.package_name)

            # 保存应用信息
            app_id = self.db_manager.save_app_info(app_info)
            if not app_id:
                logger.error("保存应用信息失败")
                return False

            # 创建分析会话
            if not session_id:
                session_id = f"single_page_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            analysis_session = AnalysisSession(
                session_id=session_id,
                app_info=app_info,
                device_id=self.device_id,
                enable_ai_enhancement=self.enable_ai_enhancement,
                enable_screenshots=self.enable_screenshots,
                status=AnalysisStatus.COMPLETED,
                start_time=datetime.now(),
                end_time=datetime.now()
            )

            # 添加页面到会话
            analysis_session.add_page(page)

            # 保存会话
            session_db_id = self.db_manager.save_analysis_session(analysis_session, app_id)
            if not session_db_id:
                logger.error("保存分析会话失败")
                return False

            # 保存页面
            page_db_id = self.db_manager.save_page(page, session_db_id, app_id)
            if not page_db_id:
                logger.error("保存页面失败")
                return False

            # 保存元素
            if page.elements:
                saved_elements = self.db_manager.save_elements(page.elements, page_db_id)
                logger.info(f"保存了 {saved_elements} 个元素到数据库")

            return True

        except Exception as e:
            logger.error(f"保存页面到数据库失败: {e}")
            return False

    def _get_current_app_info(self, package_name: str) -> AppInfo:
        """
        获取当前应用信息

        Args:
            package_name: 包名

        Returns:
            AppInfo: 应用信息对象
        """
        try:
            # 尝试从应用控制器获取应用信息
            app_info_dict = self.page_analyzer.app_controller.get_app_info(package_name)

            if app_info_dict:
                return AppInfo(
                    package_name=package_name,
                    app_name=app_info_dict.get('app_name', ''),
                    version_name=app_info_dict.get('version_name', ''),
                    version_code=app_info_dict.get('version_code', ''),
                    main_activity=app_info_dict.get('main_activity', ''),
                    target_sdk=app_info_dict.get('target_sdk'),
                    min_sdk=app_info_dict.get('min_sdk'),
                    install_time=app_info_dict.get('install_time'),
                    last_update_time=app_info_dict.get('last_update_time'),
                    app_size=app_info_dict.get('app_size'),
                    permissions=app_info_dict.get('permissions', [])
                )
            else:
                # 创建基本应用信息
                return AppInfo(
                    package_name=package_name,
                    app_name=package_name.split('.')[-1] if package_name else 'Unknown App'
                )

        except Exception as e:
            logger.warning(f"获取应用信息失败: {e}")
            return AppInfo(
                package_name=package_name,
                app_name=package_name.split('.')[-1] if package_name else 'Unknown App'
            )

    def _create_error_result(self, error_type: str, error_message: str, start_time: float) -> Dict[str, Any]:
        """
        创建错误结果

        Args:
            error_type: 错误类型
            error_message: 错误消息
            start_time: 开始时间

        Returns:
            Dict[str, Any]: 错误结果
        """
        duration = time.time() - start_time

        return {
            "success": False,
            "error_type": error_type,
            "error_message": error_message,
            "processing_duration": duration,
            "timestamp": datetime.now().isoformat()
        }

    def get_enhancement_statistics(self) -> Dict[str, Any]:
        """
        获取增强统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            stats = {
                "device_id": self.device_id,
                "ai_enhancement_enabled": self.enable_ai_enhancement,
                "screenshots_enabled": self.enable_screenshots,
                "database_enabled": self.save_to_database,
                "semantic_agent_available": self.semantic_agent.agent is not None,
                "database_initialized": self.db_manager.engine is not None,
                "page_analyzer_connected": self.page_analyzer.device is not None
            }

            return stats

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {"error": str(e)}

    async def batch_enhance_elements(self,
                                   elements: list,
                                   screenshot_path: str,
                                   page_context: Dict[str, Any] = None) -> Tuple[int, int]:
        """
        批量增强元素语义信息

        Args:
            elements: 元素列表
            screenshot_path: 截图路径
            page_context: 页面上下文

        Returns:
            Tuple[int, int]: (成功增强数量, 总数量)
        """
        try:
            if not self.enable_ai_enhancement:
                logger.info("AI增强未启用，跳过批量增强")
                return 0, len(elements)

            enhanced_count = 0

            for i, element in enumerate(elements):
                try:
                    if element.is_actionable():
                        logger.debug(f"批量增强元素 {i+1}/{len(elements)}: {element.element_type}")

                        semantic_result = await self.semantic_agent.analyze_element_with_screenshot(
                            element, screenshot_path, page_context
                        )

                        # 更新元素信息
                        self.semantic_agent._update_element_with_semantic_result(element, semantic_result)
                        enhanced_count += 1

                except Exception as e:
                    logger.warning(f"批量增强元素失败: {e}")
                    continue

            logger.info(f"批量增强完成: {enhanced_count}/{len(elements)} 个元素")
            return enhanced_count, len(elements)

        except Exception as e:
            logger.error(f"批量增强失败: {e}")
            return 0, len(elements)

    def _update_page_with_enhanced_elements(self, page: UIPage):
        """
        更新页面对象，确保包含最新的AI增强信息

        Args:
            page: UI页面对象
        """
        try:
            enhanced_count = 0
            for element in page.elements:
                if element.semantic_description or element.expected_action or element.element_category:
                    enhanced_count += 1
                    # 确保元素的更新时间是最新的
                    if not element.updated_at:
                        element.updated_at = datetime.now()

            logger.debug(f"页面包含 {enhanced_count} 个AI增强元素")

        except Exception as e:
            logger.error(f"更新页面AI增强信息失败: {e}")


# 便捷函数
async def enhance_current_page_simple(device_id: Optional[str] = None) -> Dict[str, Any]:
    """
    简化的当前页面增强函数

    Args:
        device_id: 设备ID

    Returns:
        Dict[str, Any]: 增强结果
    """
    enhancer = PageSemanticEnhancer(device_id)

    if not enhancer.initialize():
        return {
            "success": False,
            "error_type": "初始化失败",
            "error_message": "页面语义增强器初始化失败"
        }

    return await enhancer.enhance_current_page()


if __name__ == '__main__':
    # 测试代码
    async def run_enhance():
        result = await enhance_current_page_simple("ASALE3741B000022")
        print("增强结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))

    import json
    asyncio.run(run_enhance())