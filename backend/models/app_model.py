"""
应用信息和分析会话数据模型
"""
from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List, Set
from datetime import datetime
from enum import Enum
from .page_model import UIPage, PageAnalysisResult


class AnalysisStatus(Enum):
    """分析状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class AppInfo:
    """应用信息模型"""
    package_name: str
    app_name: str = ""
    version_name: str = ""
    version_code: str = ""
    main_activity: str = ""
    target_sdk: int = 0
    min_sdk: int = 0
    
    # 应用元数据
    install_time: Optional[datetime] = None
    last_update_time: Optional[datetime] = None
    app_size: int = 0  # 应用大小（字节）
    permissions: List[str] = field(default_factory=list)
    
    # 分析统计
    total_analysis_count: int = 0
    last_analysis_time: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'package_name': self.package_name,
            'app_name': self.app_name,
            'version_name': self.version_name,
            'version_code': self.version_code,
            'main_activity': self.main_activity,
            'target_sdk': self.target_sdk,
            'min_sdk': self.min_sdk,
            'install_time': self.install_time.isoformat() if self.install_time else None,
            'last_update_time': self.last_update_time.isoformat() if self.last_update_time else None,
            'app_size': self.app_size,
            'permissions': self.permissions,
            'total_analysis_count': self.total_analysis_count,
            'last_analysis_time': self.last_analysis_time.isoformat() if self.last_analysis_time else None
        }


@dataclass
class AnalysisSession:
    """分析会话模型"""
    session_id: str
    app_info: AppInfo
    device_id: str = ""
    
    # 会话配置
    max_depth: int = 20
    max_pages: int = 100
    enable_ai_enhancement: bool = True
    enable_screenshots: bool = True
    
    # 会话状态
    status: AnalysisStatus = AnalysisStatus.PENDING
    current_page_id: str = ""
    pages_discovered: Set[str] = field(default_factory=set)
    pages_analyzed: Set[str] = field(default_factory=set)
    pages_failed: Set[str] = field(default_factory=set)
    
    # 分析结果
    pages: Dict[str, UIPage] = field(default_factory=dict)
    analysis_results: List[PageAnalysisResult] = field(default_factory=list)
    
    # 统计信息
    total_elements_found: int = 0
    total_actionable_elements: int = 0
    total_navigations: int = 0
    ai_enhanced_elements: int = 0
    
    # 时间信息
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    created_at: datetime = field(default_factory=datetime.now)
    
    # 错误信息
    error_message: str = ""
    error_details: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.session_id:
            self.session_id = self.generate_session_id()
    
    def generate_session_id(self) -> str:
        """生成会话ID"""
        import hashlib
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        key_data = f"{self.app_info.package_name}_{self.device_id}_{timestamp}"
        hash_suffix = hashlib.md5(key_data.encode()).hexdigest()[:8]
        return f"session_{timestamp}_{hash_suffix}"
    
    def start_analysis(self):
        """开始分析"""
        self.status = AnalysisStatus.RUNNING
        self.start_time = datetime.now()
        self.error_message = ""
        self.error_details = {}
    
    def complete_analysis(self):
        """完成分析"""
        self.status = AnalysisStatus.COMPLETED
        self.end_time = datetime.now()
        self.update_statistics()
    
    def fail_analysis(self, error_message: str, error_details: Dict[str, Any] = None):
        """分析失败"""
        self.status = AnalysisStatus.FAILED
        self.end_time = datetime.now()
        self.error_message = error_message
        self.error_details = error_details or {}
    
    def cancel_analysis(self):
        """取消分析"""
        self.status = AnalysisStatus.CANCELLED
        self.end_time = datetime.now()
    
    def add_page(self, page: UIPage):
        """添加页面"""
        self.pages[page.page_id] = page
        self.pages_discovered.add(page.page_id)
        if page.analysis_status == "completed":
            self.pages_analyzed.add(page.page_id)
        elif page.analysis_status == "failed":
            self.pages_failed.add(page.page_id)
    
    def add_analysis_result(self, result: PageAnalysisResult):
        """添加分析结果"""
        self.analysis_results.append(result)
        if result.success:
            self.pages_analyzed.add(result.page.page_id)
        else:
            self.pages_failed.add(result.page.page_id)
    
    def get_page(self, page_id: str) -> Optional[UIPage]:
        """获取页面"""
        return self.pages.get(page_id)
    
    def get_unanalyzed_pages(self) -> List[str]:
        """获取未分析的页面ID列表"""
        return list(self.pages_discovered - self.pages_analyzed - self.pages_failed)
    
    def update_statistics(self):
        """更新统计信息"""
        self.total_elements_found = sum(page.element_count for page in self.pages.values())
        self.total_actionable_elements = sum(page.actionable_element_count for page in self.pages.values())
        
        # 统计导航关系
        self.total_navigations = sum(len(page.navigations) for page in self.pages.values())
        
        # 统计AI增强元素
        self.ai_enhanced_elements = sum(
            sum(1 for elem in page.elements if elem.semantic_description)
            for page in self.pages.values()
        )
    
    def get_analysis_duration(self) -> float:
        """获取分析耗时（秒）"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        elif self.start_time:
            return (datetime.now() - self.start_time).total_seconds()
        return 0.0
    
    def get_progress_percentage(self) -> float:
        """获取分析进度百分比"""
        if not self.pages_discovered:
            return 0.0
        
        completed = len(self.pages_analyzed)
        total = len(self.pages_discovered)
        return (completed / total) * 100.0
    
    def get_success_rate(self) -> float:
        """获取分析成功率"""
        if not self.pages_discovered:
            return 0.0
        
        successful = len(self.pages_analyzed)
        total = len(self.pages_analyzed) + len(self.pages_failed)
        if total == 0:
            return 0.0
        return (successful / total) * 100.0
    
    def generate_page_graph(self) -> Dict[str, Any]:
        """生成页面关系图谱"""
        nodes = []
        edges = []
        
        for page in self.pages.values():
            nodes.append({
                'id': page.page_id,
                'label': page.page_name or page.activity_name,
                'type': 'page',
                'element_count': page.element_count,
                'actionable_count': page.actionable_element_count,
                'visit_count': page.visit_count
            })
            
            for navigation in page.navigations:
                edges.append({
                    'source': navigation.source_page_id,
                    'target': navigation.target_page_id,
                    'action': navigation.action_type,
                    'element_id': navigation.via_element_id,
                    'success_rate': navigation.success_rate
                })
        
        return {
            'nodes': nodes,
            'edges': edges,
            'statistics': {
                'total_pages': len(nodes),
                'total_navigations': len(edges),
                'max_depth': self.max_depth,
                'analysis_duration': self.get_analysis_duration()
            }
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'session_id': self.session_id,
            'app_info': self.app_info.to_dict(),
            'device_id': self.device_id,
            'max_depth': self.max_depth,
            'max_pages': self.max_pages,
            'enable_ai_enhancement': self.enable_ai_enhancement,
            'enable_screenshots': self.enable_screenshots,
            'status': self.status.value,
            'current_page_id': self.current_page_id,
            'pages_discovered': list(self.pages_discovered),
            'pages_analyzed': list(self.pages_analyzed),
            'pages_failed': list(self.pages_failed),
            'total_elements_found': self.total_elements_found,
            'total_actionable_elements': self.total_actionable_elements,
            'total_navigations': self.total_navigations,
            'ai_enhanced_elements': self.ai_enhanced_elements,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'created_at': self.created_at.isoformat(),
            'error_message': self.error_message,
            'error_details': self.error_details,
            'analysis_duration': self.get_analysis_duration(),
            'progress_percentage': self.get_progress_percentage(),
            'success_rate': self.get_success_rate()
        }
    
    def to_summary_report(self) -> Dict[str, Any]:
        """生成分析摘要报告"""
        return {
            'session_info': {
                'session_id': self.session_id,
                'app_name': self.app_info.app_name,
                'package_name': self.app_info.package_name,
                'device_id': self.device_id,
                'analysis_duration': self.get_analysis_duration(),
                'status': self.status.value
            },
            'coverage_statistics': {
                'total_pages_discovered': len(self.pages_discovered),
                'pages_successfully_analyzed': len(self.pages_analyzed),
                'pages_failed': len(self.pages_failed),
                'success_rate': self.get_success_rate(),
                'progress_percentage': self.get_progress_percentage()
            },
            'element_statistics': {
                'total_elements': self.total_elements_found,
                'actionable_elements': self.total_actionable_elements,
                'ai_enhanced_elements': self.ai_enhanced_elements,
                'ai_enhancement_rate': (self.ai_enhanced_elements / max(self.total_actionable_elements, 1)) * 100
            },
            'navigation_statistics': {
                'total_navigations': self.total_navigations,
                'avg_navigations_per_page': self.total_navigations / max(len(self.pages), 1)
            },
            'error_summary': {
                'has_errors': bool(self.error_message or self.pages_failed),
                'error_message': self.error_message,
                'failed_pages_count': len(self.pages_failed)
            }
        }
