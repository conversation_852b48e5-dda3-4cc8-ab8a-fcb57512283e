"""
单页面分析器
实现单个页面的完整分析流程，包括截图、元素提取、坐标解析等
"""
import asyncio
import os
import time
import hashlib
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime
from pathlib import Path
from loguru import logger

try:
    import uiautomator2 as u2
except ImportError as e:
    logger.error(f"导入uiautomator2失败: {e}")
    raise

from backend.config import config
from backend.models.page_model import UIPage, PageAnalysisResult
from backend.models.element_model import UIElement
from backend.element_extractor import UIElementExtractor
from backend.app_controller import AndroidAppController


class PageAnalyzer:
    """单页面分析器"""
    
    def __init__(self, device_id: Optional[str] = None):
        """
        初始化页面分析器
        
        Args:
            device_id: 设备ID，为None时使用默认设备
        """
        self.device_id = device_id or config.device.device_id
        self.device = None
        
        # 初始化组件
        self.element_extractor = UIElementExtractor(device_id)
        self.app_controller = AndroidAppController(device_id)
        
        # 配置路径
        self.screenshots_dir = config.paths.screenshots_dir
        self.xml_files_dir = config.paths.xml_files_dir
        
        # 页面识别规则
        self.page_rules = config.get_page_identification_rules()
        
    def connect_device(self) -> bool:
        """
        连接设备
        
        Returns:
            bool: 连接是否成功
        """
        try:
            logger.info(f"页面分析器连接设备: {self.device_id or 'default'}")
            
            # 连接元素提取器
            if not self.element_extractor.connect_device():
                return False
            
            # 获取设备引用
            self.device = self.element_extractor.device
            
            logger.info("页面分析器设备连接成功")
            return True
            
        except Exception as e:
            logger.error(f"页面分析器连接设备失败: {e}")
            return False
    
    def capture_screenshot(self, filename: Optional[str] = None) -> Optional[str]:
        """
        截取当前页面截图
        
        Args:
            filename: 文件名，为None时自动生成
            
        Returns:
            Optional[str]: 截图文件路径，失败时返回None
        """
        try:
            if not self.device:
                logger.error("设备未连接")
                return None
            
            # 生成文件名
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
                filename = f"screenshot_{timestamp}.png"
            
            # 确保文件名以.png结尾
            if not filename.endswith('.png'):
                filename += '.png'
            
            screenshot_path = self.screenshots_dir / filename
            
            logger.debug(f"正在截取屏幕截图: {screenshot_path}")
            
            # 截图
            self.device.screenshot(str(screenshot_path))
            
            if screenshot_path.exists():
                logger.info(f"截图保存成功: {screenshot_path}")
                return str(screenshot_path)
            else:
                logger.error("截图文件未生成")
                return None
                
        except Exception as e:
            logger.error(f"截取屏幕截图失败: {e}")
            return None
    
    def save_page_xml(self, xml_content: str, filename: Optional[str] = None) -> Optional[str]:
        """
        保存页面XML源码
        
        Args:
            xml_content: XML内容
            filename: 文件名，为None时自动生成
            
        Returns:
            Optional[str]: XML文件路径，失败时返回None
        """
        try:
            # 生成文件名
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
                filename = f"page_source_{timestamp}.xml"
            
            # 确保文件名以.xml结尾
            if not filename.endswith('.xml'):
                filename += '.xml'
            
            xml_path = self.xml_files_dir / filename
            
            logger.debug(f"正在保存页面XML: {xml_path}")
            
            # 保存XML
            with open(xml_path, 'w', encoding='utf-8') as f:
                f.write(xml_content)
            
            logger.info(f"页面XML保存成功: {xml_path}")
            return str(xml_path)
            
        except Exception as e:
            logger.error(f"保存页面XML失败: {e}")
            return None
    
    def get_current_activity_info(self) -> Dict[str, str]:
        """
        获取当前Activity信息
        
        Returns:
            Dict[str, str]: Activity信息
        """
        try:
            activity_info = {
                'activity_name': '',
                'package_name': '',
                'full_activity': ''
            }
            
            # 获取当前Activity
            current_activity = self.app_controller.get_current_activity()
            if current_activity:
                activity_info['full_activity'] = current_activity
                
                # 解析包名和Activity名
                if '/' in current_activity:
                    parts = current_activity.split('/')
                    activity_info['package_name'] = parts[0]
                    activity_info['activity_name'] = parts[1]
            
            return activity_info
            
        except Exception as e:
            logger.warning(f"获取Activity信息失败: {e}")
            return {'activity_name': '', 'package_name': '', 'full_activity': ''}
    
    def generate_page_id(self, activity_info: Dict[str, str], elements: List[UIElement]) -> str:
        """
        生成页面唯一ID
        
        Args:
            activity_info: Activity信息
            elements: 页面元素列表
            
        Returns:
            str: 页面ID
        """
        try:
            # 基于Activity名称和关键元素生成ID
            key_data = f"{activity_info.get('full_activity', '')}"
            
            # 添加关键元素信息
            key_elements = []
            for element in elements[:10]:  # 只取前10个元素
                if element.is_actionable():
                    key_elements.append(f"{element.element_type}_{element.resource_id}_{element.text}")
            
            if key_elements:
                key_data += "_" + "|".join(key_elements)
            
            # 生成哈希ID
            page_id = hashlib.md5(key_data.encode()).hexdigest()[:16]
            
            logger.debug(f"生成页面ID: {page_id}")
            return page_id
            
        except Exception as e:
            logger.warning(f"生成页面ID失败: {e}")
            # 使用时间戳作为备用ID
            return datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
    
    def generate_page_name(self, activity_info: Dict[str, str], elements: List[UIElement]) -> str:
        """
        生成页面描述性名称
        
        Args:
            activity_info: Activity信息
            elements: 页面元素列表
            
        Returns:
            str: 页面名称
        """
        try:
            # 优先使用Activity名称
            if activity_info.get('activity_name'):
                activity_name = activity_info['activity_name']
                # 清理Activity名称
                if activity_name.startswith('.'):
                    activity_name = activity_name[1:]
                
                # 转换为可读名称
                page_name = activity_name.replace('Activity', '').replace('_', ' ').title()
                if page_name:
                    return page_name
            
            # 尝试从页面元素推断名称
            title_elements = []
            for element in elements:
                if (element.element_type in ['TextView', 'Button'] and 
                    element.text and len(element.text) < 20):
                    title_elements.append(element.text)
            
            if title_elements:
                return title_elements[0]
            
            # 使用包名作为备用
            if activity_info.get('package_name'):
                package_name = activity_info['package_name']
                return package_name.split('.')[-1].title() + " Page"
            
            return "Unknown Page"
            
        except Exception as e:
            logger.warning(f"生成页面名称失败: {e}")
            return "Unknown Page"
    
    def wait_for_page_stable(self, timeout: int = 10) -> bool:
        """
        等待页面稳定
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            bool: 页面是否稳定
        """
        try:
            logger.debug("等待页面稳定...")
            
            stable_count = self.page_rules.get("stable_check_count", 2)
            check_interval = self.page_rules.get("stable_check_interval", 1.0)
            
            start_time = time.time()
            last_xml = ""
            consecutive_stable = 0
            
            while time.time() - start_time < timeout:
                # 获取当前页面XML
                current_xml = self.element_extractor.get_page_source()
                if not current_xml:
                    time.sleep(check_interval)
                    continue
                
                # 比较XML内容
                if current_xml == last_xml:
                    consecutive_stable += 1
                    if consecutive_stable >= stable_count:
                        logger.info("页面已稳定")
                        return True
                else:
                    consecutive_stable = 0
                
                last_xml = current_xml
                time.sleep(check_interval)
            
            logger.warning("页面稳定性检查超时")
            return False
            
        except Exception as e:
            logger.error(f"等待页面稳定失败: {e}")
            return False
    
    def analyze_current_page(self, 
                           capture_screenshot: bool = True,
                           save_xml: bool = True,
                           wait_stable: bool = True) -> PageAnalysisResult:
        """
        分析当前页面
        
        Args:
            capture_screenshot: 是否截图
            save_xml: 是否保存XML
            wait_stable: 是否等待页面稳定
            
        Returns:
            PageAnalysisResult: 页面分析结果
        """
        start_time = time.time()
        
        try:
            logger.info("开始分析当前页面")
            
            # 等待页面稳定
            if wait_stable:
                if not self.wait_for_page_stable():
                    logger.warning("页面未稳定，继续分析")
            
            # 获取Activity信息
            activity_info = self.get_current_activity_info()
            logger.info(f"当前Activity: {activity_info.get('full_activity', 'Unknown')}")
            
            # 提取页面元素
            elements = self.element_extractor.extract_current_page_elements()
            if not elements:
                logger.warning("未提取到页面元素")
            
            # 生成页面ID和名称
            page_id = self.generate_page_id(activity_info, elements)
            page_name = self.generate_page_name(activity_info, elements)
            
            # 创建页面对象
            page = UIPage(
                page_id=page_id,
                page_name=page_name,
                activity_name=activity_info.get('activity_name', ''),
                package_name=activity_info.get('package_name', ''),
                elements=elements
            )
            
            # 计算内容哈希
            page.calculate_content_hash()
            
            # 截图
            screenshot_captured = False
            if capture_screenshot:
                screenshot_path = self.capture_screenshot(f"page_{page_id}")
                if screenshot_path:
                    page.screenshot_path = screenshot_path
                    screenshot_captured = True
            
            # 保存XML
            xml_saved = False
            if save_xml:
                xml_content = self.element_extractor.get_page_source()
                if xml_content:
                    xml_path = self.save_page_xml(xml_content, f"page_{page_id}")
                    if xml_path:
                        page.xml_source_path = xml_path
                        xml_saved = True
            
            # 更新页面状态
            page.analysis_status = "completed"
            page.mark_visited()
            
            # 计算分析耗时
            analysis_duration = time.time() - start_time
            
            # 创建分析结果
            result = PageAnalysisResult(
                page=page,
                success=True,
                analysis_duration=analysis_duration,
                screenshot_captured=screenshot_captured,
                xml_saved=xml_saved,
                total_elements_found=len(elements),
                actionable_elements_found=len([e for e in elements if e.is_actionable()])
            )
            
            logger.info(f"页面分析完成: {page_name} (ID: {page_id})")
            logger.info(f"分析耗时: {analysis_duration:.2f}秒")
            logger.info(f"元素统计: 总计{len(elements)}个，可操作{result.actionable_elements_found}个")
            
            return result
            
        except Exception as e:
            logger.error(f"页面分析失败: {e}")
            
            # 创建失败结果
            analysis_duration = time.time() - start_time
            
            # 创建基本页面对象
            page = UIPage(
                page_id=f"failed_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                page_name="Analysis Failed",
                analysis_status="failed",
                analysis_error=str(e)
            )
            
            result = PageAnalysisResult(
                page=page,
                success=False,
                error_message=str(e),
                analysis_duration=analysis_duration
            )
            
            return result


if __name__ == '__main__':
    analyzer = PageAnalyzer(device_id="13764254B4001229")
    analyzer.connect_device()
    result = analyzer.analyze_current_page()

    print(result.to_dict())

