"""
脚本生成工具
提供脚本文件生成、版本管理、目录结构管理等功能
"""
import os
import sys
import json
import shutil
from typing import Dict, Any, Optional, List
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加相对路径导入
sys.path.append(str(Path(__file__).parent.parent))

from config import config
from utils.database_manager import DatabaseManager
from models.script_models import GeneratedScript, ScriptVersion


class ScriptGenerationTool:
    """脚本生成工具"""
    
    def __init__(self):
        """初始化脚本生成工具"""
        self.config = config
        self.db_manager = DatabaseManager()
        
        # 脚本模板
        self.templates = self._load_templates()
        
        # 输出目录
        self.output_dirs = {
            'scripts': Path(config.data_paths.scripts_dir),
            'projects': Path(config.data_paths.projects_dir),
            'templates': Path(config.data_paths.templates_dir)
        }
        
        # 确保目录存在
        self._ensure_directories()
        
        logger.info("脚本生成工具初始化完成")
    
    def _ensure_directories(self):
        """确保输出目录存在"""
        try:
            for dir_type, dir_path in self.output_dirs.items():
                dir_path.mkdir(parents=True, exist_ok=True)
                logger.debug(f"确保目录存在: {dir_path}")
        except Exception as e:
            logger.error(f"创建输出目录失败: {e}")
    
    def _load_templates(self) -> Dict[str, str]:
        """加载脚本模板"""
        try:
            templates = {
                'basic': self._get_basic_template(),
                'advanced': self._get_advanced_template(),
                'batch': self._get_batch_template()
            }
            return templates
        except Exception as e:
            logger.error(f"加载脚本模板失败: {e}")
            return {}
    
    def _get_basic_template(self) -> str:
        """获取基础脚本模板"""
        return """/**
 * 基础自动化测试脚本
 * 生成时间: {generation_time}
 * 脚本名称: {script_name}
 * 应用包名: {package_name}
 */

import {{ AndroidAgent, AndroidDevice, getConnectedDevices }} from '@midscene/android';
import "dotenv/config";

const sleep = (ms: number) => new Promise((r) => setTimeout(r, ms));

async function main() {{
  let device: AndroidDevice | null = null;
  let agent: AndroidAgent | null = null;

  try {{
    console.log('开始执行自动化测试: {script_name}');

    // 获取连接的设备
    const devices = await getConnectedDevices();
    if (devices.length === 0) {{
      throw new Error('未找到连接的Android设备');
    }}

    // 创建设备实例
    device = new AndroidDevice(devices[0].udid);
    console.log(`连接到设备: ${{devices[0].udid}}`);

    // 连接设备
    await device.connect();

    // 创建AI智能体
    agent = new AndroidAgent(device, {{
      aiActionContext: 'If any location, permission, user agreement, etc. popup, click agree. If login page pops up, close it.',
    }});

    // 启动应用
    await device.launch('{package_name}');
    await sleep(3000);

    {test_steps}

    {assertions}

    console.log('测试执行完成');

  }} catch (error) {{
    console.error('测试执行失败:', error);
    throw error;
  }} finally {{
    // 清理资源
    if (device) {{
      try {{
        await device.disconnect();
      }} catch (e) {{
        console.warn('设备断开连接失败:', e);
      }}
    }}
  }}
}}

// 运行测试
main().catch(console.error);
"""
    
    def _get_advanced_template(self) -> str:
        """获取高级脚本模板"""
        return """/**
 * 高级自动化测试脚本
 * 生成时间: {generation_time}
 * 脚本名称: {script_name}
 * 应用包名: {package_name}
 */

import {{ AndroidAgent, AndroidDevice, getConnectedDevices }} from '@midscene/android';
import {{ writeFileSync }} from 'fs';
import {{ join }} from 'path';
import "dotenv/config";

interface TestResult {{
  testName: string;
  status: 'pass' | 'fail';
  duration: number;
  error?: string;
  screenshots?: string[];
}}

class TestRunner {{
  private device: AndroidDevice | null = null;
  private agent: AndroidAgent | null = null;
  private results: TestResult[] = [];
  private startTime: number = 0;

  async setup(): Promise<void> {{
    console.log('设置测试环境...');
    
    const devices = await getConnectedDevices();
    if (devices.length === 0) {{
      throw new Error('未找到连接的Android设备');
    }}

    this.device = new AndroidDevice(devices[0].udid);
    await this.device.connect();

    this.agent = new AndroidAgent(this.device, {{
      aiActionContext: 'If any location, permission, user agreement, etc. popup, click agree. If login page pops up, close it.',
    }});

    await this.device.launch('{package_name}');
    await this.sleep(3000);
  }}

  async teardown(): Promise<void> {{
    console.log('清理测试环境...');
    
    if (this.device) {{
      try {{
        await this.device.disconnect();
      }} catch (e) {{
        console.warn('设备断开连接失败:', e);
      }}
    }}
  }}

  private sleep(ms: number): Promise<void> {{
    return new Promise((resolve) => setTimeout(resolve, ms));
  }}

  private async takeScreenshot(name: string): Promise<string> {{
    if (!this.device) throw new Error('设备未连接');
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${{name}}_${{timestamp}}.png`;
    const filepath = join(process.cwd(), 'screenshots', filename);
    
    // 这里需要实现截图逻辑
    // await this.device.screenshot(filepath);
    
    return filepath;
  }}

  async runTest(testName: string, testFunction: () => Promise<void>): Promise<TestResult> {{
    const startTime = Date.now();
    console.log(`开始执行测试: ${{testName}}`);

    try {{
      await testFunction();
      
      const duration = Date.now() - startTime;
      const result: TestResult = {{
        testName,
        status: 'pass',
        duration
      }};
      
      this.results.push(result);
      console.log(`测试通过: ${{testName}} (耗时: ${{duration}}ms)`);
      return result;
      
    }} catch (error) {{
      const duration = Date.now() - startTime;
      const screenshot = await this.takeScreenshot(`error_${{testName}}`);
      
      const result: TestResult = {{
        testName,
        status: 'fail',
        duration,
        error: error instanceof Error ? error.message : String(error),
        screenshots: [screenshot]
      }};
      
      this.results.push(result);
      console.error(`测试失败: ${{testName}} (耗时: ${{duration}}ms)`, error);
      return result;
    }}
  }}

  async generateReport(): Promise<void> {{
    const report = {{
      testSuite: '{script_name}',
      timestamp: new Date().toISOString(),
      totalTests: this.results.length,
      passedTests: this.results.filter(r => r.status === 'pass').length,
      failedTests: this.results.filter(r => r.status === 'fail').length,
      totalDuration: this.results.reduce((sum, r) => sum + r.duration, 0),
      results: this.results
    }};

    const reportPath = join(process.cwd(), 'reports', `test-report-${{Date.now()}}.json`);
    writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`测试报告已生成: ${{reportPath}}`);
  }}

  // 测试步骤
  {test_methods}
}}

async function main() {{
  const runner = new TestRunner();
  
  try {{
    await runner.setup();
    
    {test_execution}
    
    await runner.generateReport();
    
  }} catch (error) {{
    console.error('测试套件执行失败:', error);
  }} finally {{
    await runner.teardown();
  }}
}}

main().catch(console.error);
"""
    
    def _get_batch_template(self) -> str:
        """获取批量测试模板"""
        return """/**
 * 批量自动化测试脚本
 * 生成时间: {generation_time}
 * 包含测试用例: {test_cases_count}
 */

import {{ AndroidAgent, AndroidDevice, getConnectedDevices }} from '@midscene/android';
import "dotenv/config";

interface TestCase {{
  id: string;
  name: string;
  steps: () => Promise<void>;
  assertions: () => Promise<void>;
}}

class BatchTestRunner {{
  private device: AndroidDevice | null = null;
  private agent: AndroidAgent | null = null;
  private testCases: TestCase[] = [];

  constructor() {{
    this.initializeTestCases();
  }}

  private initializeTestCases(): void {{
    {test_cases_initialization}
  }}

  async runAllTests(): Promise<void> {{
    console.log(`开始执行批量测试，共 ${{this.testCases.length}} 个用例`);
    
    let passedCount = 0;
    let failedCount = 0;

    for (const testCase of this.testCases) {{
      try {{
        console.log(`执行测试用例: ${{testCase.name}}`);
        
        await testCase.steps();
        await testCase.assertions();
        
        passedCount++;
        console.log(`✓ ${{testCase.name}} - 通过`);
        
      }} catch (error) {{
        failedCount++;
        console.error(`✗ ${{testCase.name}} - 失败:`, error);
      }}
    }}

    console.log(`批量测试完成: 通过 ${{passedCount}}, 失败 ${{failedCount}}`);
  }}

  // 其他方法...
}}

async function main() {{
  const runner = new BatchTestRunner();
  await runner.runAllTests();
}}

main().catch(console.error);
"""
    
    def generate_script_file(self, script: GeneratedScript, 
                           template_type: str = 'basic',
                           output_dir: str = None) -> Dict[str, Any]:
        """
        生成脚本文件
        
        Args:
            script: 脚本对象
            template_type: 模板类型
            output_dir: 输出目录
            
        Returns:
            Dict[str, Any]: 生成结果
        """
        try:
            # 确定输出目录
            if not output_dir:
                output_dir = self.output_dirs['scripts'] / script.package_name
            
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # 生成文件名
            safe_name = self._sanitize_filename(script.script_name)
            filename = f"{safe_name}.ts"
            file_path = output_path / filename
            
            # 写入脚本内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(script.script_content)
            
            # 生成配套文件
            support_files = self._generate_support_files(script, output_path)
            
            logger.info(f"脚本文件生成成功: {file_path}")
            return {
                'success': True,
                'script_file': str(file_path),
                'output_dir': str(output_path),
                'support_files': support_files,
                'file_size': len(script.script_content)
            }
            
        except Exception as e:
            logger.error(f"生成脚本文件失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _generate_support_files(self, script: GeneratedScript, 
                               output_path: Path) -> List[str]:
        """生成配套文件"""
        try:
            support_files = []
            
            # 生成package.json
            package_json = {
                "name": self._sanitize_filename(script.script_name).lower(),
                "version": "1.0.0",
                "description": script.script_description,
                "main": f"{self._sanitize_filename(script.script_name)}.js",
                "scripts": {
                    "build": "tsc",
                    "test": f"node {self._sanitize_filename(script.script_name)}.js"
                },
                "dependencies": {
                    "@midscene/android": script.midscene_version or "latest",
                    "dotenv": "^16.0.0"
                },
                "devDependencies": {
                    "@types/node": "^20.0.0",
                    "typescript": "^5.0.0"
                }
            }
            
            package_file = output_path / 'package.json'
            with open(package_file, 'w', encoding='utf-8') as f:
                json.dump(package_json, f, indent=2)
            support_files.append(str(package_file))
            
            # 生成tsconfig.json
            tsconfig = {
                "compilerOptions": {
                    "target": "ES2020",
                    "module": "commonjs",
                    "lib": ["ES2020"],
                    "outDir": "./dist",
                    "strict": True,
                    "esModuleInterop": True,
                    "skipLibCheck": True
                }
            }
            
            tsconfig_file = output_path / 'tsconfig.json'
            with open(tsconfig_file, 'w', encoding='utf-8') as f:
                json.dump(tsconfig, f, indent=2)
            support_files.append(str(tsconfig_file))
            
            # 生成README.md
            readme_content = f"""# {script.script_name}

{script.script_description}

## 运行说明

1. 安装依赖:
```bash
npm install
```

2. 编译TypeScript:
```bash
npm run build
```

3. 运行测试:
```bash
npm test
```

## 注意事项

- 确保Android设备已连接并启用USB调试
- 确保目标应用已安装: {script.package_name}
- 配置环境变量（如需要）

## 脚本信息

- 生成时间: {script.created_at}
- Midscene版本: {script.midscene_version}
- 生成方法: {script.generation_method}
"""
            
            readme_file = output_path / 'README.md'
            with open(readme_file, 'w', encoding='utf-8') as f:
                f.write(readme_content)
            support_files.append(str(readme_file))
            
            return support_files
            
        except Exception as e:
            logger.error(f"生成配套文件失败: {e}")
            return []
    
    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名"""
        try:
            # 移除或替换不安全的字符
            safe_chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_"
            sanitized = "".join(c if c in safe_chars else "_" for c in filename)
            
            # 确保不以数字开头
            if sanitized and sanitized[0].isdigit():
                sanitized = "test_" + sanitized
            
            # 限制长度
            if len(sanitized) > 50:
                sanitized = sanitized[:50]
            
            return sanitized or "test_script"
            
        except Exception as e:
            logger.warning(f"清理文件名失败: {e}")
            return "test_script"
    
    def create_project_from_scripts(self, project_name: str, 
                                  script_ids: List[str],
                                  output_dir: str = None) -> Dict[str, Any]:
        """
        从脚本创建完整项目
        
        Args:
            project_name: 项目名称
            script_ids: 脚本ID列表
            output_dir: 输出目录
            
        Returns:
            Dict[str, Any]: 创建结果
        """
        try:
            # 确定输出目录
            if not output_dir:
                output_dir = self.output_dirs['projects'] / self._sanitize_filename(project_name)
            
            project_path = Path(output_dir)
            project_path.mkdir(parents=True, exist_ok=True)
            
            # 创建项目结构
            directories = ['src', 'tests', 'config', 'docs', 'reports', 'screenshots']
            for directory in directories:
                (project_path / directory).mkdir(exist_ok=True)
            
            # 生成脚本文件
            generated_scripts = []
            for script_id in script_ids:
                script = self.db_manager.get_script_by_id(script_id)
                if script:
                    result = self.generate_script_file(
                        script, 
                        output_dir=str(project_path / 'tests')
                    )
                    if result.get('success'):
                        generated_scripts.append(result)
            
            # 创建项目配置文件
            project_files = self._create_project_files(project_name, project_path, generated_scripts)
            
            logger.info(f"项目创建成功: {project_path}")
            return {
                'success': True,
                'project_path': str(project_path),
                'project_name': project_name,
                'generated_scripts': generated_scripts,
                'project_files': project_files
            }
            
        except Exception as e:
            logger.error(f"创建项目失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _create_project_files(self, project_name: str, 
                            project_path: Path,
                            generated_scripts: List[Dict]) -> List[str]:
        """创建项目配置文件"""
        try:
            project_files = []
            
            # 主package.json
            main_package = {
                "name": self._sanitize_filename(project_name).lower(),
                "version": "1.0.0",
                "description": f"自动化测试项目: {project_name}",
                "scripts": {
                    "build": "tsc",
                    "test": "npm run build && node dist/index.js",
                    "test:all": "npm run build && npm run test:scripts",
                    "test:scripts": " && ".join([f"node dist/tests/{Path(script['script_file']).stem}.js" for script in generated_scripts])
                },
                "dependencies": {
                    "@midscene/android": "latest",
                    "dotenv": "^16.0.0"
                },
                "devDependencies": {
                    "@types/node": "^20.0.0",
                    "typescript": "^5.0.0"
                }
            }
            
            package_file = project_path / 'package.json'
            with open(package_file, 'w', encoding='utf-8') as f:
                json.dump(main_package, f, indent=2)
            project_files.append(str(package_file))
            
            # 主tsconfig.json
            main_tsconfig = {
                "compilerOptions": {
                    "target": "ES2020",
                    "module": "commonjs",
                    "lib": ["ES2020"],
                    "outDir": "./dist",
                    "rootDir": "./",
                    "strict": True,
                    "esModuleInterop": True,
                    "skipLibCheck": True,
                    "forceConsistentCasingInFileNames": True,
                    "resolveJsonModule": True
                },
                "include": ["src/**/*", "tests/**/*"],
                "exclude": ["node_modules", "dist"]
            }
            
            tsconfig_file = project_path / 'tsconfig.json'
            with open(tsconfig_file, 'w', encoding='utf-8') as f:
                json.dump(main_tsconfig, f, indent=2)
            project_files.append(str(tsconfig_file))
            
            # 创建主入口文件
            index_content = f"""/**
 * {project_name} - 主入口文件
 * 自动生成的测试项目
 */

import {{ execSync }} from 'child_process';
import {{ readdirSync }} from 'fs';
import {{ join }} from 'path';

async function runAllTests() {{
  console.log('开始执行所有测试脚本...');
  
  const testsDir = join(__dirname, 'tests');
  const testFiles = readdirSync(testsDir).filter(file => file.endsWith('.js'));
  
  let passedCount = 0;
  let failedCount = 0;
  
  for (const testFile of testFiles) {{
    try {{
      console.log(`\\n执行测试: ${{testFile}}`);
      execSync(`node ${{join(testsDir, testFile)}}`, {{ stdio: 'inherit' }});
      passedCount++;
      console.log(`✓ ${{testFile}} - 通过`);
    }} catch (error) {{
      failedCount++;
      console.error(`✗ ${{testFile}} - 失败`);
    }}
  }}
  
  console.log(`\\n测试完成: 通过 ${{passedCount}}, 失败 ${{failedCount}}`);
}}

runAllTests().catch(console.error);
"""
            
            index_file = project_path / 'src' / 'index.ts'
            with open(index_file, 'w', encoding='utf-8') as f:
                f.write(index_content)
            project_files.append(str(index_file))
            
            return project_files
            
        except Exception as e:
            logger.error(f"创建项目文件失败: {e}")
            return []
    
    def create_script_version(self, script_id: str, 
                            version_notes: str = None) -> Dict[str, Any]:
        """创建脚本版本"""
        try:
            script = self.db_manager.get_script_by_id(script_id)
            if not script:
                return {'success': False, 'error': '脚本不存在'}
            
            # 创建版本记录
            version = ScriptVersion(
                script_id=script.id,
                version_number=self._get_next_version_number(script.id),
                script_content=script.script_content,
                version_notes=version_notes or f"版本创建于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                created_by="script_generation_tool"
            )
            
            saved_version = self.db_manager.save_script_version(version)
            
            if saved_version:
                logger.info(f"脚本版本创建成功: {script.script_name} v{version.version_number}")
                return {
                    'success': True,
                    'version_id': saved_version.id,
                    'version_number': version.version_number
                }
            else:
                return {'success': False, 'error': '保存版本失败'}
            
        except Exception as e:
            logger.error(f"创建脚本版本失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _get_next_version_number(self, script_id: str) -> str:
        """获取下一个版本号"""
        try:
            # 从数据库查询最新版本号
            # 这里简化处理，实际应该查询数据库
            return f"1.{datetime.now().strftime('%Y%m%d%H%M%S')}"
        except Exception as e:
            logger.warning(f"获取版本号失败: {e}")
            return "1.0.0"
    
    def cleanup_old_files(self, days_old: int = 30) -> Dict[str, Any]:
        """清理旧文件"""
        try:
            cleaned_files = []
            total_size = 0
            
            for dir_type, dir_path in self.output_dirs.items():
                if dir_path.exists():
                    for file_path in dir_path.rglob('*'):
                        if file_path.is_file():
                            # 检查文件年龄
                            file_age = (datetime.now() - datetime.fromtimestamp(file_path.stat().st_mtime)).days
                            
                            if file_age > days_old:
                                file_size = file_path.stat().st_size
                                file_path.unlink()
                                
                                cleaned_files.append({
                                    'file': str(file_path),
                                    'size': file_size,
                                    'age_days': file_age
                                })
                                total_size += file_size
            
            logger.info(f"清理完成: 删除 {len(cleaned_files)} 个文件，释放 {total_size} 字节")
            return {
                'success': True,
                'cleaned_files_count': len(cleaned_files),
                'total_size_freed': total_size,
                'cleaned_files': cleaned_files
            }
            
        except Exception as e:
            logger.error(f"清理旧文件失败: {e}")
            return {'success': False, 'error': str(e)}
