"""
脚本生成系统主控制器
协调各个智能体完成从用例到脚本的完整流程
"""
import logging
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime as dt
from pathlib import Path

from backend.excel_reader import ExcelReader
from backend.script_database_manager import ScriptDatabaseManager
from backend.agents.case_info_agent import CaseInfoAgent
from backend.agents.script_generation_team import ScriptGenerationTeam
from backend.agents.script_review_agent import ScriptReviewAgent
from backend.agents.file_generation_agent import FileGenerationAgent

logger = logging.getLogger(__name__)


class ScriptGenerationController:
    """脚本生成系统主控制器"""
    
    def __init__(self, data_dir: str = None, testcase_dir: str = None):
        """
        初始化主控制器

        Args:
            data_dir: 数据目录路径，如果为None则自动检测
            testcase_dir: 测试用例输出目录，如果为None则使用 backend/testcase
        """
        # 自动检测项目根目录
        current_file = Path(__file__)
        project_root = current_file.parent.parent  # 从 backend/script_generation_controller.py 回到项目根目录

        if data_dir is None:
            self.data_dir = project_root / "backend" / "data"
        else:
            self.data_dir = Path(data_dir)

        if testcase_dir is None:
            # 使用相对于项目根目录的 backend/testcase 路径
            self.testcase_dir = project_root / "backend" / "testcase"
        else:
            self.testcase_dir = Path(testcase_dir)

        # 初始化组件
        self.excel_reader = ExcelReader(str(self.data_dir) if data_dir else None)
        self.script_db = ScriptDatabaseManager()
        
        # 初始化智能体
        self.case_info_agent = CaseInfoAgent()
        self.script_generation_team = ScriptGenerationTeam()
        self.script_review_agent = ScriptReviewAgent()
        self.file_generation_agent = FileGenerationAgent(str(self.testcase_dir))
        
        logger.info("脚本生成系统主控制器初始化完成")
    
    async def initialize_system(self) -> bool:
        """
        初始化系统，加载Excel数据到数据库
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            logger.info("开始初始化系统...")
            
            # 1. 读取Excel数据
            test_cases = self.excel_reader.read_test_cases()
            package_components = self.excel_reader.read_package_components()
            
            logger.info(f"读取到 {len(test_cases)} 个测试用例")
            logger.info(f"读取到 {len(package_components)} 个包组件映射")
            
            # 2. 保存到数据库
            if package_components:
                saved_components = self.script_db.save_package_components(package_components)
                logger.info(f"保存了 {saved_components} 个包组件映射")
            
            if test_cases:
                saved_cases = self.script_db.save_test_cases(test_cases)
                logger.info(f"保存了 {saved_cases} 个测试用例")
            
            logger.info("系统初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"系统初始化失败: {e}")
            return False
    
    async def generate_script_from_case_description(self, case_description: str, 
                                                  business_module: str = None) -> Dict[str, Any]:
        """
        从自然语言用例描述生成自动化脚本
        
        Args:
            case_description: 自然语言用例描述
            business_module: 业务模块（可选）
            
        Returns:
            Dict[str, Any]: 生成结果
        """
        try:
            logger.info("开始生成脚本流程...")
            
            # 1. 用例信息获取智能体：分析用例并获取相关元素
            logger.info("步骤1: 分析用例信息...")
            case_info = await self.case_info_agent.analyze_case_info(case_description, business_module)
            
            # 2. 创建智能体会话
            agent_session = self.script_db.create_agent_session(case_info.test_case.id)
            
            # 3. 脚本生成智能体：生成初始脚本
            logger.info("步骤2: 生成自动化脚本...")
            generation_result = await self.script_generation_team.generate_script(case_info)
            
            # 4. 脚本审查智能体：审查和优化脚本
            logger.info("步骤3: 审查脚本质量...")
            review_result = await self.script_review_agent.review_script(
                generation_result["script_content"], 
                case_info, 
                generation_result["metadata"]
            )
            
            # 5. 如果需要修订，进行迭代优化
            if review_result.get("needs_revision", False) and review_result["overall_score"] < 60:
                logger.info("脚本需要修订，进行二次生成...")
                # 可以在这里实现迭代优化逻辑
                pass
            
            # 6. 脚本文件生成智能体：保存最终脚本
            logger.info("步骤4: 保存脚本文件...")
            file_result = await self.file_generation_agent.save_script_file(
                review_result["final_script"],
                case_info,
                review_result,
                generation_result["metadata"]
            )
            
            # 7. 更新会话状态
            self._update_session_status(agent_session, file_result, review_result)
            
            # 8. 生成最终结果
            final_result = {
                "success": True,
                "session_id": agent_session.session_id,
                "test_case_id": case_info.test_case.tcid,
                "business_module": case_info.business_context.get("module_name"),
                "script_file": file_result["file_path"],
                "script_name": file_result["file_name"],
                "review_score": review_result["overall_score"],
                "generation_metadata": {
                    "case_analysis": {
                        "elements_found": len(case_info.related_elements),
                        "components_found": len(case_info.package_components)
                    },
                    "script_generation": generation_result["metadata"],
                    "review_result": {
                        "score": review_result["overall_score"],
                        "approved": review_result["approved"],
                        "issues_count": len(review_result["issues"])
                    },
                    "file_generation": {
                        "file_size": file_result["file_size"],
                        "database_saved": file_result["database_saved"]
                    }
                },
                "processing_time": dt.now().isoformat()
            }
            
            logger.info(f"脚本生成流程完成: {file_result['file_name']}")
            return final_result
            
        except Exception as e:
            logger.error(f"脚本生成流程失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "processing_time": dt.now().isoformat()
            }
    
    async def generate_script_from_case_id(self, case_id: str) -> Dict[str, Any]:
        """
        从数据库中的用例ID生成脚本
        
        Args:
            case_id: 测试用例ID
            
        Returns:
            Dict[str, Any]: 生成结果
        """
        try:
            # 从数据库获取测试用例
            test_case = self.script_db.get_test_case_by_id(case_id)
            if not test_case:
                return {
                    "success": False,
                    "error": f"未找到测试用例: {case_id}"
                }
            
            # 直接使用现有的测试用例生成脚本，确保 TCID 正确传递
            logger.info(f"使用现有测试用例生成脚本: {test_case.tcid}")

            # 获取业务模块信息
            with self.script_db.get_session() as session:
                from backend.models.script_models import BusinessModule
                business_module = session.query(BusinessModule).filter_by(
                    id=test_case.business_module_id
                ).first()
                module_name = business_module.module_name if business_module else test_case.group_name

            # 直接使用现有测试用例生成脚本
            return await self._generate_script_from_existing_case(test_case, module_name)
            
        except Exception as e:
            logger.error(f"从用例ID生成脚本失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _generate_script_from_existing_case(self, test_case, module_name: str) -> Dict[str, Any]:
        """
        从现有测试用例生成脚本，确保 TCID 正确传递

        Args:
            test_case: 现有的测试用例对象
            module_name: 业务模块名称

        Returns:
            Dict[str, Any]: 生成结果
        """
        try:
            logger.info(f"开始为现有用例生成脚本: {test_case.tcid}")

            # 1. 创建 CaseElementInfo，使用现有的测试用例
            from backend.agents.case_info_agent import CaseElementInfo

            # 获取相关的包组件
            package_components = self.excel_reader.get_components_by_module(module_name)

            # 构建业务上下文
            business_context = {
                "module_name": module_name,
                "component": test_case.component,
                "group_name": test_case.group_name,
                "sub_group": test_case.sub_group
            }

            # 创建 CaseElementInfo，直接使用现有的测试用例
            case_info = CaseElementInfo(
                test_case=test_case,  # 使用现有的测试用例，保持 TCID 不变
                related_elements=[],
                package_components=package_components,
                business_context=business_context
            )

            logger.info(f"CaseElementInfo 创建完成，TCID: {case_info.test_case.tcid}")

            # 2. 生成脚本
            script_result = await self.script_generation_team.generate_script(case_info)

            if not script_result.get("success"):
                return {
                    "success": False,
                    "error": f"脚本生成失败: {script_result.get('error')}",
                    "processing_time": dt.now().isoformat()
                }

            script_content = script_result["script_content"]
            generation_metadata = script_result.get("metadata", {})

            # 3. 审查脚本
            review_result = await self.script_review_agent.review_script(script_content, case_info, generation_metadata)

            # 4. 保存脚本文件
            file_result = await self.file_generation_agent.save_script_file(
                script_content, case_info, review_result, generation_metadata
            )

            return {
                "success": True,
                "script_content": script_content,
                "script_file": file_result.get("file_path"),
                "review_score": review_result.get("overall_score"),
                "review_result": review_result,
                "test_case_id": test_case.tcid,  # 确保返回正确的 TCID
                "processing_time": dt.now().isoformat()
            }

        except Exception as e:
            logger.error(f"从现有用例生成脚本失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "test_case_id": test_case.tcid if test_case else "unknown",
                "processing_time": dt.now().isoformat()
            }

    async def batch_generate_scripts(self, case_ids: List[str] = None,
                                   business_module: str = None) -> Dict[str, Any]:
        """
        批量生成脚本
        
        Args:
            case_ids: 指定的用例ID列表，为None时处理所有用例
            business_module: 指定的业务模块，为None时处理所有模块
            
        Returns:
            Dict[str, Any]: 批量处理结果
        """
        try:
            logger.info("开始批量生成脚本...")
            
            # 获取要处理的测试用例
            with self.script_db.get_session() as session:
                from backend.models.script_models import TestCase, BusinessModule
                query = session.query(TestCase)

                if case_ids:
                    query = query.filter(TestCase.tcid.in_(case_ids))

                if business_module:
                    query = query.join(BusinessModule).filter(
                        BusinessModule.module_name == business_module
                    )
                
                test_cases = query.all()
            
            logger.info(f"找到 {len(test_cases)} 个待处理的测试用例")
            
            # 批量处理
            results = []
            success_count = 0
            
            for test_case in test_cases:
                try:
                    logger.info(f"处理用例: {test_case.tcid}")
                    result = await self.generate_script_from_case_id(test_case.tcid)
                    results.append(result)
                    
                    if result.get("success", False):
                        success_count += 1
                        
                except Exception as e:
                    logger.error(f"处理用例 {test_case.tcid} 失败: {e}")
                    results.append({
                        "success": False,
                        "test_case_id": test_case.tcid,
                        "error": str(e)
                    })
            
            batch_result = {
                "success": True,
                "total_cases": len(test_cases),
                "success_count": success_count,
                "failure_count": len(test_cases) - success_count,
                "results": results,
                "processing_time": dt.now().isoformat()
            }
            
            logger.info(f"批量处理完成: {success_count}/{len(test_cases)} 成功")
            return batch_result
            
        except Exception as e:
            logger.error(f"批量生成脚本失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _update_session_status(self, session, file_result: Dict[str, Any], 
                             review_result: Dict[str, Any]):
        """更新会话状态"""
        try:
            with self.script_db.get_session() as db_session:
                from backend.models.script_models import AgentSession
                session_record = db_session.query(AgentSession).filter_by(
                    session_id=session.session_id
                ).first()
                
                if session_record:
                    session_record.case_analysis_completed = True
                    session_record.script_generation_completed = True
                    session_record.script_review_completed = True
                    session_record.file_generation_completed = file_result.get("success", False)
                    session_record.status = "已完成" if file_result.get("success", False) else "失败"
                    session_record.completed_at = dt.now()
                    session_record.final_script_id = file_result.get("script_id")
                    
                    if not file_result.get("success", False):
                        session_record.error_message = file_result.get("message", "未知错误")
                    
                    db_session.commit()
                    
        except Exception as e:
            logger.error(f"更新会话状态失败: {e}")
    
    def get_generation_statistics(self) -> Dict[str, Any]:
        """获取生成统计信息"""
        try:
            with self.script_db.get_session() as session:
                from backend.models.script_models import TestCase, GeneratedScript, AgentSession

                # 统计各种数据
                total_cases = session.query(TestCase).count()
                total_scripts = session.query(GeneratedScript).count()
                total_sessions = session.query(AgentSession).count()

                completed_sessions = session.query(AgentSession).filter_by(
                    status="已完成"
                ).count()
                
                return {
                    "total_test_cases": total_cases,
                    "total_generated_scripts": total_scripts,
                    "total_sessions": total_sessions,
                    "completed_sessions": completed_sessions,
                    "success_rate": (completed_sessions / total_sessions * 100) if total_sessions > 0 else 0
                }
                
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}


async def main():
    """测试主控制器"""
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # 创建主控制器
    controller = ScriptGenerationController()
    
    # 初始化系统
    if await controller.initialize_system():
        print("系统初始化成功")
        
        # 从Excel表中获取测试用例并生成脚本
        print("\n📊 获取Excel中的测试用例...")

        # 获取所有测试用例
        test_cases = controller.excel_reader.read_test_cases()

        if not test_cases:
            print("❌ 未找到测试用例，请检查Excel文件")
            return

        print(f"✅ 找到 {len(test_cases)} 个测试用例")

        # 选择第一个测试用例进行演示
        selected_case = test_cases[0]
        print(f"\n🎯 选择测试用例: {selected_case.tcid} - {selected_case.case_name}")
        print(f"   测试步骤: {selected_case.steps}")
        print(f"   预期结果: {selected_case.expect_result}")
        print(f"   业务模块: {selected_case.group_name}")

        # 使用真实的测试用例生成脚本
        result = await controller.generate_script_from_case_id(selected_case.tcid)

        if result.get("success"):
            print(f"\n🎉 脚本生成成功!")
            print(f"   脚本文件: {result.get('script_file', '未知')}")
            print(f"   审查评分: {result.get('review_score', '未评分')}")
            print(f"   生成时间: {result.get('processing_time', '未知')}")
        else:
            print(f"\n❌ 脚本生成失败: {result.get('error', '未知错误')}")

        # 如果有多个测试用例，询问是否继续生成其他用例
        if len(test_cases) > 1:
            print(f"\n💡 还有 {len(test_cases) - 1} 个测试用例可以生成")
            print("   可以通过 generate_script_from_case_id() 方法生成其他用例的脚本")
    else:
        print("系统初始化失败")


if __name__ == "__main__":
    asyncio.run(main())
