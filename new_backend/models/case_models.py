"""
测试用例分析相关数据模型
支持自然语言用例解析、操作步骤提取、断言生成
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, JSON, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum

Base = declarative_base()


class ActionType(Enum):
    """操作类型枚举"""
    CLICK = "click"
    INPUT = "input"
    SWIPE = "swipe"
    SCROLL = "scroll"
    WAIT = "wait"
    BACK = "back"
    LAUNCH = "launch"
    CLOSE = "close"
    DRAG = "drag"
    LONG_CLICK = "long_click"


class AssertionType(Enum):
    """断言类型枚举"""
    ELEMENT_EXISTS = "element_exists"
    ELEMENT_NOT_EXISTS = "element_not_exists"
    TEXT_CONTAINS = "text_contains"
    TEXT_EQUALS = "text_equals"
    PAGE_TITLE = "page_title"
    TOAST_MESSAGE = "toast_message"
    DIALOG_APPEARS = "dialog_appears"
    PAGE_NAVIGATION = "page_navigation"


@dataclass
class ActionStep:
    """操作步骤数据类"""
    step_index: int
    action_type: str
    target_element: Optional[str] = None
    action_description: str = ""
    input_data: Optional[str] = None
    coordinates: Optional[tuple] = None
    wait_time: Optional[float] = None
    ai_action_text: str = ""  # Midscene.js AI操作描述


@dataclass
class AssertionStep:
    """断言步骤数据类"""
    step_index: int
    assertion_type: str
    target_element: Optional[str] = None
    expected_value: str = ""
    assertion_description: str = ""
    ai_assertion_text: str = ""  # Midscene.js AI断言描述


class TestCase(Base):
    """测试用例表"""
    __tablename__ = 'test_cases'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    case_id = Column(String(100), unique=True, nullable=False, comment='用例ID')
    
    # 基本信息
    module_name = Column(String(200), comment='模块名称')
    case_name = Column(String(500), nullable=False, comment='用例名称')
    test_purpose = Column(Text, comment='测试目的')
    package_name = Column(String(200), comment='应用包名')
    
    # 用例内容
    preconditions = Column(Text, comment='前置条件')
    test_steps = Column(Text, nullable=False, comment='测试步骤')
    expected_results = Column(Text, comment='预期结果')
    
    # 用例属性
    priority = Column(String(20), default='medium', comment='优先级: low, medium, high, critical')
    case_type = Column(String(50), comment='用例类型: functional, ui, integration, etc.')
    automation_level = Column(String(50), default='manual', comment='自动化级别: manual, semi-auto, full-auto')
    
    # 分析状态
    analysis_status = Column(String(50), default='pending', comment='分析状态: pending, analyzing, completed, failed')
    script_generation_status = Column(String(50), default='pending', comment='脚本生成状态')
    
    # 元数据
    case_metadata = Column(JSON, comment='用例元数据')
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    # 关联关系
    analysis_results = relationship("CaseAnalysisResult", back_populates="test_case", cascade="all, delete-orphan")
    generated_scripts = relationship("GeneratedScript", back_populates="test_case")


class CaseAnalysisResult(Base):
    """用例分析结果表"""
    __tablename__ = 'case_analysis_results'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    case_id = Column(Integer, ForeignKey('test_cases.id'), nullable=False, comment='测试用例ID')
    analysis_session_id = Column(String(100), comment='分析会话ID')
    
    # 分析结果
    extracted_actions = Column(JSON, comment='提取的操作步骤（ActionStep列表）')
    extracted_assertions = Column(JSON, comment='提取的断言步骤（AssertionStep列表）')
    
    # 元素匹配结果
    matched_elements = Column(JSON, comment='匹配的UI元素信息')
    element_mapping = Column(JSON, comment='元素映射关系')
    
    # 分析质量评估
    analysis_confidence = Column(Float, comment='分析置信度')
    completeness_score = Column(Float, comment='完整性评分')
    clarity_score = Column(Float, comment='清晰度评分')
    
    # 分析详情
    analysis_notes = Column(Text, comment='分析备注')
    identified_risks = Column(JSON, comment='识别的风险点')
    optimization_suggestions = Column(JSON, comment='优化建议')
    
    # 智能体信息
    analyzer_agent = Column(String(100), comment='分析智能体名称')
    analysis_model = Column(String(100), comment='使用的AI模型')
    processing_time = Column(Integer, comment='处理时间（毫秒）')
    
    # 时间戳
    analyzed_at = Column(DateTime, default=datetime.now, comment='分析时间')
    
    # 关联关系
    test_case = relationship("TestCase", back_populates="analysis_results")


class ElementMapping(Base):
    """元素映射表"""
    __tablename__ = 'element_mappings'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    case_analysis_id = Column(Integer, ForeignKey('case_analysis_results.id'), nullable=False, comment='用例分析ID')
    
    # 映射信息
    step_index = Column(Integer, comment='步骤索引')
    action_description = Column(Text, comment='操作描述')
    target_element_description = Column(Text, comment='目标元素描述')
    
    # 匹配的元素信息
    matched_element_id = Column(String(100), comment='匹配的元素ID')
    element_selector = Column(Text, comment='元素选择器')
    match_confidence = Column(Float, comment='匹配置信度')
    match_method = Column(String(50), comment='匹配方法: text, id, xpath, ai_semantic')
    
    # 备选元素
    alternative_elements = Column(JSON, comment='备选元素列表')
    
    # 时间戳
    mapped_at = Column(DateTime, default=datetime.now, comment='映射时间')


class CaseValidation(Base):
    """用例验证表"""
    __tablename__ = 'case_validations'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    case_id = Column(Integer, ForeignKey('test_cases.id'), nullable=False, comment='测试用例ID')
    
    # 验证结果
    validation_status = Column(String(50), comment='验证状态: passed, failed, warning')
    validation_score = Column(Float, comment='验证评分')
    
    # 验证详情
    missing_elements = Column(JSON, comment='缺失的元素')
    ambiguous_steps = Column(JSON, comment='模糊的步骤')
    incomplete_assertions = Column(JSON, comment='不完整的断言')
    
    # 验证建议
    improvement_suggestions = Column(JSON, comment='改进建议')
    
    # 验证者信息
    validator_agent = Column(String(100), comment='验证智能体')
    validation_model = Column(String(100), comment='验证模型')
    
    # 时间戳
    validated_at = Column(DateTime, default=datetime.now, comment='验证时间')


# 创建所有表的函数
def create_case_tables(engine):
    """创建用例相关表"""
    Base.metadata.create_all(engine)
