/**
 * 自动生成的测试脚本
 * 
 * 测试用例ID: TexAI_AIALG_VA_NEW_feedback_0010
 * 业务模块: AI中心
 * 生成时间: 2025-08-29 19:27:50
 * 文件描述: 自动化测试脚本 - TexAI_AIALG_VA_NEW_feedback_0010
 * 
 * 测试步骤:
 * 1.点击Ella/Folax桌面图标进入对话页->执行任意指令，如what's the weather today
2.点击卡片下方的踩icon
3.检查feedback弹窗的选项
4.问题类型选择其他/建议
 *
 * 预期结果:
 * 2.点踩icon切换已点击状态，会弹出feedback弹窗。弹窗文案翻译成对应语言
3.弹窗中有：问题类型词条，词条可多选
4.问题类型选择其他/建议后会出现问题反馈。问题反馈底纹：您的宝贵意见对我们至关重要,我们希望倾听每一位用户的声音。
 * 
 * 注意: 此文件由AI自动生成，请根据实际情况调整
 */

import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';
import "dotenv/config";

const sleep = (ms: number) => new Promise((r) => setTimeout(r, ms));

async function runFeedbackTest() {
    let device: AndroidDevice | null = null;

    try {
        // 设备连接和初始化
        const devices = await getConnectedDevices();
        if (devices.length === 0) {
            throw new Error('No connected devices found');
        }

        device = new AndroidDevice(devices[0].udid, {
            imeStrategy: "yadb-for-non-ascii"
        });
        
        // 创建AI智能体，配置全局操作上下文
        const agent = new AndroidAgent(device, {
            aiActionContext: `如果出现位置、权限、用户协议等弹窗，点击同意。如果出现登录页面，关闭它`
        });

        await device.connect();
        
        // 启动AI助手应用 (替换为实际包名)


        // 测试步骤1: 进入对话页并执行天气查询指令
        // await agent.aiAction('点击Ella或Folax桌面图标进入对话页面');
        await device.launch('com.transsion.aivoiceassistant');
        await sleep(2000);
        await agent.aiWaitFor('对话页面完全加载');
        await agent.aiInput("what\'s the weather today",'底部输入框');
        await agent.aiTap('发送按钮');
        await agent.aiWaitFor('天气卡片显示在对话页面中');

        // 测试步骤2: 点击踩icon
        // 滚动页面，使底部的踩icon可见
        await agent.aiScroll();
        //
        await agent.aiTap('踩icon');
        //
        // await agent.aiWaitFor('踩icon状态变为高亮');
        // await agent.aiAssert('踩icon状态变为高亮');
        //
        await agent.aiWaitFor('feedback弹窗显示');

        // 验证点1: 检查弹窗状态和文案
        await agent.aiAssert('feedback弹窗已显示');
        await agent.aiAssert('弹窗文案已翻译为英语');

        // 验证点2: 检查问题类型选项
        const feedbackOptions = await agent.aiQuery("{options: string[]}, 获取feedback弹窗中的问题类型选项");
        if (!feedbackOptions || !feedbackOptions.options || feedbackOptions.options.length === 0) {
            throw new Error('未找到反馈选项');
        }
        console.log('找到反馈选项:', feedbackOptions.options);

        // 测试步骤3: 选择"其他/建议"选项
        // await agent.aiAction('在feedback弹窗中选择"other/suggestions"选项');
        await agent.aiTap('other/suggestions');

        await agent.aiWaitFor('问题反馈输入框出现');

        // 验证点3: 检查问题反馈提示文案
        // await agent.aiAssert('问题反馈输入框显示底纹文案:"您的宝贵意见对我们至关重要,我们希望倾听每一位用户的声音"');
        await agent.aiAssert('问题反馈输入框显示底纹文案:"your valuable feedback is important to us"');

        console.log('测试成功完成！所有验证点已通过');

    } catch (error) {
        console.error('测试执行失败:', error);
        throw error;
    } finally {
        // 清理资源
        if (device) {
            await device.destroy();
        }
    }
}

// 执行测试
runFeedbackTest().catch(console.error);