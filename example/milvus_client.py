from pymilvus import MilvusClient

client = MilvusClient("milvus_demo.db")

def main():
    """主函数"""
    if client.has_collection(collection_name="demo_collection"):
        client.drop_collection(collection_name="demo_collection")
    client.create_collection(
        collection_name="demo_collection",
        dimension=768,  # The vectors we will use in this demo has 768 dimensions
    )


if __name__ == "__main__":
    main()
