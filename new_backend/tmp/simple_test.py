#!/usr/bin/env python3
"""
简化测试脚本 - 直接测试核心功能
避免复杂的模块导入问题
"""
import os
import sys
import pandas as pd
from pathlib import Path
from loguru import logger

# 添加相对路径导入
sys.path.append(str(Path(__file__).parent.parent))

# 直接导入需要的模块
try:
    from utils.module_package_mapper import ModulePackageMapper
    print("✓ 成功导入 ModulePackageMapper")
except Exception as e:
    print(f"✗ 导入 ModulePackageMapper 失败: {e}")

try:
    from utils.excel_case_reader import ExcelCaseReader
    print("✓ 成功导入 ExcelCaseReader")
except Exception as e:
    print(f"✗ 导入 ExcelCaseReader 失败: {e}")


def test_excel_structure_analysis():
    """测试Excel结构分析"""
    try:
        print("\n" + "="*60)
        print("测试Excel结构分析")
        print("="*60)
        
        # Excel文件路径
        excel_file = Path(__file__).parent.parent / "data" / "case" / "case.xlsx"
        
        if not excel_file.exists():
            print(f"❌ Excel文件不存在: {excel_file}")
            return False
        
        # 读取Excel文件
        df = pd.read_excel(excel_file, engine='openpyxl')
        
        print(f"✓ 成功读取Excel文件")
        print(f"  - 文件路径: {excel_file}")
        print(f"  - 总行数: {len(df)}")
        print(f"  - 总列数: {len(df.columns)}")
        
        # 显示列名
        print(f"\n列名列表:")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i:2d}. {col}")
        
        # 显示第一行数据
        if len(df) > 0:
            print(f"\n第一行数据示例:")
            row = df.iloc[0]
            for col in ['*TCID', '*CaseName', '*Group', '*Component', '*Steps']:
                if col in df.columns:
                    value = row[col]
                    if pd.isna(value):
                        value_str = "NULL"
                    else:
                        value_str = str(value)[:50]
                        if len(str(value)) > 50:
                            value_str += "..."
                    print(f"  {col}: {value_str}")
        
        return True
        
    except Exception as e:
        print(f"❌ Excel结构分析失败: {e}")
        return False


def test_module_package_mapper():
    """测试模块包映射器"""
    try:
        print("\n" + "="*60)
        print("测试模块包映射器")
        print("="*60)
        
        # 创建映射器
        mapper = ModulePackageMapper()
        print("✓ 成功创建 ModulePackageMapper")
        
        # 测试读取包组件映射
        print("\n1. 测试读取包组件映射...")
        components = mapper.read_package_components_from_excel()
        print(f"✓ 读取到 {len(components)} 个包组件映射")
        
        # 显示前几个映射
        for i, comp in enumerate(components[:3]):
            print(f"\n映射 {i+1}:")
            print(f"  业务模块: {comp.business_module}")
            print(f"  包名: {comp.package_name}")
            print(f"  组件: {comp.component_name}")
            print(f"  描述: {comp.description[:50]}...")
        
        # 测试数据库操作（如果配置了数据库）
        try:
            print("\n2. 测试数据库操作...")
            saved_count = mapper.save_package_components_to_db(components)
            print(f"✓ 成功保存 {saved_count} 个映射到数据库")
            
            # 测试查询
            stats = mapper.get_mapping_statistics()
            print(f"✓ 统计信息: {stats}")
            
        except Exception as e:
            print(f"⚠ 数据库操作跳过（可能未配置）: {e}")
        
        # 清理资源
        mapper.cleanup()
        print("✓ 资源清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块包映射器测试失败: {e}")
        return False


def test_excel_case_reader():
    """测试Excel用例读取器"""
    try:
        print("\n" + "="*60)
        print("测试Excel用例读取器")
        print("="*60)
        
        # 创建读取器
        reader = ExcelCaseReader()
        print("✓ 成功创建 ExcelCaseReader")
        
        # 测试读取Excel文件
        print("\n1. 测试读取Excel用例文件...")
        real_cases = reader.read_test_cases_from_excel()
        print(f"✓ 读取到 {len(real_cases)} 个真实用例")
        
        # 显示第一个用例的详细信息
        if real_cases:
            case = real_cases[0]
            print(f"\n第一个用例详情:")
            print(f"  ID: {case.tcid}")
            print(f"  名称: {case.casename}")
            print(f"  模块: {case.group}")
            print(f"  组件: {case.component}")
            print(f"  类型: {case.type}")
            print(f"  优先级: {case.level}")
            print(f"  步骤: {case.steps[:100]}...")
            print(f"  预期: {case.expectresult[:100]}...")
        
        # 测试转换为标准格式
        print("\n2. 测试转换为标准格式...")
        standard_cases = reader.convert_to_standard_format(real_cases)
        print(f"✓ 转换为 {len(standard_cases)} 个标准格式用例")
        
        # 显示转换后的第一个用例
        if standard_cases:
            std_case = standard_cases[0]
            print(f"\n转换后的第一个用例:")
            print(f"  ID: {std_case.case_id}")
            print(f"  名称: {std_case.case_name}")
            print(f"  模块: {std_case.module_name}")
            print(f"  包名: {std_case.package_name}")
            print(f"  类型: {std_case.test_type}")
            print(f"  优先级: {std_case.priority}")
        
        # 清理资源
        reader.cleanup()
        print("✓ 资源清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ Excel用例读取器测试失败: {e}")
        return False


def main():
    """主函数"""
    try:
        # 设置日志
        logger.remove()
        logger.add(sys.stdout, level="INFO", format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | {message}")
        
        print("开始简化测试...")
        
        # 测试1: Excel结构分析
        success1 = test_excel_structure_analysis()
        
        # 测试2: 模块包映射器
        success2 = test_module_package_mapper()
        
        # 测试3: Excel用例读取器
        success3 = test_excel_case_reader()
        
        # 总结
        print("\n" + "="*60)
        print("测试总结")
        print("="*60)
        print(f"Excel结构分析: {'✓ 通过' if success1 else '✗ 失败'}")
        print(f"模块包映射器: {'✓ 通过' if success2 else '✗ 失败'}")
        print(f"Excel用例读取器: {'✓ 通过' if success3 else '✗ 失败'}")
        
        if all([success1, success2, success3]):
            print("\n🎉 所有核心功能测试通过！")
        else:
            print("\n❌ 部分测试失败，请检查错误信息。")
        
    except Exception as e:
        print(f"❌ 测试程序执行失败: {e}")


if __name__ == "__main__":
    main()
