"""
多智能体自动化脚本生成系统API接口
提供统一的API接口供外部调用
"""
import os
import sys
import asyncio
import json
from typing import Dict, Any, Optional, List
from pathlib import Path
from datetime import datetime
from loguru import logger
from fastapi import FastAPI, HTTPException, UploadFile, File
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel

# 添加相对路径导入
sys.path.append(str(Path(__file__).parent))

from main_controller import MainController
from agents.batch_processing_agent import BatchProcessingAgent
from utils.script_generation_tool import ScriptGenerationTool


# 数据模型
class TestCaseRequest(BaseModel):
    case_id: str
    case_name: str
    test_purpose: Optional[str] = None
    package_name: str
    module_name: Optional[str] = None
    preconditions: Optional[str] = None
    test_steps: str
    expected_results: Optional[str] = None
    priority: str = "medium"
    test_type: str = "functional"


class BatchProcessRequest(BaseModel):
    test_cases: List[TestCaseRequest]
    package_name: Optional[str] = None


class AppAnalysisRequest(BaseModel):
    package_name: str
    app_name: Optional[str] = None


class ScriptExportRequest(BaseModel):
    script_ids: List[str]
    output_dir: Optional[str] = None


class ProjectCreateRequest(BaseModel):
    project_name: str
    script_ids: List[str]
    output_dir: Optional[str] = None


# 创建FastAPI应用
app = FastAPI(
    title="多智能体自动化脚本生成系统",
    description="基于Midscene.js和AutoGen的Android自动化测试脚本生成系统",
    version="1.0.0"
)

# 全局组件
controller = None
batch_agent = None
script_tool = None


@app.on_event("startup")
async def startup_event():
    """启动事件"""
    global controller, batch_agent, script_tool
    
    try:
        logger.info("初始化API服务...")
        
        controller = MainController()
        batch_agent = BatchProcessingAgent()
        script_tool = ScriptGenerationTool()
        
        logger.info("API服务初始化完成")
        
    except Exception as e:
        logger.error(f"API服务初始化失败: {e}")
        raise


@app.on_event("shutdown")
async def shutdown_event():
    """关闭事件"""
    global controller, batch_agent, script_tool
    
    try:
        logger.info("清理API服务资源...")
        
        if controller:
            controller.cleanup()
        
        if batch_agent:
            batch_agent.cleanup()
        
        logger.info("API服务资源清理完成")
        
    except Exception as e:
        logger.warning(f"API服务资源清理失败: {e}")


# API路由
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "多智能体自动化脚本生成系统API",
        "version": "1.0.0",
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        if not controller:
            raise HTTPException(status_code=503, detail="系统未初始化")
        
        status = await controller.get_system_status()
        return JSONResponse(content=status)
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/analyze-app")
async def analyze_app(request: AppAnalysisRequest):
    """分析应用"""
    try:
        if not controller:
            raise HTTPException(status_code=503, detail="系统未初始化")
        
        result = await controller.analyze_app(
            package_name=request.package_name,
            app_name=request.app_name
        )
        
        if result.get('success'):
            return JSONResponse(content=result)
        else:
            raise HTTPException(status_code=400, detail=result.get('error', '分析失败'))
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分析应用失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/process-test-case")
async def process_test_case(request: TestCaseRequest):
    """处理单个测试用例"""
    try:
        if not controller:
            raise HTTPException(status_code=503, detail="系统未初始化")
        
        test_case_data = request.dict()
        result = await controller.process_test_case(test_case_data)
        
        if result.get('success'):
            return JSONResponse(content=result)
        else:
            raise HTTPException(status_code=400, detail=result.get('error', '处理失败'))
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理测试用例失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/batch-process")
async def batch_process(request: BatchProcessRequest):
    """批量处理测试用例"""
    try:
        if not controller:
            raise HTTPException(status_code=503, detail="系统未初始化")
        
        test_cases_data = [case.dict() for case in request.test_cases]
        result = await controller.batch_process_cases(
            cases_data=test_cases_data,
            package_name=request.package_name
        )
        
        if result.get('success'):
            return JSONResponse(content=result)
        else:
            raise HTTPException(status_code=400, detail=result.get('error', '批量处理失败'))
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量处理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/upload-excel")
async def upload_excel(file: UploadFile = File(...), sheet_name: Optional[str] = None):
    """上传Excel文件进行批量处理"""
    try:
        if not batch_agent:
            raise HTTPException(status_code=503, detail="批量处理服务未初始化")
        
        # 检查文件类型
        if not file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(status_code=400, detail="只支持Excel文件(.xlsx, .xls)")
        
        # 保存上传的文件
        upload_dir = Path("tmp/uploads")
        upload_dir.mkdir(parents=True, exist_ok=True)
        
        file_path = upload_dir / f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{file.filename}"
        
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # 处理Excel文件
        result = await batch_agent.process_excel_file(
            excel_file_path=str(file_path),
            sheet_name=sheet_name
        )
        
        # 清理临时文件
        try:
            file_path.unlink()
        except:
            pass
        
        if result.get('success'):
            return JSONResponse(content=result)
        else:
            raise HTTPException(status_code=400, detail=result.get('error', 'Excel处理失败'))
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Excel文件处理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/export-scripts")
async def export_scripts(request: ScriptExportRequest):
    """导出脚本文件"""
    try:
        if not batch_agent:
            raise HTTPException(status_code=503, detail="批量处理服务未初始化")
        
        result = await batch_agent.export_scripts_to_files(
            script_ids=request.script_ids,
            output_dir=request.output_dir
        )
        
        if result.get('success'):
            return JSONResponse(content=result)
        else:
            raise HTTPException(status_code=400, detail=result.get('error', '导出失败'))
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出脚本失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/create-project")
async def create_project(request: ProjectCreateRequest):
    """创建项目"""
    try:
        if not batch_agent:
            raise HTTPException(status_code=503, detail="批量处理服务未初始化")
        
        result = await batch_agent.create_project_structure(
            project_name=request.project_name,
            script_ids=request.script_ids,
            output_dir=request.output_dir
        )
        
        if result.get('success'):
            return JSONResponse(content=result)
        else:
            raise HTTPException(status_code=400, detail=result.get('error', '创建项目失败'))
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建项目失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/scripts")
async def list_scripts(package_name: Optional[str] = None, limit: int = 50):
    """获取脚本列表"""
    try:
        if not controller:
            raise HTTPException(status_code=503, detail="系统未初始化")
        
        # 这里应该从数据库查询脚本列表
        # 简化处理，返回示例数据
        scripts = []
        
        return JSONResponse(content={
            "scripts": scripts,
            "total": len(scripts),
            "limit": limit
        })
        
    except Exception as e:
        logger.error(f"获取脚本列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/scripts/{script_id}")
async def get_script(script_id: str):
    """获取脚本详情"""
    try:
        if not controller:
            raise HTTPException(status_code=503, detail="系统未初始化")
        
        # 从数据库获取脚本详情
        script = controller.db_manager.get_script_by_id(script_id)
        
        if not script:
            raise HTTPException(status_code=404, detail="脚本不存在")
        
        return JSONResponse(content={
            "script_id": script.script_id,
            "script_name": script.script_name,
            "script_description": script.script_description,
            "package_name": script.package_name,
            "status": script.status,
            "created_at": script.created_at.isoformat() if script.created_at else None,
            "script_content": script.script_content
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取脚本详情失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/download-script/{script_id}")
async def download_script(script_id: str):
    """下载脚本文件"""
    try:
        if not controller:
            raise HTTPException(status_code=503, detail="系统未初始化")
        
        # 获取脚本
        script = controller.db_manager.get_script_by_id(script_id)
        if not script:
            raise HTTPException(status_code=404, detail="脚本不存在")
        
        # 生成临时文件
        temp_dir = Path("tmp/downloads")
        temp_dir.mkdir(parents=True, exist_ok=True)
        
        safe_name = "".join(c for c in script.script_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        filename = f"{safe_name}_{script_id}.ts"
        file_path = temp_dir / filename
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(script.script_content)
        
        return FileResponse(
            path=str(file_path),
            filename=filename,
            media_type='application/typescript'
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载脚本失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/statistics")
async def get_statistics():
    """获取系统统计信息"""
    try:
        if not controller:
            raise HTTPException(status_code=503, detail="系统未初始化")
        
        # 获取各种统计信息
        stats = {
            "system_status": await controller.get_system_status(),
            "batch_statistics": batch_agent.get_batch_statistics() if batch_agent else {},
            "timestamp": datetime.now().isoformat()
        }
        
        return JSONResponse(content=stats)
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 错误处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    logger.error(f"未处理的异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "内部服务器错误"}
    )


# 启动函数
def start_api_server(host: str = "0.0.0.0", port: int = 8000):
    """启动API服务器"""
    import uvicorn
    
    logger.info(f"启动API服务器: http://{host}:{port}")
    uvicorn.run(app, host=host, port=port)


if __name__ == "__main__":
    start_api_server()
