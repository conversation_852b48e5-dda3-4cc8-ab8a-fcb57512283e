"""
数据模型模块

包含系统所需的所有数据模型：
- 页面和元素模型
- 用例分析模型
- 脚本生成模型
- 智能体会话模型
"""

from .page_models import UIPage, UIElement, ElementPosition, ElementAttributes
from .case_models import TestCase, CaseAnalysisResult, ActionStep, AssertionStep
from .script_models import GeneratedScript, ScriptVersion, ScriptReview
from .session_models import AgentSession, AgentMessage, WorkflowExecution

__all__ = [
    # 页面模型
    "UIPage", "UIElement", "ElementPosition", "ElementAttributes",
    
    # 用例模型
    "TestCase", "CaseAnalysisResult", "ActionStep", "AssertionStep",
    
    # 脚本模型
    "GeneratedScript", "ScriptVersion", "ScriptReview",
    
    # 会话模型
    "AgentSession", "AgentMessage", "WorkflowExecution"
]
