"""
脚本生成相关数据模型
支持Midscene.js脚本生成、版本管理、质量审查
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, JSON, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum

Base = declarative_base()


class ScriptStatus(Enum):
    """脚本状态枚举"""
    GENERATING = "generating"
    GENERATED = "generated"
    REVIEWING = "reviewing"
    APPROVED = "approved"
    REJECTED = "rejected"
    OPTIMIZED = "optimized"
    DEPLOYED = "deployed"
    FAILED = "failed"


class ReviewStatus(Enum):
    """审查状态枚举"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    APPROVED = "approved"
    REJECTED = "rejected"
    NEEDS_REVISION = "needs_revision"


@dataclass
class ScriptMetrics:
    """脚本质量指标"""
    lines_of_code: int = 0
    complexity_score: float = 0.0
    coverage_score: float = 0.0
    maintainability_score: float = 0.0
    readability_score: float = 0.0


class GeneratedScript(Base):
    """生成的脚本表"""
    __tablename__ = 'generated_scripts'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    script_id = Column(String(100), unique=True, nullable=False, comment='脚本ID')
    case_id = Column(Integer, ForeignKey('test_cases.id'), nullable=False, comment='测试用例ID')
    
    # 脚本基本信息
    script_name = Column(String(300), nullable=False, comment='脚本名称')
    script_description = Column(Text, comment='脚本描述')
    module_name = Column(String(200), comment='模块名称')
    package_name = Column(String(200), comment='应用包名')
    
    # 脚本内容
    script_content = Column(Text, nullable=False, comment='脚本内容')
    script_template_version = Column(String(50), comment='脚本模板版本')
    midscene_version = Column(String(50), comment='Midscene.js版本')
    
    # 脚本状态
    status = Column(String(50), default='generating', comment='脚本状态')
    generation_method = Column(String(100), comment='生成方法: ai_generated, template_based, hybrid')
    
    # 脚本质量指标
    quality_score = Column(Float, comment='质量评分')
    complexity_level = Column(String(50), comment='复杂度级别: low, medium, high')
    estimated_execution_time = Column(Integer, comment='预估执行时间（秒）')
    
    # 脚本元数据
    script_metadata = Column(JSON, comment='脚本元数据')
    generation_config = Column(JSON, comment='生成配置')
    
    # 文件信息
    file_path = Column(String(500), comment='脚本文件路径')
    file_size = Column(Integer, comment='文件大小（字节）')
    
    # 生成信息
    generator_agent = Column(String(100), comment='生成智能体')
    generation_model = Column(String(100), comment='生成模型')
    generation_time = Column(Integer, comment='生成时间（毫秒）')
    
    # 时间戳
    generated_at = Column(DateTime, default=datetime.now, comment='生成时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    # 关联关系
    test_case = relationship("TestCase", back_populates="generated_scripts")
    versions = relationship("ScriptVersion", back_populates="script", cascade="all, delete-orphan")
    reviews = relationship("ScriptReview", back_populates="script", cascade="all, delete-orphan")
    executions = relationship("ScriptExecution", back_populates="script")


class ScriptVersion(Base):
    """脚本版本表"""
    __tablename__ = 'script_versions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    script_id = Column(Integer, ForeignKey('generated_scripts.id'), nullable=False, comment='脚本ID')
    version_number = Column(String(50), nullable=False, comment='版本号')
    
    # 版本信息
    version_description = Column(Text, comment='版本描述')
    change_summary = Column(Text, comment='变更摘要')
    script_content = Column(Text, nullable=False, comment='脚本内容')
    
    # 版本状态
    is_current = Column(Boolean, default=False, comment='是否为当前版本')
    is_stable = Column(Boolean, default=False, comment='是否为稳定版本')
    
    # 变更信息
    changed_by = Column(String(100), comment='变更者')
    change_reason = Column(Text, comment='变更原因')
    
    # 版本元数据
    version_metadata = Column(JSON, comment='版本元数据')
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    
    # 关联关系
    script = relationship("GeneratedScript", back_populates="versions")


class ScriptReview(Base):
    """脚本审查表"""
    __tablename__ = 'script_reviews'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    script_id = Column(Integer, ForeignKey('generated_scripts.id'), nullable=False, comment='脚本ID')
    review_session_id = Column(String(100), comment='审查会话ID')
    
    # 审查基本信息
    review_type = Column(String(50), comment='审查类型: automated, manual, hybrid')
    review_status = Column(String(50), default='pending', comment='审查状态')
    
    # 审查结果
    overall_score = Column(Float, comment='总体评分')
    approval_status = Column(String(50), comment='批准状态: approved, rejected, conditional')
    
    # 审查维度评分
    functionality_score = Column(Float, comment='功能性评分')
    reliability_score = Column(Float, comment='可靠性评分')
    maintainability_score = Column(Float, comment='可维护性评分')
    performance_score = Column(Float, comment='性能评分')
    security_score = Column(Float, comment='安全性评分')
    
    # 审查发现的问题
    critical_issues = Column(JSON, comment='严重问题')
    major_issues = Column(JSON, comment='主要问题')
    minor_issues = Column(JSON, comment='次要问题')
    suggestions = Column(JSON, comment='改进建议')
    
    # 审查详情
    review_comments = Column(Text, comment='审查评论')
    code_quality_notes = Column(Text, comment='代码质量备注')
    test_coverage_notes = Column(Text, comment='测试覆盖率备注')
    
    # 审查者信息
    reviewer_agent = Column(String(100), comment='审查智能体')
    review_model = Column(String(100), comment='审查模型')
    review_duration = Column(Integer, comment='审查时长（毫秒）')
    
    # 时间戳
    reviewed_at = Column(DateTime, default=datetime.now, comment='审查时间')
    
    # 关联关系
    script = relationship("GeneratedScript", back_populates="reviews")


class ScriptExecution(Base):
    """脚本执行记录表"""
    __tablename__ = 'script_executions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    script_id = Column(Integer, ForeignKey('generated_scripts.id'), nullable=False, comment='脚本ID')
    execution_id = Column(String(100), unique=True, nullable=False, comment='执行ID')
    
    # 执行环境
    device_info = Column(JSON, comment='设备信息')
    app_version = Column(String(50), comment='应用版本')
    execution_environment = Column(String(100), comment='执行环境')
    
    # 执行结果
    execution_status = Column(String(50), comment='执行状态: running, passed, failed, timeout, cancelled')
    start_time = Column(DateTime, comment='开始时间')
    end_time = Column(DateTime, comment='结束时间')
    execution_duration = Column(Integer, comment='执行时长（毫秒）')
    
    # 执行详情
    steps_executed = Column(Integer, comment='已执行步骤数')
    steps_total = Column(Integer, comment='总步骤数')
    assertions_passed = Column(Integer, comment='通过的断言数')
    assertions_failed = Column(Integer, comment='失败的断言数')
    
    # 错误信息
    error_message = Column(Text, comment='错误信息')
    failure_step = Column(Integer, comment='失败步骤')
    stack_trace = Column(Text, comment='堆栈跟踪')
    
    # 执行日志
    execution_log = Column(Text, comment='执行日志')
    screenshot_paths = Column(JSON, comment='截图路径列表')
    
    # 执行元数据
    execution_metadata = Column(JSON, comment='执行元数据')
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    
    # 关联关系
    script = relationship("GeneratedScript", back_populates="executions")


# 创建所有表的函数
def create_script_tables(engine):
    """创建脚本相关表"""
    Base.metadata.create_all(engine)
