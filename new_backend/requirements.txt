# 基于Midscene.js+AutoGen框架的多智能体自动化脚本生成系统
# 依赖包列表

# 核心框架
autogen-agentchat>=0.5.7
autogen-core>=0.5.7

# 数据库相关
SQLAlchemy>=2.0.0
PyMySQL>=1.1.0
pymysql>=1.1.0

# Android自动化
uiautomator2>=3.0.0
adbutils>=2.0.0

# 图像处理
Pillow>=10.0.0
opencv-python>=4.8.0

# HTTP客户端
requests>=2.31.0
httpx>=0.25.0

# 异步支持
asyncio-mqtt>=0.13.0
aiofiles>=23.0.0

# 日志记录
loguru>=0.7.0

# 数据处理
pandas>=2.0.0
numpy>=1.24.0
openpyxl>=3.1.0

# JSON和配置
pydantic>=2.0.0
python-dotenv>=1.0.0

# 文本处理
jieba>=0.42.1
regex>=2023.0.0

# 时间处理
python-dateutil>=2.8.0

# 类型提示
typing-extensions>=4.7.0

# 测试框架
pytest>=7.4.0
pytest-asyncio>=0.21.0

# 开发工具
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0

# API客户端
openai>=1.0.0
anthropic>=0.7.0

# 文件处理
pathlib2>=2.3.7
chardet>=5.2.0

# 网络工具
beautifulsoup4>=4.12.0
lxml>=4.9.0

# 加密和安全
cryptography>=41.0.0
hashlib2>=1.0.0

# 系统工具
psutil>=5.9.0
platform>=1.0.8

# 数据序列化
msgpack>=1.0.0
pickle5>=0.0.12

# 缓存
redis>=5.0.0
diskcache>=5.6.0

# 监控和指标
prometheus-client>=0.17.0

# 多进程
multiprocessing-logging>=0.3.4

# XML处理
xmltodict>=0.13.0
lxml>=4.9.0

# 图形界面（可选）
tkinter>=8.6
PyQt5>=5.15.0

# 机器学习（可选）
scikit-learn>=1.3.0
transformers>=4.30.0

# 向量数据库（可选）
chromadb>=0.4.0
faiss-cpu>=1.7.0

# 其他工具
tqdm>=4.66.0
click>=8.1.0
rich>=13.0.0
colorama>=0.4.6
