"""
单脚本生成智能体
基于Midscene.js API生成可执行的TypeScript自动化脚本
"""
import os
import sys
import json
import asyncio
from typing import Dict, Any, Optional, List
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加相对路径导入
sys.path.append(str(Path(__file__).parent.parent))

from config import config
from utils.autogen_client import AutoGenClientManager
from utils.database_manager import DatabaseManager
from models.case_models import TestCase, CaseAnalysisResult
from models.script_models import GeneratedScript


class ScriptGenerationAgent:
    """单脚本生成智能体"""
    
    def __init__(self):
        """初始化脚本生成智能体"""
        self.config = config
        self.agent_name = "script_generation_agent"
        
        # 核心组件
        self.autogen_client = AutoGenClientManager()
        self.db_manager = DatabaseManager()
        
        # 智能体
        self.generation_agent = None
        
        # 初始化智能体
        self._initialize_agent()
    
    def _initialize_agent(self):
        """初始化脚本生成智能体"""
        try:
            logger.info("初始化脚本生成智能体...")
            
            # 创建脚本生成智能体
            self.generation_agent = self.autogen_client.create_text_agent(
                name="script_generation_agent",
                system_message=self._get_script_generation_prompt()
            )
            
            if self.generation_agent:
                logger.info("脚本生成智能体初始化成功")
            else:
                logger.warning("脚本生成智能体初始化失败，将使用降级模式")
            
        except Exception as e:
            logger.error(f"初始化脚本生成智能体失败: {e}")
            self.generation_agent = None
    
    def _get_script_generation_prompt(self) -> str:
        """获取脚本生成提示词"""
        return """你是一个专业的Android自动化测试脚本生成专家，专门使用Midscene.js框架。

任务：根据测试用例分析结果，生成完整的TypeScript自动化测试脚本。

Midscene.js框架API参考：
```typescript
import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';
import "dotenv/config";

const sleep = (ms: number) => new Promise((r) => setTimeout(r, ms));

// 设备连接和初始化
const devices = await getConnectedDevices();
const device = new AndroidDevice(devices[0].udid);

// 创建AI智能体（核心功能）
const agent = new AndroidAgent(device, {
  aiActionContext: 'If any location, permission, user agreement, etc. popup, click agree. If login page pops up, close it.',
});

await device.connect();
await device.launch('应用包名');

// AI驱动的操作（推荐使用）
await agent.aiAction('在搜索框输入 "关键词" 并点击搜索');
await agent.aiAction('点击登录按钮');
await agent.aiAction('输入用户名和密码');
await agent.aiAction('滑动到页面底部');

// AI驱动的等待
await agent.aiWaitFor("页面加载完成，显示搜索结果");
await agent.aiWaitFor("登录成功，显示用户主页");

// AI驱动的查询（获取页面信息）
const items = await agent.aiQuery("{title: string, price: number}[], 获取商品列表信息");
const userInfo = await agent.aiQuery("string, 获取当前用户名");

// AI驱动的断言（验证结果）
await agent.aiAssert("页面显示搜索结果");
await agent.aiAssert("用户已成功登录");
await agent.aiAssert("左侧有分类筛选器");
```

重要特性：
1. 使用AI驱动的操作，用自然语言描述操作意图
2. aiAction: 执行操作（点击、输入、滑动等）
3. aiWaitFor: 等待条件满足
4. aiQuery: 查询页面信息
5. aiAssert: 验证页面状态
6. aiActionContext: 设置全局操作上下文（处理弹窗等）

脚本生成要求：
1. 生成完整可执行的TypeScript代码
2. 包含完整的错误处理和日志记录
3. 使用AI驱动的操作描述，避免硬编码坐标
4. 每个操作和断言都要有清晰的注释
5. 包含测试前置条件和清理逻辑
6. 使用async/await处理异步操作

返回格式：
{
    "script_content": "完整的TypeScript脚本内容",
    "script_metadata": {
        "framework": "midscene.js",
        "language": "typescript",
        "estimated_execution_time": 60,
        "complexity_level": "medium"
    },
    "generation_notes": "脚本生成说明",
    "optimization_suggestions": ["优化建议"]
}

请确保生成的脚本符合Midscene.js最佳实践，并且可以直接运行。"""
    
    async def generate_script(self, test_case: TestCase, 
                            analysis_result: CaseAnalysisResult) -> Dict[str, Any]:
        """
        生成自动化测试脚本
        
        Args:
            test_case: 测试用例对象
            analysis_result: 用例分析结果
            
        Returns:
            Dict[str, Any]: 生成结果
        """
        try:
            logger.info(f"开始生成脚本: {test_case.case_name}")
            
            # 准备生成输入
            generation_input = self._prepare_generation_input(test_case, analysis_result)
            
            # 使用AI智能体生成脚本
            if self.generation_agent and self.autogen_client.is_available():
                generation_result = await self._generate_with_ai(generation_input)
            else:
                # 降级处理
                logger.warning("使用降级模式生成脚本")
                generation_result = self._generate_with_template(test_case, analysis_result)
            
            # 验证和优化脚本
            validated_result = self._validate_script_result(generation_result, test_case)
            
            # 保存生成的脚本
            saved_script = self._save_generated_script(test_case, validated_result)
            
            logger.info(f"脚本生成完成: {test_case.case_name}")
            return {
                'success': True,
                'script_result': validated_result,
                'saved_script_id': saved_script.id if saved_script else None
            }
            
        except Exception as e:
            logger.error(f"生成脚本失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _prepare_generation_input(self, test_case: TestCase, 
                                analysis_result: CaseAnalysisResult) -> str:
        """准备脚本生成输入"""
        try:
            input_parts = [
                "请根据以下测试用例和分析结果生成Midscene.js自动化测试脚本：",
                "",
                "=== 测试用例信息 ===",
                f"用例ID: {test_case.case_id}",
                f"用例名称: {test_case.case_name}",
                f"测试目的: {test_case.test_purpose or '未指定'}",
                f"应用包名: {test_case.package_name}",
                f"模块名称: {test_case.module_name or '未指定'}",
                "",
                "前置条件:",
                test_case.preconditions or "无特殊前置条件",
                "",
                "测试步骤:",
                test_case.test_steps,
                "",
                "预期结果:",
                test_case.expected_results or "无明确预期结果",
                "",
                "=== 分析结果 ===",
            ]
            
            # 添加提取的操作步骤
            if analysis_result.extracted_actions:
                input_parts.extend([
                    "",
                    "提取的操作步骤:",
                ])
                
                for i, action in enumerate(analysis_result.extracted_actions, 1):
                    action_desc = f"{i}. {action.get('action_type', 'unknown')} - {action.get('action_description', '')}"
                    if action.get('target_element'):
                        action_desc += f" (目标: {action['target_element']})"
                    if action.get('input_data'):
                        action_desc += f" (输入: {action['input_data']})"
                    input_parts.append(action_desc)
            
            # 添加提取的断言
            if analysis_result.extracted_assertions:
                input_parts.extend([
                    "",
                    "提取的断言:",
                ])
                
                for i, assertion in enumerate(analysis_result.extracted_assertions, 1):
                    assertion_desc = f"{i}. {assertion.get('assertion_type', 'unknown')} - {assertion.get('assertion_description', '')}"
                    if assertion.get('expected_value'):
                        assertion_desc += f" (期望: {assertion['expected_value']})"
                    input_parts.append(assertion_desc)
            
            # 添加元素映射信息
            if analysis_result.matched_elements:
                input_parts.extend([
                    "",
                    "元素映射信息:",
                ])
                
                for key, mapping in analysis_result.matched_elements.items():
                    mapping_desc = f"- {key}: {mapping.get('target_description', '')} -> {mapping.get('element_type', '')} ({mapping.get('element_text', 'N/A')})"
                    input_parts.append(mapping_desc)
            
            input_parts.extend([
                "",
                "请生成完整的TypeScript脚本，确保：",
                "1. 使用Midscene.js的AI驱动操作",
                "2. 包含完整的错误处理",
                "3. 添加详细的注释说明",
                "4. 覆盖所有测试步骤和验证点",
                "5. 可以直接运行"
            ])
            
            return "\n".join(input_parts)
            
        except Exception as e:
            logger.error(f"准备生成输入失败: {e}")
            return f"生成脚本: {test_case.case_name}"
    
    async def _generate_with_ai(self, generation_input: str) -> Dict[str, Any]:
        """使用AI智能体生成脚本"""
        try:
            # 调用AI智能体
            result = await self.autogen_client.run_text_task(
                agent=self.generation_agent,
                content=generation_input
            )
            
            if result.get('success'):
                # 解析AI返回的结果
                content = result.get('content', '')
                script_data = self._parse_ai_generation_result(content)
                
                if script_data:
                    return script_data
                else:
                    logger.warning("AI脚本生成结果解析失败")
                    return self._create_empty_script_result()
            else:
                logger.warning(f"AI脚本生成失败: {result.get('error')}")
                return self._create_empty_script_result()
                
        except Exception as e:
            logger.error(f"AI脚本生成异常: {e}")
            return self._create_empty_script_result()
    
    def _parse_ai_generation_result(self, content: str) -> Optional[Dict[str, Any]]:
        """解析AI生成结果"""
        try:
            # 尝试提取JSON部分
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            
            if start_idx >= 0 and end_idx > start_idx:
                json_str = content[start_idx:end_idx]
                result = json.loads(json_str)
                
                # 验证必要字段
                if 'script_content' in result:
                    return result
            
            # 如果没有JSON格式，尝试提取代码块
            script_content = self._extract_code_from_content(content)
            if script_content:
                return {
                    'script_content': script_content,
                    'script_metadata': {
                        'framework': 'midscene.js',
                        'language': 'typescript',
                        'estimated_execution_time': 60,
                        'complexity_level': 'medium'
                    },
                    'generation_notes': 'AI生成的脚本',
                    'optimization_suggestions': []
                }
            
            return None
            
        except json.JSONDecodeError as e:
            logger.warning(f"解析AI生成结果JSON失败: {e}")
            # 尝试提取代码内容
            script_content = self._extract_code_from_content(content)
            if script_content:
                return {
                    'script_content': script_content,
                    'script_metadata': {
                        'framework': 'midscene.js',
                        'language': 'typescript'
                    },
                    'generation_notes': 'AI生成的脚本（JSON解析失败）'
                }
            return None
        except Exception as e:
            logger.warning(f"解析AI生成结果异常: {e}")
            return None
    
    def _extract_code_from_content(self, content: str) -> Optional[str]:
        """从内容中提取代码"""
        try:
            # 查找代码块
            import re
            
            # 匹配```typescript或```ts代码块
            ts_pattern = r'```(?:typescript|ts)\n(.*?)\n```'
            ts_match = re.search(ts_pattern, content, re.DOTALL)
            if ts_match:
                return ts_match.group(1).strip()
            
            # 匹配普通代码块
            code_pattern = r'```\n(.*?)\n```'
            code_match = re.search(code_pattern, content, re.DOTALL)
            if code_match:
                code_content = code_match.group(1).strip()
                # 检查是否包含TypeScript/JavaScript关键词
                if any(keyword in code_content for keyword in ['import', 'await', 'async', 'const', 'function']):
                    return code_content
            
            return None

        except Exception as e:
            logger.warning(f"提取代码失败: {e}")
            return None

    def _generate_with_template(self, test_case: TestCase,
                              analysis_result: CaseAnalysisResult) -> Dict[str, Any]:
        """使用模板生成脚本（降级模式）"""
        try:
            logger.info("使用模板生成脚本")

            # 生成脚本内容
            script_content = self._create_script_template(test_case, analysis_result)

            return {
                'script_content': script_content,
                'script_metadata': {
                    'framework': 'midscene.js',
                    'language': 'typescript',
                    'estimated_execution_time': 60,
                    'complexity_level': 'medium',
                    'generation_method': 'template'
                },
                'generation_notes': '基于模板的降级生成',
                'optimization_suggestions': ['建议使用AI模式重新生成以获得更好的脚本质量']
            }

        except Exception as e:
            logger.error(f"模板生成脚本失败: {e}")
            return self._create_empty_script_result()

    def _create_script_template(self, test_case: TestCase,
                              analysis_result: CaseAnalysisResult) -> str:
        """创建脚本模板"""
        try:
            # 脚本模板
            template_parts = [
                "/**",
                f" * 自动化测试脚本: {test_case.case_name}",
                f" * 用例ID: {test_case.case_id}",
                f" * 应用包名: {test_case.package_name}",
                f" * 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                " * 框架: Midscene.js",
                " */",
                "",
                "import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';",
                "import \"dotenv/config\";",
                "",
                "const sleep = (ms: number) => new Promise((r) => setTimeout(r, ms));",
                "",
                "async function main() {",
                "  let device: AndroidDevice | null = null;",
                "  let agent: AndroidAgent | null = null;",
                "",
                "  try {",
                "    console.log('开始执行自动化测试...');",
                "",
                "    // 获取连接的设备",
                "    const devices = await getConnectedDevices();",
                "    if (devices.length === 0) {",
                "      throw new Error('未找到连接的Android设备');",
                "    }",
                "",
                "    // 创建设备实例",
                "    device = new AndroidDevice(devices[0].udid);",
                "    console.log(`连接到设备: ${devices[0].udid}`);",
                "",
                "    // 连接设备",
                "    await device.connect();",
                "",
                "    // 创建AI智能体",
                "    agent = new AndroidAgent(device, {",
                "      aiActionContext: 'If any location, permission, user agreement, etc. popup, click agree. If login page pops up, close it.',",
                "    });",
                "",
                f"    // 启动应用: {test_case.package_name}",
                f"    await device.launch('{test_case.package_name}');",
                "    await sleep(3000); // 等待应用启动",
                "",
                "    // === 前置条件 ===",
                f"    // {test_case.preconditions or '无特殊前置条件'}",
                "",
                "    // === 测试步骤 ===",
            ]

            # 添加操作步骤
            if analysis_result.extracted_actions:
                for i, action in enumerate(analysis_result.extracted_actions, 1):
                    action_type = action.get('action_type', 'click')
                    action_desc = action.get('action_description', '')
                    ai_action_text = action.get('ai_action_text', action_desc)

                    template_parts.extend([
                        f"    // 步骤 {i}: {action_desc}",
                        f"    await agent.aiAction('{ai_action_text}');",
                        "    await sleep(1000); // 等待操作完成",
                        ""
                    ])
            else:
                # 如果没有分析结果，添加基本的操作模板
                template_parts.extend([
                    "    // TODO: 根据测试步骤添加具体操作",
                    "    // 示例操作：",
                    "    // await agent.aiAction('点击登录按钮');",
                    "    // await agent.aiAction('输入用户名和密码');",
                    ""
                ])

            # 添加断言步骤
            template_parts.extend([
                "    // === 验证结果 ===",
            ])

            if analysis_result.extracted_assertions:
                for i, assertion in enumerate(analysis_result.extracted_assertions, 1):
                    assertion_desc = assertion.get('assertion_description', '')
                    ai_assertion_text = assertion.get('ai_assertion_text', assertion_desc)

                    template_parts.extend([
                        f"    // 验证 {i}: {assertion_desc}",
                        f"    await agent.aiAssert('{ai_assertion_text}');",
                        ""
                    ])
            else:
                # 如果没有断言，添加基本的验证模板
                template_parts.extend([
                    "    // TODO: 根据预期结果添加具体验证",
                    "    // 示例验证：",
                    "    // await agent.aiAssert('页面显示登录成功');",
                    ""
                ])

            # 添加结尾部分
            template_parts.extend([
                "    console.log('测试执行完成');",
                "",
                "  } catch (error) {",
                "    console.error('测试执行失败:', error);",
                "    throw error;",
                "  } finally {",
                "    // 清理资源",
                "    if (device) {",
                "      try {",
                "        await device.disconnect();",
                "      } catch (e) {",
                "        console.warn('设备断开连接失败:', e);",
                "      }",
                "    }",
                "  }",
                "}",
                "",
                "// 运行测试",
                "main().catch(console.error);"
            ])

            return "\n".join(template_parts)

        except Exception as e:
            logger.error(f"创建脚本模板失败: {e}")
            return self._get_minimal_script_template(test_case)

    def _get_minimal_script_template(self, test_case: TestCase) -> str:
        """获取最小脚本模板"""
        return f"""/**
 * 自动化测试脚本: {test_case.case_name}
 * 用例ID: {test_case.case_id}
 * 应用包名: {test_case.package_name}
 */

import {{ AndroidAgent, AndroidDevice, getConnectedDevices }} from '@midscene/android';
import "dotenv/config";

async function main() {{
  try {{
    const devices = await getConnectedDevices();
    const device = new AndroidDevice(devices[0].udid);
    await device.connect();

    const agent = new AndroidAgent(device);
    await device.launch('{test_case.package_name}');

    // TODO: 添加测试步骤
    console.log('请根据测试用例添加具体的操作和验证');

  }} catch (error) {{
    console.error('测试失败:', error);
  }}
}}

main().catch(console.error);"""

    def _create_empty_script_result(self) -> Dict[str, Any]:
        """创建空的脚本结果"""
        return {
            'script_content': '// 脚本生成失败\nconsole.log("脚本生成失败，请手动编写");',
            'script_metadata': {
                'framework': 'midscene.js',
                'language': 'typescript',
                'estimated_execution_time': 0,
                'complexity_level': 'unknown'
            },
            'generation_notes': '脚本生成失败',
            'optimization_suggestions': ['请检查输入数据和生成配置']
        }

    def _validate_script_result(self, script_result: Dict[str, Any],
                               test_case: TestCase) -> Dict[str, Any]:
        """验证和优化脚本结果"""
        try:
            # 确保必要字段存在
            if 'script_content' not in script_result:
                script_result['script_content'] = self._get_minimal_script_template(test_case)

            if 'script_metadata' not in script_result:
                script_result['script_metadata'] = {
                    'framework': 'midscene.js',
                    'language': 'typescript'
                }

            # 验证脚本内容
            script_content = script_result['script_content']

            # 检查必要的导入
            if 'import' not in script_content or 'AndroidAgent' not in script_content:
                logger.warning("脚本缺少必要的导入，添加基本导入")
                script_content = self._add_missing_imports(script_content)
                script_result['script_content'] = script_content

            # 检查基本结构
            if 'async function' not in script_content and 'await' in script_content:
                logger.warning("脚本缺少async函数结构")

            # 添加脚本元数据
            metadata = script_result.get('script_metadata', {})
            metadata.update({
                'case_id': test_case.case_id,
                'case_name': test_case.case_name,
                'package_name': test_case.package_name,
                'generation_time': datetime.now().isoformat(),
                'script_length': len(script_content),
                'lines_of_code': len(script_content.split('\n'))
            })
            script_result['script_metadata'] = metadata

            # 计算复杂度
            complexity_score = self._calculate_script_complexity(script_content)
            metadata['complexity_score'] = complexity_score

            return script_result

        except Exception as e:
            logger.error(f"验证脚本结果失败: {e}")
            return script_result

    def _add_missing_imports(self, script_content: str) -> str:
        """添加缺失的导入"""
        try:
            basic_imports = [
                "import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';",
                "import \"dotenv/config\";",
                ""
            ]

            # 如果没有导入，添加到开头
            if 'import' not in script_content:
                return "\n".join(basic_imports) + script_content

            return script_content

        except Exception as e:
            logger.warning(f"添加导入失败: {e}")
            return script_content

    def _calculate_script_complexity(self, script_content: str) -> float:
        """计算脚本复杂度"""
        try:
            # 简单的复杂度计算
            complexity_indicators = {
                'aiAction': 1.0,
                'aiAssert': 1.0,
                'aiWaitFor': 0.5,
                'aiQuery': 1.5,
                'if': 0.5,
                'for': 1.0,
                'while': 1.0,
                'try': 0.5,
                'catch': 0.5
            }

            total_complexity = 0.0
            for indicator, weight in complexity_indicators.items():
                count = script_content.count(indicator)
                total_complexity += count * weight

            return min(total_complexity, 10.0)  # 限制最大复杂度

        except Exception as e:
            logger.warning(f"计算脚本复杂度失败: {e}")
            return 1.0

    def _save_generated_script(self, test_case: TestCase,
                             script_result: Dict[str, Any]) -> Optional[GeneratedScript]:
        """保存生成的脚本到数据库"""
        try:
            # 创建脚本对象
            script = GeneratedScript(
                script_id=f"script_{test_case.case_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                case_id=test_case.id,
                script_name=f"{test_case.module_name or 'unknown'}_{test_case.case_id}_test",
                script_description=f"自动生成的测试脚本: {test_case.case_name}",
                module_name=test_case.module_name,
                package_name=test_case.package_name,
                script_content=script_result.get('script_content', ''),
                script_template_version=self.config.midscene.script_template_version,
                midscene_version="latest",
                status='generated',
                generation_method='ai_generated' if self.generation_agent else 'template_based',
                script_metadata=script_result.get('script_metadata', {}),
                generation_config={
                    'agent_name': self.agent_name,
                    'generation_time': datetime.now().isoformat(),
                    'autogen_available': self.autogen_client.is_available()
                },
                generator_agent=self.agent_name,
                generation_model=self.config.autogen.model_name
            )

            # 保存到数据库
            saved_script = self.db_manager.save_generated_script(script)

            if saved_script:
                logger.info(f"脚本保存成功: {script.script_name}")

            return saved_script

        except Exception as e:
            logger.error(f"保存生成脚本失败: {e}")
            return None

    def get_script_summary(self, script_result: Dict[str, Any]) -> Dict[str, Any]:
        """获取脚本摘要"""
        try:
            script_content = script_result.get('script_content', '')
            metadata = script_result.get('script_metadata', {})

            # 统计脚本特征
            features = {
                'has_ai_actions': 'aiAction' in script_content,
                'has_ai_assertions': 'aiAssert' in script_content,
                'has_ai_waits': 'aiWaitFor' in script_content,
                'has_ai_queries': 'aiQuery' in script_content,
                'has_error_handling': 'try' in script_content and 'catch' in script_content,
                'has_device_management': 'device.connect' in script_content,
                'has_app_launch': 'device.launch' in script_content
            }

            # 计算质量评分
            quality_score = sum(features.values()) / len(features)

            summary = {
                'script_length': len(script_content),
                'lines_of_code': len(script_content.split('\n')),
                'estimated_execution_time': metadata.get('estimated_execution_time', 60),
                'complexity_level': metadata.get('complexity_level', 'medium'),
                'complexity_score': metadata.get('complexity_score', 1.0),
                'quality_score': quality_score,
                'features': features,
                'framework': metadata.get('framework', 'midscene.js'),
                'language': metadata.get('language', 'typescript'),
                'generation_method': metadata.get('generation_method', 'unknown')
            }

            return summary

        except Exception as e:
            logger.error(f"生成脚本摘要失败: {e}")
            return {}

    def cleanup(self):
        """清理资源"""
        try:
            if self.autogen_client:
                self.autogen_client.cleanup()

            logger.info("脚本生成智能体资源清理完成")

        except Exception as e:
            logger.warning(f"清理脚本生成智能体资源失败: {e}")
