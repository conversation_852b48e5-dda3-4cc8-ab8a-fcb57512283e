# 🚀 数据库问题快速解决方案

## 问题描述
```
(pymysql.err.OperationalError) (1049, "Unknown database 'ui_analysis'")
```

这个错误表示数据库 `ui_analysis` 不存在。

## 🔧 快速解决方案

### 方案1: 使用MySQL命令行（推荐）

1. **打开MySQL命令行**：
   ```bash
   mysql -u root -p
   ```

2. **执行以下SQL命令**：
   ```sql
   CREATE DATABASE ui_analysis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

3. **验证数据库创建**：
   ```sql
   SHOW DATABASES;
   USE ui_analysis;
   SELECT DATABASE();
   ```

4. **退出MySQL**：
   ```sql
   EXIT;
   ```

### 方案2: 使用提供的SQL脚本

1. **运行SQL脚本**：
   ```bash
   mysql -u root -p < temp/manual_database_setup.sql
   ```

### 方案3: 修改用户权限（如果使用bcy用户）

如果您想继续使用 `bcy` 用户，需要确保该用户存在并有正确权限：

```sql
-- 以root用户登录MySQL
mysql -u root -p

-- 创建用户（如果不存在）
CREATE USER 'bcy'@'localhost' IDENTIFIED BY 'Bcy&zdd88329';

-- 授予权限
GRANT ALL PRIVILEGES ON *.* TO 'bcy'@'localhost';
FLUSH PRIVILEGES;

-- 创建数据库
CREATE DATABASE ui_analysis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 🧪 验证解决方案

创建数据库后，运行以下命令验证：

```bash
python temp/check_database_setup.py
```

或者直接测试系统：

```bash
python -m backend.main --package com.android.settings --no-ai --max-pages 5
```

## 🔄 替代方案：使用SQLite（临时解决）

如果MySQL设置有困难，可以临时修改为使用SQLite：

1. **修改配置文件** `backend/config.py`：
   ```python
   @property
   def connection_url(self) -> str:
       """获取数据库连接URL"""
       # 临时使用SQLite
       return "sqlite:///temp/ui_analysis.db"
   ```

2. **安装SQLite支持**：
   ```bash
   pip install sqlalchemy
   ```

## 📋 完整的一键解决脚本

创建一个批处理文件 `fix_database.bat`（Windows）：

```batch
@echo off
echo 正在创建数据库...
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS ui_analysis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
echo 数据库创建完成！
echo 测试连接...
python temp/check_database_setup.py
pause
```

或者Shell脚本 `fix_database.sh`（Linux/Mac）：

```bash
#!/bin/bash
echo "正在创建数据库..."
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS ui_analysis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
echo "数据库创建完成！"
echo "测试连接..."
python temp/check_database_setup.py
```

## 🎯 推荐步骤

1. **最简单的解决方案**：
   ```bash
   mysql -u root -p -e "CREATE DATABASE ui_analysis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
   ```

2. **验证修复**：
   ```bash
   python temp/check_database_setup.py
   ```

3. **开始使用系统**：
   ```bash
   python temp/quick_start.py
   ```

## 💡 注意事项

- 确保MySQL服务正在运行
- 使用有创建数据库权限的用户（通常是root）
- 如果密码包含特殊字符，可能需要转义
- 数据库名称 `ui_analysis` 必须与配置文件中的设置一致

完成以上步骤后，系统应该能够正常连接数据库并开始工作！
