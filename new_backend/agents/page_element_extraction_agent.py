"""
单页面元素提取智能体
基于UIautomator2和多模态大模型的页面元素分析和语义增强
"""
import os
import sys
import json
import asyncio
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加相对路径导入
sys.path.append(str(Path(__file__).parent.parent))

from config import config
from utils.uiautomator2_helper import UIAutomator2Helper
from utils.autogen_client import AutoGenClientManager
from new_backend.models.page_models import UIPage, UIElement, AppInfo, AnalysisSession


class PageElementExtractionAgent:
    """单页面元素提取智能体"""
    
    def __init__(self):
        """初始化页面元素提取智能体"""
        self.config = config
        self.ui_helper = None
        self.autogen_client = AutoGenClientManager()
        
        # 智能体配置
        self.agent_name = "page_element_extraction_agent"
        self.multimodal_agent = None
        
        # 初始化智能体
        self._initialize_agent()
    
    def _initialize_agent(self):
        """初始化多模态智能体"""
        try:
            logger.info("初始化页面元素提取智能体...")
            
            # 创建多模态智能体用于语义增强
            self.multimodal_agent = self.autogen_client.create_multimodal_agent(
                name="multimodal_semantic_enhancer",
                system_message=self._get_semantic_enhancement_prompt()
            )
            
            logger.info("页面元素提取智能体初始化成功")
            
        except Exception as e:
            logger.error(f"初始化页面元素提取智能体失败: {e}")
            self.multimodal_agent = None
    
    def _get_semantic_enhancement_prompt(self) -> str:
        """获取语义增强提示词"""
        return """你是一个专业的Android UI元素语义分析专家。

任务：分析UI元素的语义和功能，结合页面截图和元素信息，返回JSON格式结果。

分析要点：
1. 结合页面截图理解元素的视觉上下文
2. 分析元素类型、位置、文本内容和属性
3. 推断用户交互意图和功能分类
4. 提供准确的中文语义描述

返回格式：
{
    "semantic_description": "元素的语义描述（中文）",
    "expected_action": "预期的用户操作",
    "element_category": "元素类别",
    "confidence_score": 0.95,
    "visual_context": "基于截图的视觉上下文描述",
    "interaction_hints": "交互提示"
}

请确保返回有效的JSON格式，并充分利用视觉信息提高分析准确性。"""
    
    def connect_device(self, device_id: Optional[str] = None) -> bool:
        """连接Android设备"""
        try:
            logger.info(f"连接Android设备: {device_id or 'default'}")
            
            self.ui_helper = UIAutomator2Helper(device_id)
            
            if self.ui_helper.is_connected():
                device_info = self.ui_helper.get_device_info()
                logger.info(f"设备连接成功: {device_info.get('product_name', 'Unknown')}")
                return True
            else:
                logger.error("设备连接失败")
                return False
                
        except Exception as e:
            logger.error(f"连接设备异常: {e}")
            return False
    
    def extract_page_elements(self, package_name: str, 
                            enable_ai_enhancement: bool = True) -> Tuple[Optional[UIPage], List[UIElement]]:
        """
        提取当前页面的所有元素
        
        Args:
            package_name: 应用包名
            enable_ai_enhancement: 是否启用AI语义增强
            
        Returns:
            Tuple[UIPage, List[UIElement]]: 页面对象和元素列表
        """
        try:
            if not self.ui_helper or not self.ui_helper.is_connected():
                logger.error("设备未连接")
                return None, []
            
            logger.info(f"开始提取页面元素: {package_name}")
            
            # 使用UIautomator2提取页面元素
            page, elements, screenshot_path, xml_path = self.ui_helper.extract_page_elements(package_name)
            
            if not page or not elements:
                logger.warning("页面元素提取失败")
                return None, []
            
            # 设置页面与元素的关联
            for element in elements:
                element.page = page
            
            page.elements = elements
            
            # AI语义增强
            if enable_ai_enhancement and self.multimodal_agent:
                enhanced_count = asyncio.run(
                    self._enhance_elements_semantics(page, elements, screenshot_path)
                )
                logger.info(f"AI语义增强完成: {enhanced_count}/{len(elements)} 个元素")
                page.is_enhanced = True
                page.enhanced_at = datetime.now()
            
            logger.info(f"页面元素提取完成: {len(elements)} 个元素")
            return page, elements
            
        except Exception as e:
            logger.error(f"提取页面元素失败: {e}")
            return None, []
    
    async def _enhance_elements_semantics(self, page: UIPage, elements: List[UIElement], 
                                        screenshot_path: Optional[str]) -> int:
        """使用AI增强元素语义信息"""
        try:
            if not screenshot_path or not self.multimodal_agent:
                logger.warning("无法进行AI语义增强：缺少截图或智能体")
                return 0
            
            enhanced_count = 0
            
            # 只对可操作元素进行语义增强
            actionable_elements = [elem for elem in elements if elem.is_actionable]
            logger.info(f"需要语义增强的可操作元素: {len(actionable_elements)}")
            
            # 准备页面上下文
            page_context = {
                'page_name': page.page_name,
                'activity_name': page.activity_name,
                'package_name': page.app.package_name if page.app else 'unknown'
            }
            
            # 批量处理元素（避免过多API调用）
            batch_size = self.config.agents.element_extraction_batch_size
            
            for i in range(0, len(actionable_elements), batch_size):
                batch_elements = actionable_elements[i:i + batch_size]
                
                for element in batch_elements:
                    try:
                        # 使用多模态智能体分析元素
                        semantic_result = await self._analyze_element_with_ai(
                            element, screenshot_path, page_context
                        )
                        
                        if semantic_result:
                            # 更新元素语义信息
                            self._update_element_semantics(element, semantic_result)
                            enhanced_count += 1
                        
                    except Exception as e:
                        logger.warning(f"元素语义增强失败: {e}")
                        continue
                
                # 避免API调用过于频繁
                await asyncio.sleep(0.5)
            
            return enhanced_count
            
        except Exception as e:
            logger.error(f"AI语义增强失败: {e}")
            return 0
    
    async def _analyze_element_with_ai(self, element: UIElement, screenshot_path: str, 
                                     page_context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """使用AI分析单个元素的语义"""
        try:
            # 创建元素分析提示
            analysis_prompt = self._create_element_analysis_prompt(element, page_context)
            
            # 调用多模态智能体
            result = await self.autogen_client.run_multimodal_task(
                agent=self.multimodal_agent,
                text_content=analysis_prompt,
                image_path=screenshot_path
            )
            
            if result and result.get('success'):
                # 解析AI返回的JSON结果
                semantic_data = self._parse_ai_semantic_result(result.get('content', ''))
                return semantic_data
            else:
                logger.warning(f"AI分析失败: {result}")
                return None
                
        except Exception as e:
            logger.warning(f"AI元素分析异常: {e}")
            return None
    
    def _create_element_analysis_prompt(self, element: UIElement, page_context: Dict[str, Any]) -> str:
        """创建元素分析提示词"""
        prompt_parts = [
            "请分析以下UI元素，结合页面截图提供准确的语义分析：",
            "",
            "元素信息：",
            f"- 类型: {element.element_type}",
            f"- 文本: {element.text or '无'}",
            f"- 资源ID: {element.resource_id or '无'}",
            f"- 描述: {element.content_desc or '无'}",
        ]
        
        # 添加位置信息
        if element.position:
            pos = element.position
            prompt_parts.append(f"- 位置: ({pos.get('left', 0)}, {pos.get('top', 0)}) - ({pos.get('right', 0)}, {pos.get('bottom', 0)})")
        
        # 添加属性信息
        if element.attributes:
            attrs = element.attributes
            prompt_parts.extend([
                f"- 可点击: {'是' if attrs.get('clickable', False) else '否'}",
                f"- 可编辑: {'是' if attrs.get('editable', False) else '否'}",
                f"- 可滚动: {'是' if attrs.get('scrollable', False) else '否'}"
            ])
        
        # 添加页面上下文
        if page_context:
            prompt_parts.extend([
                "",
                "页面上下文：",
                f"- 应用: {page_context.get('package_name', '未知')}",
                f"- 页面: {page_context.get('page_name', '未知')}",
                f"- Activity: {page_context.get('activity_name', '未知')}"
            ])
        
        prompt_parts.extend([
            "",
            "请基于截图中的视觉信息和元素属性，返回JSON格式的详细分析结果。",
            "特别关注元素在页面中的视觉位置、周围环境和用户交互意图。"
        ])
        
        return "\n".join(prompt_parts)
    
    def _parse_ai_semantic_result(self, content: str) -> Optional[Dict[str, Any]]:
        """解析AI返回的语义分析结果"""
        try:
            # 尝试解析JSON
            if isinstance(content, str):
                # 提取JSON部分
                start_idx = content.find('{')
                end_idx = content.rfind('}') + 1
                if start_idx >= 0 and end_idx > start_idx:
                    json_str = content[start_idx:end_idx]
                    result = json.loads(json_str)
                    
                    # 验证必要字段
                    required_fields = ['semantic_description', 'expected_action', 'element_category', 'confidence_score']
                    if all(field in result for field in required_fields):
                        return result
            
            return None
            
        except json.JSONDecodeError as e:
            logger.warning(f"解析AI语义结果JSON失败: {e}")
            return None
        except Exception as e:
            logger.warning(f"解析AI语义结果异常: {e}")
            return None
    
    def _update_element_semantics(self, element: UIElement, semantic_result: Dict[str, Any]):
        """更新元素的语义信息"""
        try:
            element.semantic_description = semantic_result.get('semantic_description', '')
            element.expected_action = semantic_result.get('expected_action', '')
            element.element_category = semantic_result.get('element_category', '')
            element.confidence_score = semantic_result.get('confidence_score', 0.0)
            element.visual_context = semantic_result.get('visual_context', '')
            element.interaction_hints = semantic_result.get('interaction_hints', '')
            element.is_enhanced = True
            element.enhanced_at = datetime.now()
            
        except Exception as e:
            logger.warning(f"更新元素语义信息失败: {e}")
    
    def get_page_summary(self, page: UIPage, elements: List[UIElement]) -> Dict[str, Any]:
        """获取页面摘要信息"""
        try:
            actionable_elements = [elem for elem in elements if elem.is_actionable]
            enhanced_elements = [elem for elem in elements if elem.is_enhanced]
            
            # 统计元素类型
            element_types = {}
            for elem in elements:
                elem_type = elem.element_type
                element_types[elem_type] = element_types.get(elem_type, 0) + 1
            
            # 统计元素类别（AI增强后的）
            element_categories = {}
            for elem in enhanced_elements:
                category = elem.element_category
                if category:
                    element_categories[category] = element_categories.get(category, 0) + 1
            
            summary = {
                'page_info': {
                    'page_id': page.page_id,
                    'page_name': page.page_name,
                    'activity_name': page.activity_name,
                    'is_enhanced': page.is_enhanced
                },
                'element_statistics': {
                    'total_elements': len(elements),
                    'actionable_elements': len(actionable_elements),
                    'enhanced_elements': len(enhanced_elements),
                    'enhancement_rate': len(enhanced_elements) / len(actionable_elements) if actionable_elements else 0
                },
                'element_types': element_types,
                'element_categories': element_categories,
                'extraction_time': page.discovered_at.isoformat() if page.discovered_at else None,
                'enhancement_time': page.enhanced_at.isoformat() if page.enhanced_at else None
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"生成页面摘要失败: {e}")
            return {}
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.ui_helper:
                # UIautomator2会自动管理连接，无需手动清理
                pass
            
            if self.autogen_client:
                # AutoGen客户端清理
                pass
                
            logger.info("页面元素提取智能体资源清理完成")
            
        except Exception as e:
            logger.warning(f"清理资源失败: {e}")
