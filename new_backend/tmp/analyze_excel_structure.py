#!/usr/bin/env python3
"""
分析Excel用例文件结构
读取真实的Excel文件，分析字段结构，生成字段映射
"""
import os
import sys
import pandas as pd
from pathlib import Path
from loguru import logger

# 添加相对路径导入
sys.path.append(str(Path(__file__).parent.parent))


def analyze_excel_structure(excel_file_path: str):
    """分析Excel文件结构"""
    try:
        logger.info(f"分析Excel文件: {excel_file_path}")
        
        if not Path(excel_file_path).exists():
            logger.error(f"Excel文件不存在: {excel_file_path}")
            return
        
        # 读取Excel文件
        df = pd.read_excel(excel_file_path, engine='openpyxl')
        
        print("="*80)
        print("Excel文件结构分析报告")
        print("="*80)
        
        # 基本信息
        print(f"文件路径: {excel_file_path}")
        print(f"总行数: {len(df)}")
        print(f"总列数: {len(df.columns)}")
        print()
        
        # 列名信息
        print("列名列表:")
        print("-"*40)
        for i, col in enumerate(df.columns, 1):
            print(f"{i:2d}. {col}")
        print()
        
        # 数据类型信息
        print("数据类型信息:")
        print("-"*40)
        for col in df.columns:
            dtype = df[col].dtype
            non_null_count = df[col].count()
            null_count = len(df) - non_null_count
            print(f"{col:<30} | {str(dtype):<15} | 非空:{non_null_count:4d} | 空值:{null_count:4d}")
        print()
        
        # 示例数据（前3行）
        print("示例数据（前3行）:")
        print("-"*40)
        for i in range(min(3, len(df))):
            print(f"\n第{i+1}行数据:")
            for col in df.columns:
                value = df.iloc[i][col]
                if pd.isna(value):
                    value_str = "NULL"
                else:
                    value_str = str(value)[:50]  # 限制显示长度
                    if len(str(value)) > 50:
                        value_str += "..."
                print(f"  {col:<30}: {value_str}")
        
        # 字段映射建议
        print("\n" + "="*80)
        print("字段映射建议")
        print("="*80)
        
        # 定义标准字段映射
        standard_fields = {
            'case_id': ['tcid', 'id', 'case_id', 'caseid', '用例ID', '用例编号'],
            'case_name': ['case_name', 'casename', 'name', '用例名称', '测试用例名称'],
            'test_purpose': ['purpose', 'test_purpose', '测试目的', '用例目的'],
            'package_name': ['package_name', 'package', 'app_package', '包名', '应用包名'],
            'module_name': ['module', 'module_name', 'group', '模块', '模块名称'],
            'preconditions': ['precondition', 'preconditions', 'pre_condition', '前置条件', '预置条件'],
            'test_steps': ['steps', 'test_steps', 'teststeps', '测试步骤', '操作步骤'],
            'expected_results': ['expect_result', 'expected_result', 'expected_results', '预期结果', '期望结果'],
            'priority': ['priority', 'level', '优先级', '重要程度'],
            'test_type': ['type', 'test_type', 'case_type', '测试类型', '用例类型']
        }
        
        # 生成字段映射
        field_mapping = {}
        for standard_field, possible_names in standard_fields.items():
            matched_column = None
            for col in df.columns:
                col_lower = col.lower().strip()
                for possible_name in possible_names:
                    if possible_name.lower() in col_lower or col_lower in possible_name.lower():
                        matched_column = col
                        break
                if matched_column:
                    break
            
            if matched_column:
                field_mapping[standard_field] = matched_column
                print(f"{standard_field:<20} -> {matched_column}")
            else:
                print(f"{standard_field:<20} -> 未找到匹配列")
        
        # 生成代码模板
        print("\n" + "="*80)
        print("数据模型代码模板")
        print("="*80)
        
        print("@dataclass")
        print("class RealTestCase:")
        print('    """真实Excel测试用例数据模型"""')
        
        for col in df.columns:
            # 生成安全的字段名
            safe_field_name = col.replace(' ', '_').replace('*', '').replace('（', '').replace('）', '').replace('(', '').replace(')', '').replace('【', '').replace('】', '')
            safe_field_name = ''.join(c for c in safe_field_name if c.isalnum() or c == '_').lower()
            
            # 确定数据类型
            if df[col].dtype == 'object':
                field_type = "str"
                default_value = '""'
            elif df[col].dtype in ['int64', 'int32']:
                field_type = "int"
                default_value = "0"
            elif df[col].dtype in ['float64', 'float32']:
                field_type = "float"
                default_value = "0.0"
            else:
                field_type = "str"
                default_value = '""'
            
            print(f"    {safe_field_name}: {field_type} = {default_value}  # {col}")
        
        # 生成字段映射字典
        print("\n" + "="*80)
        print("字段映射字典")
        print("="*80)
        
        print("EXCEL_FIELD_MAPPING = {")
        for col in df.columns:
            safe_field_name = col.replace(' ', '_').replace('*', '').replace('（', '').replace('）', '').replace('(', '').replace(')', '').replace('【', '').replace('】', '')
            safe_field_name = ''.join(c for c in safe_field_name if c.isalnum() or c == '_').lower()
            print(f"    '{safe_field_name}': '{col}',")
        print("}")
        
        return field_mapping
        
    except Exception as e:
        logger.error(f"分析Excel文件失败: {e}")
        return None


def main():
    """主函数"""
    try:
        # 设置日志
        logger.remove()
        logger.add(sys.stdout, level="INFO", format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | {message}")
        
        # Excel文件路径
        excel_file_path = Path(__file__).parent.parent / "data" / "case" / "case.xlsx"
        
        # 分析Excel结构
        field_mapping = analyze_excel_structure(str(excel_file_path))
        
        if field_mapping:
            print(f"\n分析完成！建议的字段映射: {field_mapping}")
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")


if __name__ == "__main__":
    main()
