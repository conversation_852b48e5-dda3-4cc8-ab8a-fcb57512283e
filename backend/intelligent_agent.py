"""
智能语义增强模块
基于AutoGen框架实现UI元素语义分析和增强
"""
import sys
import asyncio
import json
import base64
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
from loguru import logger

# 添加example目录到路径
sys.path.append(str(Path(__file__).parent.parent / "example"))

# AutoGen相关导入
AUTOGEN_AVAILABLE = False

def _try_import_autogen():
    """尝试导入AutoGen模块"""
    global AUTOGEN_AVAILABLE

    if AUTOGEN_AVAILABLE:
        return True

    try:
        from autogen_agentchat.agents import AssistantAgent
        from autogen_agentchat.messages import TextMessage
        from autogen_agentchat.ui import Console
        from autogen_ext.models.openai import OpenAIChatCompletionClient
        # from example.llms import uitars_model_client
        from example.llms import qwenvl_model_client

        AUTOGEN_AVAILABLE = True
        logger.info("AutoGen模块导入成功")
        return True

    except ImportError as e:
        logger.warning(f"AutoGen依赖不可用: {e}")
        logger.warning("将使用降级模式，禁用AI语义增强功能")
        AUTOGEN_AVAILABLE = False
        return False
    except Exception as e:
        logger.error(f"AutoGen导入异常: {e}")
        AUTOGEN_AVAILABLE = False
        return False

from .config import config
from .models.element_model import UIElement
from .models.page_model import UIPage


class UISemanticAgent:
    """UI语义分析智能体 - 基于AutoGen框架"""

    def __init__(self):
        """初始化UI语义分析智能体"""
        self.ai_config = config.ai
        self.model_client = None
        self.agent = None
        self.fallback_enabled = self.ai_config.fallback_enabled

        # 初始化智能体
        self._initialize_agent()

    def _initialize_agent(self):
        """初始化AutoGen智能体"""
        if not _try_import_autogen():
            logger.warning("AutoGen不可用，跳过智能体初始化")
            self.model_client = None
            self.agent = None
            return

        try:
            # 导入AutoGen组件
            from autogen_agentchat.agents import AssistantAgent
            from example.llms import uitars_model_client

            logger.info("初始化UI语义分析智能体...")

            # 创建模型客户端
            self.model_client = uitars_model_client()

            # 创建智能体 - 按照AutoGen标准方式
            self.agent = AssistantAgent(
                name="ui_semantic_analyzer",
                model_client=self.model_client,
                system_message=self._get_system_prompt()
            )

            logger.info("UI语义分析智能体初始化成功")

        except Exception as e:
            logger.error(f"初始化智能体失败: {e}")
            self.model_client = None
            self.agent = None
            if not self.fallback_enabled:
                raise
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """你是一个专业的Android UI元素分析专家。

任务：分析UI元素的语义和功能，返回JSON格式结果。

分析要点：
- 元素类型和属性
- 文本内容和描述
- 用户交互意图
- 功能分类

返回格式：
{
    "semantic_description": "元素的语义描述（中文）",
    "expected_action": "预期的用户操作",
    "element_category": "元素类别",
    "confidence_score": 0.95
}

请确保返回有效的JSON格式。"""
    
    async def _create_element_analysis_tool(self, element: UIElement, page_context: Dict[str, Any] = None) -> str:
        """创建元素分析工具函数"""
        async def analyze_ui_element(element_info: str) -> str:
            """分析UI元素的语义信息"""
            return f"分析UI元素: {element_info}"

        return analyze_ui_element
    
    def _create_element_analysis_prompt(self, element: UIElement, page_context: Dict[str, Any] = None) -> str:
        """创建元素分析提示词"""
        prompt_parts = [
            "分析以下UI元素：",
            f"类型: {element.element_type}",
            f"文本: {element.text or '无'}",
            f"资源ID: {element.resource_id or '无'}",
            f"描述: {element.content_desc or '无'}",
            f"可点击: {'是' if element.attributes and element.attributes.clickable else '否'}",
            f"可编辑: {'是' if element.attributes and element.attributes.editable else '否'}",
        ]

        if page_context:
            prompt_parts.append(f"应用: {page_context.get('package_name', '未知')}")

        prompt_parts.append("请返回JSON格式的分析结果。")
        return "\n".join(prompt_parts)
    
    async def analyze_element_semantic(self,
                                     element: UIElement,
                                     screenshot_path: Optional[str] = None,
                                     page_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        分析单个元素的语义信息 - 使用AutoGen标准方式

        Args:
            element: UI元素
            screenshot_path: 页面截图路径（暂不使用）
            page_context: 页面上下文信息

        Returns:
            Dict[str, Any]: 语义分析结果
        """
        try:
            if not _try_import_autogen() or not self.agent:
                logger.debug("AutoGen不可用，使用降级处理")
                return self._fallback_element_analysis(element)

            logger.debug(f"分析元素语义: {element.element_type} - {element.text or element.resource_id}")

            # 创建分析任务
            task = self._create_element_analysis_prompt(element, page_context)

            # 使用AutoGen标准方式运行任务
            result = await self.agent.run(task=task)

            # 解析响应
            if result and result.messages:
                content = result.messages[-1].content
                return self._parse_analysis_result(content, element)
            else:
                logger.warning("智能体返回空响应")
                return self._fallback_element_analysis(element)

        except Exception as e:
            logger.error(f"元素语义分析失败: {e}")
            if self.fallback_enabled:
                return self._fallback_element_analysis(element)
            else:
                raise

    def _parse_analysis_result(self, content: str, element: UIElement) -> Dict[str, Any]:
        """解析分析结果"""
        try:
            # 尝试解析JSON
            if isinstance(content, str):
                # 提取JSON部分
                start_idx = content.find('{')
                end_idx = content.rfind('}') + 1
                if start_idx >= 0 and end_idx > start_idx:
                    json_str = content[start_idx:end_idx]
                    result = json.loads(json_str)

                    # 验证必要字段
                    required_fields = ['semantic_description', 'expected_action', 'element_category', 'confidence_score']
                    if all(field in result for field in required_fields):
                        logger.debug(f"元素语义分析成功: {result['semantic_description']}")
                        return result

        except json.JSONDecodeError as e:
            logger.warning(f"解析JSON失败: {e}")
        except Exception as e:
            logger.warning(f"解析结果异常: {e}")

        # 解析失败，使用降级处理
        logger.warning("AI分析结果格式不正确，使用降级处理")
        return self._fallback_element_analysis(element)
    
    def _fallback_element_analysis(self, element: UIElement) -> Dict[str, Any]:
        """
        降级处理：基于规则的元素分析
        
        Args:
            element: UI元素
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        try:
            logger.debug(f"使用规则分析元素: {element.element_type}")
            
            # 基于元素类型和属性进行分析
            element_type = element.element_type.lower()
            text = element.text or ""
            resource_id = element.resource_id or ""
            content_desc = element.content_desc or ""
            
            # 默认结果
            result = {
                "semantic_description": "",
                "expected_action": "",
                "element_category": "",
                "confidence_score": 0.7
            }
            
            # 按钮类元素
            if 'button' in element_type or (element.attributes and element.attributes.clickable):
                result["element_category"] = "按钮"
                if text:
                    result["semantic_description"] = f"'{text}' 按钮"
                    result["expected_action"] = f"点击{text}"
                else:
                    result["semantic_description"] = "可点击按钮"
                    result["expected_action"] = "点击操作"
            
            # 文本输入框
            elif 'edittext' in element_type or (element.attributes and element.attributes.editable):
                result["element_category"] = "输入框"
                if text:
                    result["semantic_description"] = f"包含'{text}'的输入框"
                    result["expected_action"] = "编辑文本内容"
                else:
                    result["semantic_description"] = "文本输入框"
                    result["expected_action"] = "输入文本"
            
            # 文本标签
            elif 'textview' in element_type:
                result["element_category"] = "文本标签"
                if text:
                    result["semantic_description"] = f"显示'{text}'的文本标签"
                    result["expected_action"] = "查看信息"
                else:
                    result["semantic_description"] = "文本显示区域"
                    result["expected_action"] = "查看内容"
            
            # 图片元素
            elif 'imageview' in element_type or 'image' in element_type:
                result["element_category"] = "图片"
                if content_desc:
                    result["semantic_description"] = f"图片: {content_desc}"
                else:
                    result["semantic_description"] = "图片元素"
                result["expected_action"] = "查看图片"
            
            # 列表相关
            elif 'listview' in element_type or 'recyclerview' in element_type:
                result["element_category"] = "列表"
                result["semantic_description"] = "列表容器"
                result["expected_action"] = "浏览列表项"
            
            # 滚动容器
            elif element.attributes and element.attributes.scrollable:
                result["element_category"] = "滚动区域"
                result["semantic_description"] = "可滚动的内容区域"
                result["expected_action"] = "滚动查看更多内容"
            
            # 其他可点击元素
            elif element.attributes and element.attributes.clickable:
                result["element_category"] = "可点击元素"
                if text:
                    result["semantic_description"] = f"可点击的'{text}'"
                    result["expected_action"] = f"点击{text}"
                else:
                    result["semantic_description"] = "可点击元素"
                    result["expected_action"] = "点击操作"
            
            # 默认情况
            else:
                result["element_category"] = "界面元素"
                if text:
                    result["semantic_description"] = f"显示'{text}'的界面元素"
                else:
                    result["semantic_description"] = "界面元素"
                result["expected_action"] = "查看或交互"
            
            # 根据资源ID优化描述
            if resource_id:
                if 'search' in resource_id.lower():
                    result["element_category"] = "搜索"
                    result["expected_action"] = "搜索功能"
                elif 'login' in resource_id.lower():
                    result["element_category"] = "登录"
                    result["expected_action"] = "登录操作"
                elif 'menu' in resource_id.lower():
                    result["element_category"] = "菜单"
                    result["expected_action"] = "打开菜单"
            
            return result
            
        except Exception as e:
            logger.error(f"降级分析失败: {e}")
            return {
                "semantic_description": "未知元素",
                "expected_action": "未知操作",
                "element_category": "未知",
                "confidence_score": 0.1
            }
    
    async def enhance_page_elements(self,
                                  page: UIPage,
                                  screenshot_path: Optional[str] = None) -> Tuple[int, int]:
        """
        增强页面中所有元素的语义信息 - 使用AutoGen标准方式

        Args:
            page: UI页面对象
            screenshot_path: 页面截图路径（暂不使用）

        Returns:
            Tuple[int, int]: (成功增强的元素数量, 总元素数量)
        """
        try:
            logger.info(f"开始增强页面元素语义: {page.page_name}")

            # 如果AutoGen不可用，使用降级处理
            if not _try_import_autogen() or not self.agent:
                logger.info("AutoGen不可用，使用降级语义分析")
                return self._fallback_enhance_page_elements(page)

            # 准备页面上下文
            page_context = {
                'page_name': page.page_name,
                'activity_name': page.activity_name,
                'package_name': page.package_name
            }

            enhanced_count = 0
            total_elements = len(page.elements)

            # 只增强可操作元素
            actionable_elements = [elem for elem in page.elements if elem.is_actionable()]
            logger.info(f"需要增强的可操作元素: {len(actionable_elements)}")

            # 批量处理元素
            for i, element in enumerate(actionable_elements):
                try:
                    logger.debug(f"增强元素 {i+1}/{len(actionable_elements)}: {element.element_type}")

                    # 使用AutoGen分析元素语义
                    semantic_result = await self.analyze_element_semantic(element, None, page_context)

                    # 更新元素信息
                    self._update_element_with_semantic_result(element, semantic_result)
                    enhanced_count += 1

                except Exception as e:
                    logger.warning(f"增强元素失败: {e}")
                    continue

            logger.info(f"页面元素语义增强完成: {enhanced_count}/{len(actionable_elements)} 个可操作元素")
            return enhanced_count, total_elements

        except Exception as e:
            logger.error(f"页面元素语义增强失败: {e}")
            return 0, len(page.elements) if page.elements else 0

    def _update_element_with_semantic_result(self, element: UIElement, semantic_result: Dict[str, Any]):
        """更新元素的语义信息"""
        element.semantic_description = semantic_result.get('semantic_description', '')
        element.expected_action = semantic_result.get('expected_action', '')
        element.element_category = semantic_result.get('element_category', '')
        element.confidence_score = semantic_result.get('confidence_score', 0.0)
        element.updated_at = datetime.now()

    def _fallback_enhance_page_elements(self, page) -> Tuple[int, int]:
        """
        降级处理：批量增强页面元素

        Args:
            page: UI页面对象

        Returns:
            Tuple[int, int]: (成功增强的元素数量, 总元素数量)
        """
        try:
            logger.info(f"使用降级模式增强页面元素: {page.page_name}")

            enhanced_count = 0
            total_elements = len(page.elements)

            # 只增强可操作元素
            actionable_elements = [elem for elem in page.elements if elem.is_actionable()]

            for element in actionable_elements:
                try:
                    # 使用降级分析
                    semantic_result = self._fallback_element_analysis(element)

                    # 使用统一的更新方法
                    self._update_element_with_semantic_result(element, semantic_result)
                    enhanced_count += 1

                except Exception as e:
                    logger.warning(f"降级增强元素失败: {e}")
                    continue

            logger.info(f"降级模式元素增强完成: {enhanced_count}/{len(actionable_elements)} 个可操作元素")
            return enhanced_count, total_elements

        except Exception as e:
            logger.error(f"降级模式页面元素增强失败: {e}")
            return 0, len(page.elements) if page.elements else 0
