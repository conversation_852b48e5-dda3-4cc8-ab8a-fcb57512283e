"""
全量页面遍历器
实现BFS算法的页面遍历，构建页面关系图谱
"""
import time
import asyncio
from collections import deque
from typing import List, Dict, Any, Optional, Set, Tuple
from datetime import datetime
from loguru import logger

try:
    import uiautomator2 as u2
except ImportError as e:
    logger.error(f"导入uiautomator2失败: {e}")
    raise

from .config import config
from .models.page_model import UIPage, PageNavigation, PageAnalysisResult
from .models.element_model import UIElement
from .models.app_model import AnalysisSession, AnalysisStatus
from .page_analyzer import PageAnalyzer
from .intelligent_agent import UISemanticAgent
from .app_controller import AndroidAppController


class PageCrawler:
    """页面遍历器"""
    
    def __init__(self, device_id: Optional[str] = None):
        """
        初始化页面遍历器
        
        Args:
            device_id: 设备ID，为None时使用默认设备
        """
        self.device_id = device_id or config.device.device_id
        self.device = None
        
        # 初始化组件
        self.page_analyzer = PageAnalyzer(device_id)
        self.semantic_agent = UISemanticAgent()
        self.app_controller = AndroidAppController(device_id)
        
        # 遍历配置
        self.crawler_config = config.crawler
        self.navigation_rules = config.get_navigation_rules()
        
        # 遍历状态
        self.visited_pages: Set[str] = set()
        self.page_queue: deque = deque()
        self.page_graph: Dict[str, UIPage] = {}
        self.navigation_history: List[PageNavigation] = []
        
        # 当前会话
        self.current_session: Optional[AnalysisSession] = None
        
    def connect_device(self) -> bool:
        """
        连接设备
        
        Returns:
            bool: 连接是否成功
        """
        try:
            logger.info(f"页面遍历器连接设备: {self.device_id or 'default'}")
            
            # 连接页面分析器
            if not self.page_analyzer.connect_device():
                return False
            
            # 获取设备引用
            self.device = self.page_analyzer.device
            
            logger.info("页面遍历器设备连接成功")
            return True
            
        except Exception as e:
            logger.error(f"页面遍历器连接设备失败: {e}")
            return False
    
    def _is_page_similar(self, page1: UIPage, page2: UIPage) -> bool:
        """
        判断两个页面是否相似（用于去重）
        
        Args:
            page1: 页面1
            page2: 页面2
            
        Returns:
            bool: 是否相似
        """
        try:
            # 使用页面模型的相似度判断方法
            return page1.is_similar_to(page2, similarity_threshold=0.8)
        except Exception as e:
            logger.warning(f"页面相似度判断失败: {e}")
            return False
    
    def _should_navigate_to_element(self, element: UIElement, current_page: UIPage) -> bool:
        """
        判断是否应该点击某个元素进行导航
        
        Args:
            element: UI元素
            current_page: 当前页面
            
        Returns:
            bool: 是否应该导航
        """
        try:
            # 检查元素是否可操作
            if not element.is_actionable():
                return False
            
            # 排除返回按钮
            if self.navigation_rules.get("exclude_back_button", True):
                if (element.text and any(keyword in element.text.lower() 
                                       for keyword in ['back', '返回', '上一页']) or
                    element.content_desc and any(keyword in element.content_desc.lower() 
                                               for keyword in ['back', '返回', '上一页']) or
                    element.resource_id and 'back' in element.resource_id.lower()):
                    return False
            
            # 排除系统UI
            if self.navigation_rules.get("exclude_system_ui", True):
                if element.package_name and element.package_name.startswith("com.android.systemui"):
                    return False
            
            # 只允许特定操作类型
            allowed_actions = self.navigation_rules.get("allowed_actions", ["click"])
            if "click" not in allowed_actions and element.attributes and element.attributes.clickable:
                return False
            
            # 检查元素是否有意义（有文本或资源ID）
            if not (element.text or element.resource_id or element.content_desc):
                return False
            
            return True
            
        except Exception as e:
            logger.warning(f"导航判断失败: {e}")
            return False
    
    def _click_element(self, element: UIElement) -> bool:
        """
        点击元素
        
        Args:
            element: 要点击的元素
            
        Returns:
            bool: 点击是否成功
        """
        try:
            if not self.device:
                logger.error("设备未连接")
                return False
            
            logger.debug(f"点击元素: {element.text or element.resource_id or element.element_type}")
            
            # 优先使用资源ID定位
            if element.resource_id:
                try:
                    self.device(resourceId=element.resource_id).click()
                    return True
                except Exception:
                    pass
            
            # 使用文本定位
            if element.text:
                try:
                    self.device(text=element.text).click()
                    return True
                except Exception:
                    pass
            
            # 使用内容描述定位
            if element.content_desc:
                try:
                    self.device(description=element.content_desc).click()
                    return True
                except Exception:
                    pass
            
            # 使用坐标点击
            if element.position:
                try:
                    self.device.click(element.position.center_x, element.position.center_y)
                    return True
                except Exception:
                    pass
            
            logger.warning(f"无法点击元素: {element.element_id}")
            return False
            
        except Exception as e:
            logger.error(f"点击元素失败: {e}")
            return False
    
    def _wait_for_page_change(self, original_page_id: str, timeout: int = 5) -> bool:
        """
        等待页面变化

        Args:
            original_page_id: 原始页面ID
            timeout: 超时时间（秒）

        Returns:
            bool: 页面是否发生变化
        """
        try:
            start_time = time.time()
            original_activity = None

            # 获取原始Activity作为基准
            if original_page_id in self.page_graph:
                original_activity = self.page_graph[original_page_id].activity_name

            while time.time() - start_time < timeout:
                # 获取当前Activity
                current_activity = self.app_controller.get_current_activity()

                if current_activity:
                    # 如果有原始Activity，比较是否变化
                    if original_activity:
                        if current_activity != original_activity:
                            logger.debug(f"检测到Activity变化: {original_activity} -> {current_activity}")
                            time.sleep(1)  # 给页面一些加载时间
                            return True
                    else:
                        # 没有原始Activity信息，等待页面稳定
                        time.sleep(1)
                        # 再次检查Activity是否稳定
                        stable_activity = self.app_controller.get_current_activity()
                        if stable_activity == current_activity:
                            logger.debug("页面Activity已稳定")
                            return True

                time.sleep(0.5)

            logger.debug("页面变化检测超时")
            return False

        except Exception as e:
            logger.warning(f"等待页面变化失败: {e}")
            return False
    
    async def _analyze_and_enhance_page(self, enable_ai: bool = True) -> Optional[PageAnalysisResult]:
        """
        分析并增强当前页面
        
        Args:
            enable_ai: 是否启用AI增强
            
        Returns:
            Optional[PageAnalysisResult]: 分析结果
        """
        try:
            # 分析当前页面
            result = self.page_analyzer.analyze_current_page(
                capture_screenshot=self.crawler_config.screenshot_enabled,
                save_xml=self.crawler_config.xml_save_enabled,
                wait_stable=True
            )
            
            if not result.success:
                logger.error(f"页面分析失败: {result.error_message}")
                return result
            
            # AI语义增强
            if enable_ai and self.crawler_config.enable_ai_enhancement:
                try:
                    enhanced_count, total_count = await self.semantic_agent.enhance_page_elements(
                        result.page, result.page.screenshot_path
                    )
                    result.ai_enhancement_applied = True
                    result.ai_enhanced_elements = enhanced_count
                    
                    logger.info(f"AI语义增强完成: {enhanced_count}/{total_count}")
                    
                except Exception as e:
                    logger.warning(f"AI语义增强失败: {e}")
                    result.ai_enhancement_applied = False
            
            return result
            
        except Exception as e:
            logger.error(f"页面分析和增强失败: {e}")
            return None
    
    def _add_navigation(self, source_page_id: str, target_page_id: str, 
                       via_element_id: str, action_type: str = "click"):
        """
        添加导航关系
        
        Args:
            source_page_id: 源页面ID
            target_page_id: 目标页面ID
            via_element_id: 触发元素ID
            action_type: 操作类型
        """
        try:
            navigation = PageNavigation(
                source_page_id=source_page_id,
                target_page_id=target_page_id,
                via_element_id=via_element_id,
                action_type=action_type
            )
            
            self.navigation_history.append(navigation)
            
            # 更新页面导航关系
            if source_page_id in self.page_graph:
                source_page = self.page_graph[source_page_id]
                source_page.add_navigation(navigation)
            
            if target_page_id in self.page_graph:
                target_page = self.page_graph[target_page_id]
                target_page.parent_pages.add(source_page_id)
            
            logger.debug(f"添加导航关系: {source_page_id} -> {target_page_id}")
            
        except Exception as e:
            logger.warning(f"添加导航关系失败: {e}")
    
    async def crawl_app_pages(self, 
                            package_name: str, 
                            activity_name: Optional[str] = None,
                            max_depth: Optional[int] = None,
                            max_pages: Optional[int] = None,
                            enable_ai: bool = True) -> AnalysisSession:
        """
        遍历应用的所有页面
        
        Args:
            package_name: 应用包名
            activity_name: 启动Activity名称
            max_depth: 最大遍历深度
            max_pages: 最大页面数量
            enable_ai: 是否启用AI增强
            
        Returns:
            AnalysisSession: 分析会话结果
        """
        try:
            logger.info(f"开始遍历应用页面: {package_name}")
            
            # 获取应用信息
            app_info = self.app_controller.get_app_info(package_name)
            if not app_info:
                raise Exception(f"无法获取应用信息: {package_name}")
            
            # 创建分析会话
            session = AnalysisSession(
                session_id="",  # 将自动生成
                app_info=app_info,
                device_id=self.device_id,
                max_depth=max_depth or self.crawler_config.max_depth,
                max_pages=max_pages or self.crawler_config.max_pages,
                enable_ai_enhancement=enable_ai
            )
            
            session.start_analysis()
            self.current_session = session
            
            # 启动应用
            if not self.app_controller.launch_app(package_name, activity_name):
                raise Exception(f"启动应用失败: {package_name}")

            # 如果使用UI2启动成功，则跳过额外的等待验证
            if hasattr(self.app_controller, 'ui2_device') and self.app_controller.ui2_device:
                logger.info("UI2启动成功，跳过额外验证")
            else:
                # 等待应用启动（仅在ADB启动时需要）
                if not self.app_controller.wait_for_app_launch(package_name):
                    raise Exception(f"应用启动超时: {package_name}")
            
            # 初始化遍历状态
            self.visited_pages.clear()
            self.page_queue.clear()
            self.page_graph.clear()
            self.navigation_history.clear()
            
            current_depth = 0
            
            # 分析首页
            logger.info("分析应用首页...")
            home_result = await self._analyze_and_enhance_page(enable_ai)
            if not home_result or not home_result.success:
                raise Exception("首页分析失败")
            
            home_page = home_result.page
            session.add_page(home_page)
            session.add_analysis_result(home_result)
            self.page_graph[home_page.page_id] = home_page
            self.visited_pages.add(home_page.page_id)
            
            # 将首页的可操作元素加入队列
            actionable_elements = home_page.get_actionable_elements()
            for element in actionable_elements:
                if self._should_navigate_to_element(element, home_page):
                    self.page_queue.append((home_page.page_id, element, current_depth + 1))
            
            logger.info(f"首页分析完成，发现 {len(actionable_elements)} 个可操作元素")
            
            # BFS遍历
            while (self.page_queue and 
                   len(self.visited_pages) < session.max_pages and
                   current_depth < session.max_depth):
                
                try:
                    # 从队列取出下一个要访问的元素
                    source_page_id, element, depth = self.page_queue.popleft()
                    current_depth = depth
                    
                    logger.info(f"遍历深度 {depth}: 点击元素 {element.text or element.resource_id}")
                    
                    # 点击元素
                    if not self._click_element(element):
                        logger.warning(f"点击元素失败，跳过: {element.element_id}")
                        continue
                    
                    # 等待页面变化
                    wait_time = self.navigation_rules.get("wait_after_click", 2.0)
                    time.sleep(wait_time)
                    
                    # 分析新页面
                    page_result = await self._analyze_and_enhance_page(enable_ai)
                    if not page_result or not page_result.success:
                        logger.warning("新页面分析失败，继续下一个")
                        continue
                    
                    new_page = page_result.page
                    
                    # 检查是否是新页面
                    is_new_page = True
                    for existing_page in self.page_graph.values():
                        if self._is_page_similar(new_page, existing_page):
                            logger.debug(f"发现重复页面: {new_page.page_id} 类似于 {existing_page.page_id}")
                            is_new_page = False
                            new_page = existing_page
                            break
                    
                    if is_new_page:
                        # 添加新页面
                        session.add_page(new_page)
                        session.add_analysis_result(page_result)
                        self.page_graph[new_page.page_id] = new_page
                        self.visited_pages.add(new_page.page_id)
                        
                        logger.info(f"发现新页面: {new_page.page_name} (ID: {new_page.page_id})")
                        
                        # 将新页面的可操作元素加入队列
                        if depth < session.max_depth:
                            actionable_elements = new_page.get_actionable_elements()
                            for elem in actionable_elements:
                                if self._should_navigate_to_element(elem, new_page):
                                    self.page_queue.append((new_page.page_id, elem, depth + 1))
                    
                    # 添加导航关系
                    self._add_navigation(source_page_id, new_page.page_id, element.element_id)
                    
                    # 更新会话状态
                    session.current_page_id = new_page.page_id
                    
                except Exception as e:
                    logger.warning(f"处理页面遍历失败: {e}")
                    continue
            
            # 完成遍历
            session.complete_analysis()
            
            logger.info(f"页面遍历完成!")
            logger.info(f"总计发现 {len(session.pages)} 个页面")
            logger.info(f"成功分析 {len(session.pages_analyzed)} 个页面")
            logger.info(f"分析失败 {len(session.pages_failed)} 个页面")
            logger.info(f"总计导航关系 {len(self.navigation_history)} 个")
            
            return session
            
        except Exception as e:
            logger.error(f"页面遍历失败: {e}")
            if self.current_session:
                self.current_session.fail_analysis(str(e))
                return self.current_session
            else:
                # 创建失败的会话
                from .models.app_model import AppInfo
                failed_session = AnalysisSession(
                    session_id="",
                    app_info=AppInfo(package_name=package_name),
                    device_id=self.device_id
                )
                failed_session.fail_analysis(str(e))
                return failed_session
