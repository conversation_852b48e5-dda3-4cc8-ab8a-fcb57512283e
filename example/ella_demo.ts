import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';
import "dotenv/config"; // read environment variables from .env file

const sleep = (ms: number) => new Promise((r) => setTimeout(r, ms));

async function testEllaBluetoothCommand() {
  try {
    console.log('🚀 开始测试Ella应用的蓝牙指令功能...');

    // 获取连接的设备
    const devices = await getConnectedDevices();
    if (devices.length === 0) {
      throw new Error('未找到连接的Android设备');
    }

    const device = new AndroidDevice(devices[0].udid);
    console.log(`📱 连接到设备: ${devices[0].udid}`);

    // 初始化AI智能体
    const agent = new AndroidAgent(device, {
      aiActionContext: 'If any location, permission, user agreement, etc. popup, click agree. If login page pops up, close it.',
    });

    await device.connect();
    console.log('✅ 设备连接成功');

    // 步骤1: 打开Ella应用
    console.log('📱 步骤1: 启动Ella应用...');
    await device.launch('com.transsion.aivoiceassistant');
    await sleep(5000); // 等待应用完全启动
    console.log('✅ Ella应用启动成功');

    // 步骤2: 在输入框输入"open Bluetooth"发送指令
    console.log('💬 步骤2: 输入蓝牙指令...');
    // 点击输入框
    await agent.aiTap('页面底部的输入框');
    // 发送指令
    await agent.aiInput('open Bluetooth','页面底部的输入框');
    await sleep(2000); // 等待指令发送
    console.log('✅ 蓝牙指令已发送');

    // 等待Ella处理指令并返回结果
    console.log('⏳ 等待Ella处理指令...');
    await agent.aiWaitFor('Ella显示了对蓝牙指令的响应结果', { timeoutMs: 15000 });

    // 步骤3: 检查Ella返回结果是否正确
    console.log('🔍 步骤3: 检查Ella的返回结果...');

    // 获取Ella的响应内容
    const ellaResponse = await agent.aiQuery(
      'string, 获取Ella对"open Bluetooth"指令的响应文本内容'
    );
    console.log('📝 Ella的响应:', ellaResponse);

    // 验证响应是否正确
    const isResponseCorrect = await agent.aiQuery(
      'boolean, 判断Ella的响应是否正确处理了蓝牙相关的指令，比如是否提到了蓝牙设置、蓝牙开启等相关内容'
    );

    if (isResponseCorrect) {
      console.log('✅ 验证通过: Ella正确响应了蓝牙指令');
      await agent.aiAssert('Ella正确响应了蓝牙相关指令');
    } else {
      console.log('❌ 验证失败: Ella的响应可能不正确');
      throw new Error('Ella的蓝牙指令响应不正确');
    }

    console.log('🎉 测试完成: Ella蓝牙指令功能测试通过');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    throw error;
  } finally {
    // 清理资源
    console.log('🧹 清理测试环境...');
  }
}

// 运行测试
testEllaBluetoothCommand().catch(console.error);