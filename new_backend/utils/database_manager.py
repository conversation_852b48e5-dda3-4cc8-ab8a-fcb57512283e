"""
数据库管理器
提供数据库连接、CRUD操作、事务管理等功能
"""
import os
import sys
from typing import Dict, Any, Optional, List
from pathlib import Path
from datetime import datetime
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError
from loguru import logger

# 添加相对路径导入
sys.path.append(str(Path(__file__).parent.parent))

from config import config
from models.page_models import Base as PageBase, AppInfo, AnalysisSession, UIPage, UIElement, PageNavigation
from models.case_models import Base as CaseBase, TestCase, CaseAnalysisResult, ElementMapping, CaseValidation
from models.script_models import Base as ScriptBase, GeneratedScript, ScriptVersion, ScriptReview, ScriptExecution
from models.session_models import Base as SessionBase, AgentSession, AgentMessage, WorkflowExecution, WorkflowStepExecution


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        """初始化数据库管理器"""
        self.config = config.database
        self.engine = None
        self.SessionLocal = None
        
        # 初始化数据库连接
        self._initialize_database()
    
    def _initialize_database(self) -> bool:
        """初始化数据库连接"""
        try:
            logger.info("初始化数据库连接...")
            
            # 创建数据库引擎
            self.engine = create_engine(
                self.config.connection_url,
                echo=False,  # 设置为True可以看到SQL语句
                pool_pre_ping=True,
                pool_recycle=3600
            )
            
            # 创建会话工厂
            self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
            
            # 测试连接
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            
            logger.info("数据库连接成功")
            
            # 创建所有表
            self._create_all_tables()
            
            return True
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            return False
    
    def _create_all_tables(self):
        """创建所有数据表"""
        try:
            logger.info("创建数据库表...")
            
            # 创建所有表
            PageBase.metadata.create_all(bind=self.engine)
            CaseBase.metadata.create_all(bind=self.engine)
            ScriptBase.metadata.create_all(bind=self.engine)
            SessionBase.metadata.create_all(bind=self.engine)
            
            logger.info("数据库表创建完成")
            
        except Exception as e:
            logger.error(f"创建数据库表失败: {e}")
            raise
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        return self.SessionLocal()
    
    def close_session(self, session: Session):
        """关闭数据库会话"""
        try:
            session.close()
        except Exception as e:
            logger.warning(f"关闭数据库会话失败: {e}")
    
    # ==================== 应用信息相关操作 ====================
    
    def save_app_info(self, app_info: AppInfo) -> Optional[AppInfo]:
        """保存应用信息"""
        session = self.get_session()
        try:
            session.add(app_info)
            session.commit()
            session.refresh(app_info)
            logger.debug(f"保存应用信息成功: {app_info.package_name}")
            return app_info
            
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"保存应用信息失败: {e}")
            return None
        finally:
            self.close_session(session)
    
    def get_app_by_package_name(self, package_name: str) -> Optional[AppInfo]:
        """根据包名获取应用信息"""
        session = self.get_session()
        try:
            app_info = session.query(AppInfo).filter(AppInfo.package_name == package_name).first()
            return app_info
            
        except SQLAlchemyError as e:
            logger.error(f"获取应用信息失败: {e}")
            return None
        finally:
            self.close_session(session)
    
    def update_app_info(self, app_info: AppInfo) -> bool:
        """更新应用信息"""
        session = self.get_session()
        try:
            session.merge(app_info)
            session.commit()
            logger.debug(f"更新应用信息成功: {app_info.package_name}")
            return True
            
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"更新应用信息失败: {e}")
            return False
        finally:
            self.close_session(session)
    
    # ==================== 分析会话相关操作 ====================
    
    def save_analysis_session(self, session_obj: AnalysisSession) -> Optional[AnalysisSession]:
        """保存分析会话"""
        session = self.get_session()
        try:
            session.add(session_obj)
            session.commit()
            session.refresh(session_obj)
            logger.debug(f"保存分析会话成功: {session_obj.session_id}")
            return session_obj
            
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"保存分析会话失败: {e}")
            return None
        finally:
            self.close_session(session)
    
    def update_analysis_session(self, session_obj: AnalysisSession) -> bool:
        """更新分析会话"""
        session = self.get_session()
        try:
            session.merge(session_obj)
            session.commit()
            logger.debug(f"更新分析会话成功: {session_obj.session_id}")
            return True
            
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"更新分析会话失败: {e}")
            return False
        finally:
            self.close_session(session)
    
    # ==================== 页面和元素相关操作 ====================
    
    def save_page_with_elements(self, page: UIPage, elements: List[UIElement]) -> Optional[UIPage]:
        """保存页面及其元素"""
        session = self.get_session()
        try:
            # 保存页面
            session.add(page)
            session.flush()  # 获取页面ID
            
            # 设置元素的页面ID并保存
            for element in elements:
                element.page_id = page.id
                session.add(element)
            
            session.commit()
            session.refresh(page)
            
            logger.debug(f"保存页面及元素成功: {page.page_name} ({len(elements)} 个元素)")
            return page
            
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"保存页面及元素失败: {e}")
            return None
        finally:
            self.close_session(session)
    
    def get_page_by_id(self, page_id: str) -> Optional[UIPage]:
        """根据页面ID获取页面"""
        session = self.get_session()
        try:
            page = session.query(UIPage).filter(UIPage.page_id == page_id).first()
            return page
            
        except SQLAlchemyError as e:
            logger.error(f"获取页面失败: {e}")
            return None
        finally:
            self.close_session(session)
    
    def get_elements_by_page_id(self, page_id: int) -> List[UIElement]:
        """根据页面ID获取元素列表"""
        session = self.get_session()
        try:
            elements = session.query(UIElement).filter(UIElement.page_id == page_id).all()
            return elements
            
        except SQLAlchemyError as e:
            logger.error(f"获取页面元素失败: {e}")
            return []
        finally:
            self.close_session(session)
    
    def save_page_navigation(self, navigation: PageNavigation) -> Optional[PageNavigation]:
        """保存页面导航关系"""
        session = self.get_session()
        try:
            session.add(navigation)
            session.commit()
            session.refresh(navigation)
            logger.debug("保存页面导航关系成功")
            return navigation
            
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"保存页面导航关系失败: {e}")
            return None
        finally:
            self.close_session(session)
    
    # ==================== 测试用例相关操作 ====================
    
    def save_test_case(self, test_case: TestCase) -> Optional[TestCase]:
        """保存测试用例"""
        session = self.get_session()
        try:
            session.add(test_case)
            session.commit()
            session.refresh(test_case)
            logger.debug(f"保存测试用例成功: {test_case.case_name}")
            return test_case
            
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"保存测试用例失败: {e}")
            return None
        finally:
            self.close_session(session)
    
    def get_test_case_by_id(self, case_id: str) -> Optional[TestCase]:
        """根据用例ID获取测试用例"""
        session = self.get_session()
        try:
            test_case = session.query(TestCase).filter(TestCase.case_id == case_id).first()
            return test_case
            
        except SQLAlchemyError as e:
            logger.error(f"获取测试用例失败: {e}")
            return None
        finally:
            self.close_session(session)
    
    def save_case_analysis_result(self, analysis_result: CaseAnalysisResult) -> Optional[CaseAnalysisResult]:
        """保存用例分析结果"""
        session = self.get_session()
        try:
            session.add(analysis_result)
            session.commit()
            session.refresh(analysis_result)
            logger.debug("保存用例分析结果成功")
            return analysis_result
            
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"保存用例分析结果失败: {e}")
            return None
        finally:
            self.close_session(session)
    
    # ==================== 脚本相关操作 ====================
    
    def save_generated_script(self, script: GeneratedScript) -> Optional[GeneratedScript]:
        """保存生成的脚本"""
        session = self.get_session()
        try:
            session.add(script)
            session.commit()
            session.refresh(script)
            logger.debug(f"保存生成脚本成功: {script.script_name}")
            return script
            
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"保存生成脚本失败: {e}")
            return None
        finally:
            self.close_session(session)
    
    def save_script_review(self, review: ScriptReview) -> Optional[ScriptReview]:
        """保存脚本审查结果"""
        session = self.get_session()
        try:
            session.add(review)
            session.commit()
            session.refresh(review)
            logger.debug("保存脚本审查结果成功")
            return review
            
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"保存脚本审查结果失败: {e}")
            return None
        finally:
            self.close_session(session)
    
    # ==================== 智能体会话相关操作 ====================
    
    def save_agent_session(self, agent_session: AgentSession) -> Optional[AgentSession]:
        """保存智能体会话"""
        session = self.get_session()
        try:
            session.add(agent_session)
            session.commit()
            session.refresh(agent_session)
            logger.debug(f"保存智能体会话成功: {agent_session.session_id}")
            return agent_session
            
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"保存智能体会话失败: {e}")
            return None
        finally:
            self.close_session(session)
    
    def save_agent_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """保存智能体消息"""
        session = self.get_session()
        try:
            session.add(message)
            session.commit()
            session.refresh(message)
            return message
            
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"保存智能体消息失败: {e}")
            return None
        finally:
            self.close_session(session)
    
    # ==================== 查询和统计操作 ====================
    
    def get_app_statistics(self, package_name: str) -> Dict[str, Any]:
        """获取应用统计信息"""
        session = self.get_session()
        try:
            app_info = session.query(AppInfo).filter(AppInfo.package_name == package_name).first()
            if not app_info:
                return {}
            
            # 统计页面数量
            page_count = session.query(UIPage).filter(UIPage.app_id == app_info.id).count()
            
            # 统计元素数量
            element_count = session.query(UIElement).join(UIPage).filter(UIPage.app_id == app_info.id).count()
            
            # 统计可操作元素数量
            actionable_count = session.query(UIElement).join(UIPage).filter(
                UIPage.app_id == app_info.id,
                UIElement.is_actionable == True
            ).count()
            
            return {
                'app_name': app_info.app_name,
                'package_name': app_info.package_name,
                'total_pages': page_count,
                'total_elements': element_count,
                'actionable_elements': actionable_count,
                'analysis_status': app_info.analysis_status,
                'created_at': app_info.created_at.isoformat() if app_info.created_at else None,
                'updated_at': app_info.updated_at.isoformat() if app_info.updated_at else None
            }
            
        except SQLAlchemyError as e:
            logger.error(f"获取应用统计信息失败: {e}")
            return {}
        finally:
            self.close_session(session)
    
    def cleanup(self):
        """清理数据库连接"""
        try:
            if self.engine:
                self.engine.dispose()
            logger.info("数据库连接清理完成")
            
        except Exception as e:
            logger.warning(f"清理数据库连接失败: {e}")
